\input texinfo @c -*- texinfo -*-
@documentencoding UTF-8

@settitle NUT

@titlepage
@center @titlefont{NUT}
@end titlepage

@top

@contents

@chapter Description
NUT is a low overhead generic container format. It stores audio, video,
subtitle and user-defined streams in a simple, yet efficient, way.

It was created by a group of FFmpeg and MPlayer developers in 2003
and was finalized in 2008.

The official nut specification is at svn://svn.mplayerhq.hu/nut
In case of any differences between this text and the official specification,
the official specification shall prevail.

@chapter Modes
NUT has some variants signaled by using the flags field in its main header.

@multitable @columnfractions .4 .4
@item BROADCAST   @tab Extend the syncpoint to report the sender wallclock
@item PIPE        @tab Omit completely the syncpoint
@end multitable

@section BROADCAST

The BROADCAST variant provides a secondary time reference to facilitate
detecting endpoint latency and network delays.
It assumes all the endpoint clocks are synchronized.
To be used in real-time scenarios.

@section PIPE

The PIPE variant assumes NUT is used as non-seekable intermediate container,
by not using syncpoint removes unneeded overhead and reduces the overall
memory usage.

@chapter Container-specific codec tags

@section Generic raw YUVA formats

Since many exotic planar YUVA pixel formats are not considered by
the AVI/QuickTime FourCC lists, the following scheme is adopted for
representing them.

The first two bytes can contain the values:
Y1 = only Y
Y2 = Y+A
Y3 = YUV
Y4 = YUVA

The third byte represents the width and height chroma subsampling
values for the UV planes, that is the amount to shift the luma
width/height right to find the chroma width/height.

The fourth byte is the number of bits used (8, 16, ...).

If the order of bytes is inverted, that means that each component has
to be read big-endian.

@section Raw Audio

@multitable @columnfractions .4 .4
@item ALAW  @tab A-LAW
@item ULAW  @tab MU-LAW
@item P<type><interleaving><bits> @tab little-endian PCM
@item <bits><interleaving><type>P @tab big-endian PCM
@end multitable

<type> is S for signed integer, U for unsigned integer, F for IEEE float
<interleaving> is D for default, P is for planar.
<bits> is 8/16/24/32

@example
PFD[32]   would for example be signed 32 bit little-endian IEEE float
@end example

@section Subtitles

@multitable @columnfractions .4 .4
@item UTF8   @tab Raw UTF-8
@item SSA[0] @tab SubStation Alpha
@item DVDS   @tab DVD subtitles
@item DVBS   @tab DVB subtitles
@end multitable

@section Raw Data

@multitable @columnfractions .4 .4
@item UTF8   @tab Raw UTF-8
@end multitable

@section Codecs

@multitable @columnfractions .4 .4
@item 3IV1 @tab non-compliant MPEG-4 generated by old 3ivx
@item ASV1 @tab Asus Video
@item ASV2 @tab Asus Video 2
@item CVID @tab Cinepak
@item CYUV @tab Creative YUV
@item DIVX @tab non-compliant MPEG-4 generated by old DivX
@item DUCK @tab Truemotion 1
@item FFV1 @tab FFmpeg video 1
@item FFVH @tab FFmpeg Huffyuv
@item H261 @tab ITU H.261
@item H262 @tab ITU H.262
@item H263 @tab ITU H.263
@item H264 @tab ITU H.264
@item HFYU @tab Huffyuv
@item I263 @tab Intel H.263
@item IV31 @tab Indeo 3.1
@item IV32 @tab Indeo 3.2
@item IV50 @tab Indeo 5.0
@item LJPG @tab ITU JPEG (lossless)
@item MJLS @tab ITU JPEG-LS
@item MJPG @tab ITU JPEG
@item MPG4 @tab MS MPEG-4v1 (not ISO MPEG-4)
@item MP42 @tab MS MPEG-4v2
@item MP43 @tab MS MPEG-4v3
@item MP4V @tab ISO MPEG-4 Part 2 Video (from old encoders)
@item mpg1 @tab ISO MPEG-1 Video
@item mpg2 @tab ISO MPEG-2 Video
@item MRLE @tab MS RLE
@item MSVC @tab MS Video 1
@item RT21 @tab Indeo 2.1
@item RV10 @tab RealVideo 1.0
@item RV20 @tab RealVideo 2.0
@item RV30 @tab RealVideo 3.0
@item RV40 @tab RealVideo 4.0
@item SNOW @tab FFmpeg Snow
@item SVQ1 @tab Sorenson Video 1
@item SVQ3 @tab Sorenson Video 3
@item theo @tab Xiph Theora
@item TM20 @tab Truemotion 2.0
@item UMP4 @tab non-compliant MPEG-4 generated by UB Video MPEG-4
@item VCR1 @tab ATI VCR1
@item VP30 @tab VP 3.0
@item VP31 @tab VP 3.1
@item VP50 @tab VP 5.0
@item VP60 @tab VP 6.0
@item VP61 @tab VP 6.1
@item VP62 @tab VP 6.2
@item VP70 @tab VP 7.0
@item WMV1 @tab MS WMV7
@item WMV2 @tab MS WMV8
@item WMV3 @tab MS WMV9
@item WV1F @tab non-compliant MPEG-4 generated by ?
@item WVC1 @tab VC-1
@item XVID @tab non-compliant MPEG-4 generated by old Xvid
@item XVIX @tab non-compliant MPEG-4 generated by old Xvid with interlacing bug
@end multitable

