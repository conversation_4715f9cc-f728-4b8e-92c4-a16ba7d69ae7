[Script Info]
; Script generated by FFmpeg/Lavc
ScriptType: v4.00+
PlayResX: 384
PlayResY: 288
ScaledBorderAndShadow: yes
YCbCr Matrix: None

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,Monospace,16,&Hffffff,&Hffffff,&H0,&H0,0,0,0,0,100,100,0,0,3,1,0,2,10,10,10,1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
Dialogue: 0,0:00:00.97,9:59:59.99,Default,,0,0,0,,{\an7}{\pos(38,44)}(
Dialogue: 0,0:00:01.17,9:59:59.99,De<PERSON>ult,,0,0,0,,{\an7}{\pos(38,44)}({\i1} inaudibl
Dialogue: 0,0:00:01.37,9:59:59.99,<PERSON><PERSON><PERSON>,,0,0,0,,{\an7}{\pos(38,44)}({\i1} inaudible radio chat
Dialogue: 0,0:00:01.57,9:59:59.99,Default,,0,0,0,,{\an7}{\pos(38,44)}({\i1} inaudible radio chatter{\i0} )
Dialogue: 0,0:00:03.10,9:59:59.99,Default,,0,0,0,,{\an7}{\pos(38,28)}({\i1} inaudible radio chatter{\i0} )\N{\an7}{\pos(38,44)}>>
Dialogue: 0,0:00:03.30,9:59:59.99,Default,,0,0,0,,{\an7}{\pos(38,28)}({\i1} inaudible radio chatter{\i0} )\N{\an7}{\pos(38,44)}>> Safety rema
Dialogue: 0,0:00:03.50,9:59:59.99,Default,,0,0,0,,{\an7}{\pos(38,28)}({\i1} inaudible radio chatter{\i0} )\N{\an7}{\pos(38,44)}>> Safety remains our numb
Dialogue: 0,0:00:03.70,9:59:59.99,Default,,0,0,0,,{\an7}{\pos(38,28)}({\i1} inaudible radio chatter{\i0} )\N{\an7}{\pos(38,44)}>> Safety remains our number one
