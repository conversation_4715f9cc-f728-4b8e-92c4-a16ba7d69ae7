aiohappyeyeballs==2.4.0
aiohttp==3.10.5
aiosignal==1.3.1
annotated-types==0.7.0
anthropic==0.39.0
anyio==4.4.0
APScheduler==3.11.0
asgiref==3.8.1
asttokens==2.4.1
async-timeout==4.0.3
asyncio==3.4.3
attrs==24.2.0
autogen==0.3.0
backoff==2.2.1
bcrypt==4.2.0
beautifulsoup4==4.12.3
bs4==0.0.2
build==1.2.1
cachetools==5.5.0
certifi==2024.7.4
cffi==1.17.0
chardet==5.2.0
charset-normalizer==3.3.2
chroma-hnswlib==0.7.6
chromadb==0.5.17
click==8.1.7
coloredlogs==15.0.1
colorlog==6.8.2
cryptography==43.0.0
curl-cffi==0.5.7
cxxfilt==0.3.0
dataclasses-json==0.6.7
decorator==5.1.1
defusedxml==0.7.1
Deprecated==1.2.14
diskcache==5.6.3
distro==1.9.0
docker==7.1.0
duckduckgo_search==6.3.2
eval_type_backport==0.2.0
exceptiongroup==1.2.2
executing==2.0.1
fake-useragent==1.5.1
fastapi==0.112.1
filelock==3.15.4
fire==0.6.0
FLAML==2.2.0
flatbuffers==24.3.25
frozenlist==1.4.1
fsspec==2024.6.1
gitdb==4.0.11
GitPython==3.1.43
google-ai-generativelanguage==0.6.6
google-api-core==2.19.1
google-api-python-client==2.141.0
google-auth==2.34.0
google-auth-httplib2==0.2.0
google-generativeai==0.7.2
googleapis-common-protos==1.63.2
googlesearch-python==1.2.5
greenlet==3.0.3
grpcio==1.65.5
grpcio-status==1.62.3
h11==0.14.0
h2==4.1.0
hpack==4.0.0
httpcore==1.0.6
httplib2==0.22.0
httptools==0.6.1
httpx==0.27.0
httpx-sse==0.4.0
huggingface-hub==0.24.6
humanfriendly==10.0
hyperframe==6.0.1
idna==3.7
importlib_metadata==8.0.0
importlib_resources==6.4.3
ipdb==0.13.13
jedi==0.19.1
jiter==0.5.0
jsonpatch==1.33
jsonpointer==3.0.0
kubernetes==30.1.0
langchain==0.3.7
langchain-anthropic==0.3.0
langchain-chroma==0.1.4
langchain-community==0.3.5
langchain-core==0.3.21
langchain-openai==0.2.5
langchain-text-splitters==0.3.2
langgraph==0.2.22
langgraph-checkpoint==1.0.10
langsmith==0.1.134
markdown-it-py==3.0.0
marshmallow==3.21.3
matplotlib-inline==0.1.7
mdurl==0.1.2
mmh3==4.1.0
monotonic==1.6
mpmath==1.3.0
msgpack==1.1.0
multidict==6.0.5
mypy-extensions==1.0.0
numpy==1.26.4
oauthlib==3.2.2
onnxruntime==1.19.0
openai==1.52.0
opentelemetry-api==1.26.0
opentelemetry-exporter-otlp-proto-common==1.26.0
opentelemetry-exporter-otlp-proto-grpc==1.26.0
opentelemetry-instrumentation==0.47b0
opentelemetry-instrumentation-asgi==0.47b0
opentelemetry-instrumentation-fastapi==0.47b0
opentelemetry-proto==1.26.0
opentelemetry-sdk==1.26.0
opentelemetry-semantic-conventions==0.47b0
opentelemetry-util-http==0.47b0
orjson==3.10.7
overrides==7.7.0
packaging==24.1
pandas==2.2.3
paramiko==3.4.1
parso==0.8.4
pexpect==4.9.0
pipdeptree==2.23.4
posthog==3.5.0
primp==0.6.4
prompt_toolkit==3.0.47
proto-plus==1.24.0
protobuf==4.25.4
psutil==6.1.1
ptyprocess==0.7.0
pure_eval==0.2.3
pyasn1==0.6.0
pyasn1_modules==0.4.0
pycparser==2.22
pydantic==2.9.2
pydantic-extra-types==2.9.0
pydantic-settings==2.5.2
pydantic_core==2.23.4
Pygments==2.18.0
PyNaCl==1.5.0
pyparsing==3.1.2
PyPika==0.48.9
pyproject_hooks==1.1.0
PySocks==1.7.1
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-multipart==0.0.20
pytz==2024.2
PyYAML==6.0.2
readmeai==0.5.96
regex==2024.7.24
requests==2.32.3
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
responses==0.23.3
rich==13.7.1
rsa==4.9
scipy==1.13.1
shellingham==1.5.4
six==1.16.0
smmap==5.0.1
sniffio==1.3.1
socksio==1.0.0
soupsieve==2.6
SQLAlchemy==2.0.32
stack-data==0.6.3
starlette==0.38.2
structlog==24.4.0
sympy==1.13.2
tabulate==0.9.0
tenacity==8.5.0
termcolor==2.4.0
tiktoken==0.7.0
tokenizers==0.20.0
toml==0.10.2
tomli==2.0.1
tornado==6.4.1
tqdm==4.65.0
traitlets==5.14.3
typer==0.12.4
types-PyYAML==6.0.12.20240917
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2024.2
tzlocal==5.2
uritemplate==4.1.1
urllib3==2.2.2
uvicorn==0.30.6
uvloop==0.20.0
watchfiles==0.23.0
wcwidth==0.2.13
websocket-client==1.8.0
websockets==12.0
wrapt==1.16.0
yarl==1.9.4
zipp==3.20.0
