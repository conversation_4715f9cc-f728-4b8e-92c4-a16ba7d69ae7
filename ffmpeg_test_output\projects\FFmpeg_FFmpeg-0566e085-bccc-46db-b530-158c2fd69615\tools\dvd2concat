#!/usr/bin/env perl

# Copyright (c) 2014 <PERSON>
#
# This file is part of FFmpeg.
#
# FFmpeg is free software; you can redistribute it and/or
# modify it under the terms of the GNU Lesser General Public License
# as published by the Free Software Foundation; either
# version 2.1 of the License, or (at your option) any later version.
#
# FFmpeg is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU Lesser General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with FFmpeg; if not, write to the Free Software Foundation, Inc.,
# 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA

=head1 NAME

dvd2concat - create a concat script for a DVD title

=head1 SYNOPSIS

tools/dvd2concat I<path/to/dvd/structure> > I<file.concat>

=head1 DESCRIPTION

This script uses B<lsdvd> to produce concat script for a DVD title.
The resulting script can be used to play the DVD using B<ffplay>, to
transcode it using B<ffmpeg> or any other similar use.

I<path/to/dvd/structure> is the path to the DVD structure hierarchy; it
normally contains a directory named B<VIDEO_TS>. It must not be encrypted
with CSS.

I<file.concat> is the output file. It can be used as an input to ffmpeg.
It will require the B<-safe 0> and
B<-protocol_whitelist file,subfile,concat> options.

=cut

use strict;
use warnings;
use Getopt::Long ":config" => "require_order";
use Pod::Usage;

my $title;

GetOptions (
  "help|usage|?|h" => sub { pod2usage({ -verbose => 1, -exitval => 0 }) },
  "manpage|m"      => sub { pod2usage({ -verbose => 2, -exitval => 0 }) },
  "title|t=i"      => \$title,
) and @ARGV == 1 or pod2usage({ -verbose => 1, -exitval => 1 });
my ($path) = @ARGV;

my $lsdvd_message =
"Make sure your lsdvd version has the two following patches applied:\n" .
"http://sourceforge.net/p/lsdvd/feature-requests/1/\n" .
"https://bugs.debian.org/cgi-bin/bugreport.cgi?bug=603826\n";

my $lsdvd = do {
  open my $l, "-|", "lsdvd", "-Op", "-x", $path
    or die "You need to install lsdvd for this script to work.\n$lsdvd_message";
  local $/;
  <$l>;
};
my %lsdvd = eval $lsdvd;
die $@ if $@;

if (!defined $title) {
  $title = $lsdvd{longest_track};
  warn "Using longest title $title\n";
}
my $track = $lsdvd{track}[$title - 1]
  or die "Title $title does not exist (1-", scalar(@{$lsdvd{track}}), ")\n";
my $vts_base = sprintf "%s/VIDEO_TS/VTS_%02d_", $path, $track->{vts};
my @frag;
for my $i (1 .. 9) {
  my $file = sprintf "%s%d.VOB", $vts_base, $i;
  my $size = -s $file or last;
  push @frag, { file => $file, size => $size >> 11 };
}

my $concat = "ffconcat version 1.0\n";
$concat .= "\nstream\nexact_stream_id 0x1E0\n";
for my $audio (@{$track->{audio}}) {
  $concat .= "\nstream\nexact_stream_id " . $audio->{streamid} . "\n";
  $concat .= "stream_meta language " . $audio->{langcode} . "\n" if $audio->{langcode};
}
for my $subp (@{$track->{subp}}) {
  $concat .= "\nstream\nexact_stream_id " . $subp->{streamid} . "\n";
  $concat .= "stream_codec dvd_subtitle\n";
  $concat .= "stream_meta language " . $subp->{langcode} . "\n" if $subp->{langcode};
  my $extradata = "";
  if ($track->{width} && $track->{height}) {
    $extradata .= "size: " . $track->{width} . "x" . $track->{height} . "\n";
  }
  if (my $pal = $track->{palette}) {
    my @pal;
    for my $c (@$pal) {
      # Adapted from mplayer/sub/vobsub.c
      my $y = ((hex($c) >> 16) & 0xFF);
      my $u = ((hex($c) >>  8) & 0xFF) - 128;
      my $v = ((hex($c) >>  0) & 0xFF) - 128;
      my ($r, $g, $b) = map { int($_ < 0 ? 0 : $_ > 255 ? 255 : $_) }
        $y + 1.4022 * $u,
        $y - 0.3456 * $u - 0.7145 * $v,
        $y               + 1.7710 * $v;
      push @pal, sprintf "%06x", ($r << 16) | ($g << 8) | $b;
    }
    $extradata .= "palette: " . join(", ", @pal) . "\n";
  }
  if ($extradata ne "") {
    $concat .= "stream_extradata " . unpack("H*", $extradata);
  }
}
my $chap_time = 0;
for my $chap (@{$track->{chapter}}) {
  $concat .= sprintf "\nchapter %d %.3f %.3f\n",
    $chap->{ix}, $chap_time, $chap_time + $chap->{length};
  $chap_time += $chap->{length};
}
for my $cell (@{$track->{cell}}) {
  my $off = $cell->{first_sector};
  die "Your lsdvd version does not print cell sectors.\n$lsdvd_message"
    unless defined $off;
  my $size = $cell->{last_sector} + 1 - $cell->{first_sector};

  my $frag = 0;
  while ($frag < @frag) {
    last if $off < $frag[$frag]->{size};
    $off -= $frag[$frag++]->{size};
  }
  die "Cell beyond VOB data\n" unless $frag < @frag;
  my $cur_off = $off;
  my $cur_size = $size;
  my @files;
  while ($cur_size > $frag[$frag]->{size} - $cur_off) {
    push @files, $frag[$frag]->{file};
    $cur_size -= $frag[$frag]->{size} - $cur_off;
    $cur_off = 0;
    die "Cell end beyond VOB data\n" unless ++$frag < @frag;
  }
  push @files, $frag[$frag]->{file};
  my $file = @files == 1 ? $files[0] : "concat:" . join("|", @files);
  my $start = $off << 11;
  my $end = ($off + $size) << 11;

  my $dur = int(1000 * $cell->{length});
  $concat .= "\nfile 'subfile:$file'\n";
  $concat .= "option start $start\n";
  $concat .= "option end   $end\n";
  $concat .= sprintf "duration %02d:%02d:%02d.%03d\n",
    int($dur / 3600000), int($dur / 60000) % 60, int($dur / 1000) % 60,
    $dur % 1000;
}

print $concat;
