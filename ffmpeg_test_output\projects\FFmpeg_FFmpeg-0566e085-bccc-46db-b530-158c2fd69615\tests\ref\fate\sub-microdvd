[Script Info]
; Script generated by FFmpeg/Lavc
ScriptType: v4.00+
PlayResX: 384
PlayResY: 288
ScaledBorderAndShadow: yes
YCbCr Matrix: None

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,Comic Sans MS,30,&H123456,&H123456,&H0,&H0,0,0,0,0,100,100,0,0,1,1,0,2,10,10,10,1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
Dialogue: 0,0:00:40.00,0:00:52.00,Default,,0,0,0,,{\c&H345678&}foo{\c}\N{\c&HABCDEF&}bar{\c}\Nbla
Dialogue: 0,0:00:52.00,0:00:56.00,Default,,0,0,0,,{\u1}{\s1}{\i1}{\b1}italic bold underline strike{\s0}{\u0}\Nitalic bold no-underline no-strike
Dialogue: 0,0:00:56.00,0:01:00.00,Default,,0,0,0,,back to
Dialogue: 0,0:01:00.00,0:01:04.00,Default,,0,0,0,,the future
Dialogue: 0,0:01:20.00,0:01:24.92,Default,,0,0,0,,{\pos(10,20)}Some more crazy stuff
Dialogue: 0,0:02:14.00,0:02:15.60,Default,,0,0,0,,this subtitle...
Dialogue: 0,0:02:15.60,0:02:40.00,Default,,0,0,0,,...continues up to...
Dialogue: 0,0:02:40.00,0:03:00.00,Default,,0,0,0,,this one.
Dialogue: 0,0:03:04.00,0:03:12.00,Default,,0,0,0,,and now...
Dialogue: 0,0:03:12.00,9:59:59.99,Default,,0,0,0,,...to the end of the presentation
