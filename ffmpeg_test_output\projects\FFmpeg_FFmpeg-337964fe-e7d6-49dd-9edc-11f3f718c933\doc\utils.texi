@chapter Syntax
@c man begin SYNTAX

This section documents the syntax and formats employed by the FFmpeg
libraries and tools.

@anchor{quoting_and_escaping}
@section Quoting and escaping

FFmpeg adopts the following quoting and escaping mechanism, unless
explicitly specified. The following rules are applied:

@itemize
@item
@samp{'} and @samp{\} are special characters (respectively used for
quoting and escaping). In addition to them, there might be other
special characters depending on the specific syntax where the escaping
and quoting are employed.

@item
A special character is escaped by prefixing it with a @samp{\}.

@item
All characters enclosed between @samp{''} are included literally in the
parsed string. The quote character @samp{'} itself cannot be quoted,
so you may need to close the quote and escape it.

@item
Leading and trailing whitespaces, unless escaped or quoted, are
removed from the parsed string.
@end itemize

Note that you may need to add a second level of escaping when using
the command line or a script, which depends on the syntax of the
adopted shell language.

The function @code{av_get_token} defined in
@file{libavutil/avstring.h} can be used to parse a token quoted or
escaped according to the rules defined above.

The tool @file{tools/ffescape} in the FFmpeg source tree can be used
to automatically quote or escape a string in a script.

@subsection Examples

@itemize
@item
Escape the string @code{Crime d'Amour} containing the @code{'} special
character:
@example
Crime d\'Amour
@end example

@item
The string above contains a quote, so the @code{'} needs to be escaped
when quoting it:
@example
'Crime d'\''Amour'
@end example

@item
Include leading or trailing whitespaces using quoting:
@example
'  this string starts and ends with whitespaces  '
@end example

@item
Escaping and quoting can be mixed together:
@example
' The string '\'string\'' is a string '
@end example

@item
To include a literal @samp{\} you can use either escaping or quoting:
@example
'c:\foo' can be written as c:\\foo
@end example
@end itemize

@anchor{date syntax}
@section Date

The accepted syntax is:
@example
[(YYYY-MM-DD|YYYYMMDD)[T|t| ]]((HH:MM:SS[.m...]]])|(HHMMSS[.m...]]]))[Z]
now
@end example

If the value is "now" it takes the current time.

Time is local time unless Z is appended, in which case it is
interpreted as UTC.
If the year-month-day part is not specified it takes the current
year-month-day.

@anchor{time duration syntax}
@section Time duration

There are two accepted syntaxes for expressing time duration.

@example
[-][@var{HH}:]@var{MM}:@var{SS}[.@var{m}...]
@end example

@var{HH} expresses the number of hours, @var{MM} the number of minutes
for a maximum of 2 digits, and @var{SS} the number of seconds for a
maximum of 2 digits. The @var{m} at the end expresses decimal value for
@var{SS}.

@emph{or}

@example
[-]@var{S}+[.@var{m}...][s|ms|us]
@end example

@var{S} expresses the number of seconds, with the optional decimal part
@var{m}.  The optional literal suffixes @samp{s}, @samp{ms} or @samp{us}
indicate to interpret the value as seconds, milliseconds or microseconds,
respectively.

In both expressions, the optional @samp{-} indicates negative duration.

@subsection Examples

The following examples are all valid time duration:

@table @samp
@item 55
55 seconds

@item 0.2
0.2 seconds

@item 200ms
200 milliseconds, that's 0.2s

@item 200000us
200000 microseconds, that's 0.2s

@item 12:03:45
12 hours, 03 minutes and 45 seconds

@item 23.189
23.189 seconds
@end table

@anchor{video size syntax}
@section Video size
Specify the size of the sourced video, it may be a string of the form
@var{width}x@var{height}, or the name of a size abbreviation.

The following abbreviations are recognized:
@table @samp
@item ntsc
720x480
@item pal
720x576
@item qntsc
352x240
@item qpal
352x288
@item sntsc
640x480
@item spal
768x576
@item film
352x240
@item ntsc-film
352x240
@item sqcif
128x96
@item qcif
176x144
@item cif
352x288
@item 4cif
704x576
@item 16cif
1408x1152
@item qqvga
160x120
@item qvga
320x240
@item vga
640x480
@item svga
800x600
@item xga
1024x768
@item uxga
1600x1200
@item qxga
2048x1536
@item sxga
1280x1024
@item qsxga
2560x2048
@item hsxga
5120x4096
@item wvga
852x480
@item wxga
1366x768
@item wsxga
1600x1024
@item wuxga
1920x1200
@item woxga
2560x1600
@item wqsxga
3200x2048
@item wquxga
3840x2400
@item whsxga
6400x4096
@item whuxga
7680x4800
@item cga
320x200
@item ega
640x350
@item hd480
852x480
@item hd720
1280x720
@item hd1080
1920x1080
@item 2k
2048x1080
@item 2kflat
1998x1080
@item 2kscope
2048x858
@item 4k
4096x2160
@item 4kflat
3996x2160
@item 4kscope
4096x1716
@item nhd
640x360
@item hqvga
240x160
@item wqvga
400x240
@item fwqvga
432x240
@item hvga
480x320
@item qhd
960x540
@item 2kdci
2048x1080
@item 4kdci
4096x2160
@item uhd2160
3840x2160
@item uhd4320
7680x4320
@end table

@anchor{video rate syntax}
@section Video rate

Specify the frame rate of a video, expressed as the number of frames
generated per second. It has to be a string in the format
@var{frame_rate_num}/@var{frame_rate_den}, an integer number, a float
number or a valid video frame rate abbreviation.

The following abbreviations are recognized:
@table @samp
@item ntsc
30000/1001
@item pal
25/1
@item qntsc
30000/1001
@item qpal
25/1
@item sntsc
30000/1001
@item spal
25/1
@item film
24/1
@item ntsc-film
24000/1001
@end table

@anchor{ratio syntax}
@section Ratio

A ratio can be expressed as an expression, or in the form
@var{numerator}:@var{denominator}.

Note that a ratio with infinite (1/0) or negative value is
considered valid, so you should check on the returned value if you
want to exclude those values.

The undefined value can be expressed using the "0:0" string.

@anchor{color syntax}
@section Color

It can be the name of a color as defined below (case insensitive match) or a
@code{[0x|#]RRGGBB[AA]} sequence, possibly followed by @@ and a string
representing the alpha component.

The alpha component may be a string composed by "0x" followed by an
hexadecimal number or a decimal number between 0.0 and 1.0, which
represents the opacity value (@samp{0x00} or @samp{0.0} means completely
transparent, @samp{0xff} or @samp{1.0} completely opaque). If the alpha
component is not specified then @samp{0xff} is assumed.

The string @samp{random} will result in a random color.

The following names of colors are recognized:
@table @samp
@item AliceBlue
0xF0F8FF
@item AntiqueWhite
0xFAEBD7
@item Aqua
0x00FFFF
@item Aquamarine
0x7FFFD4
@item Azure
0xF0FFFF
@item Beige
0xF5F5DC
@item Bisque
0xFFE4C4
@item Black
0x000000
@item BlanchedAlmond
0xFFEBCD
@item Blue
0x0000FF
@item BlueViolet
0x8A2BE2
@item Brown
0xA52A2A
@item BurlyWood
0xDEB887
@item CadetBlue
0x5F9EA0
@item Chartreuse
0x7FFF00
@item Chocolate
0xD2691E
@item Coral
0xFF7F50
@item CornflowerBlue
0x6495ED
@item Cornsilk
0xFFF8DC
@item Crimson
0xDC143C
@item Cyan
0x00FFFF
@item DarkBlue
0x00008B
@item DarkCyan
0x008B8B
@item DarkGoldenRod
0xB8860B
@item DarkGray
0xA9A9A9
@item DarkGreen
0x006400
@item DarkKhaki
0xBDB76B
@item DarkMagenta
0x8B008B
@item DarkOliveGreen
0x556B2F
@item Darkorange
0xFF8C00
@item DarkOrchid
0x9932CC
@item DarkRed
0x8B0000
@item DarkSalmon
0xE9967A
@item DarkSeaGreen
0x8FBC8F
@item DarkSlateBlue
0x483D8B
@item DarkSlateGray
0x2F4F4F
@item DarkTurquoise
0x00CED1
@item DarkViolet
0x9400D3
@item DeepPink
0xFF1493
@item DeepSkyBlue
0x00BFFF
@item DimGray
0x696969
@item DodgerBlue
0x1E90FF
@item FireBrick
0xB22222
@item FloralWhite
0xFFFAF0
@item ForestGreen
0x228B22
@item Fuchsia
0xFF00FF
@item Gainsboro
0xDCDCDC
@item GhostWhite
0xF8F8FF
@item Gold
0xFFD700
@item GoldenRod
0xDAA520
@item Gray
0x808080
@item Green
0x008000
@item GreenYellow
0xADFF2F
@item HoneyDew
0xF0FFF0
@item HotPink
0xFF69B4
@item IndianRed
0xCD5C5C
@item Indigo
0x4B0082
@item Ivory
0xFFFFF0
@item Khaki
0xF0E68C
@item Lavender
0xE6E6FA
@item LavenderBlush
0xFFF0F5
@item LawnGreen
0x7CFC00
@item LemonChiffon
0xFFFACD
@item LightBlue
0xADD8E6
@item LightCoral
0xF08080
@item LightCyan
0xE0FFFF
@item LightGoldenRodYellow
0xFAFAD2
@item LightGreen
0x90EE90
@item LightGrey
0xD3D3D3
@item LightPink
0xFFB6C1
@item LightSalmon
0xFFA07A
@item LightSeaGreen
0x20B2AA
@item LightSkyBlue
0x87CEFA
@item LightSlateGray
0x778899
@item LightSteelBlue
0xB0C4DE
@item LightYellow
0xFFFFE0
@item Lime
0x00FF00
@item LimeGreen
0x32CD32
@item Linen
0xFAF0E6
@item Magenta
0xFF00FF
@item Maroon
0x800000
@item MediumAquaMarine
0x66CDAA
@item MediumBlue
0x0000CD
@item MediumOrchid
0xBA55D3
@item MediumPurple
0x9370D8
@item MediumSeaGreen
0x3CB371
@item MediumSlateBlue
0x7B68EE
@item MediumSpringGreen
0x00FA9A
@item MediumTurquoise
0x48D1CC
@item MediumVioletRed
0xC71585
@item MidnightBlue
0x191970
@item MintCream
0xF5FFFA
@item MistyRose
0xFFE4E1
@item Moccasin
0xFFE4B5
@item NavajoWhite
0xFFDEAD
@item Navy
0x000080
@item OldLace
0xFDF5E6
@item Olive
0x808000
@item OliveDrab
0x6B8E23
@item Orange
0xFFA500
@item OrangeRed
0xFF4500
@item Orchid
0xDA70D6
@item PaleGoldenRod
0xEEE8AA
@item PaleGreen
0x98FB98
@item PaleTurquoise
0xAFEEEE
@item PaleVioletRed
0xD87093
@item PapayaWhip
0xFFEFD5
@item PeachPuff
0xFFDAB9
@item Peru
0xCD853F
@item Pink
0xFFC0CB
@item Plum
0xDDA0DD
@item PowderBlue
0xB0E0E6
@item Purple
0x800080
@item Red
0xFF0000
@item RosyBrown
0xBC8F8F
@item RoyalBlue
0x4169E1
@item SaddleBrown
0x8B4513
@item Salmon
0xFA8072
@item SandyBrown
0xF4A460
@item SeaGreen
0x2E8B57
@item SeaShell
0xFFF5EE
@item Sienna
0xA0522D
@item Silver
0xC0C0C0
@item SkyBlue
0x87CEEB
@item SlateBlue
0x6A5ACD
@item SlateGray
0x708090
@item Snow
0xFFFAFA
@item SpringGreen
0x00FF7F
@item SteelBlue
0x4682B4
@item Tan
0xD2B48C
@item Teal
0x008080
@item Thistle
0xD8BFD8
@item Tomato
0xFF6347
@item Turquoise
0x40E0D0
@item Violet
0xEE82EE
@item Wheat
0xF5DEB3
@item White
0xFFFFFF
@item WhiteSmoke
0xF5F5F5
@item Yellow
0xFFFF00
@item YellowGreen
0x9ACD32
@end table

@anchor{channel layout syntax}
@section Channel Layout

A channel layout specifies the spatial disposition of the channels in
a multi-channel audio stream. To specify a channel layout, FFmpeg
makes use of a special syntax.

Individual channels are identified by an id, as given by the table
below:
@table @samp
@item FL
front left
@item FR
front right
@item FC
front center
@item LFE
low frequency
@item BL
back left
@item BR
back right
@item FLC
front left-of-center
@item FRC
front right-of-center
@item BC
back center
@item SL
side left
@item SR
side right
@item TC
top center
@item TFL
top front left
@item TFC
top front center
@item TFR
top front right
@item TBL
top back left
@item TBC
top back center
@item TBR
top back right
@item DL
downmix left
@item DR
downmix right
@item WL
wide left
@item WR
wide right
@item SDL
surround direct left
@item SDR
surround direct right
@item LFE2
low frequency 2
@end table

Standard channel layout compositions can be specified by using the
following identifiers:
@table @samp
@item mono
FC
@item stereo
FL+FR
@item 2.1
FL+FR+LFE
@item 3.0
FL+FR+FC
@item 3.0(back)
FL+FR+BC
@item 4.0
FL+FR+FC+BC
@item quad
FL+FR+BL+BR
@item quad(side)
FL+FR+SL+SR
@item 3.1
FL+FR+FC+LFE
@item 5.0
FL+FR+FC+BL+BR
@item 5.0(side)
FL+FR+FC+SL+SR
@item 4.1
FL+FR+FC+LFE+BC
@item 5.1
FL+FR+FC+LFE+BL+BR
@item 5.1(side)
FL+FR+FC+LFE+SL+SR
@item 6.0
FL+FR+FC+BC+SL+SR
@item 6.0(front)
FL+FR+FLC+FRC+SL+SR
@item 3.1.2
FL+FR+FC+LFE+TFL+TFR
@item hexagonal
FL+FR+FC+BL+BR+BC
@item 6.1
FL+FR+FC+LFE+BC+SL+SR
@item 6.1
FL+FR+FC+LFE+BL+BR+BC
@item 6.1(front)
FL+FR+LFE+FLC+FRC+SL+SR
@item 7.0
FL+FR+FC+BL+BR+SL+SR
@item 7.0(front)
FL+FR+FC+FLC+FRC+SL+SR
@item 7.1
FL+FR+FC+LFE+BL+BR+SL+SR
@item 7.1(wide)
FL+FR+FC+LFE+BL+BR+FLC+FRC
@item 7.1(wide-side)
FL+FR+FC+LFE+FLC+FRC+SL+SR
@item 5.1.2
FL+FR+FC+LFE+BL+BR+TFL+TFR
@item octagonal
FL+FR+FC+BL+BR+BC+SL+SR
@item cube
FL+FR+BL+BR+TFL+TFR+TBL+TBR
@item 5.1.4
FL+FR+FC+LFE+BL+BR+TFL+TFR+TBL+TBR
@item 7.1.2
FL+FR+FC+LFE+BL+BR+SL+SR+TFL+TFR
@item 7.1.4
FL+FR+FC+LFE+BL+BR+SL+SR+TFL+TFR+TBL+TBR
@item 7.2.3
FL+FR+FC+LFE+BL+BR+SL+SR+TFL+TFR+TBC+LFE2
@item 9.1.4
FL+FR+FC+LFE+BL+BR+FLC+FRC+SL+SR+TFL+TFR+TBL+TBR
@item 9.1.6
FL+FR+FC+LFE+BL+BR+FLC+FRC+SL+SR+TFL+TFR+TBL+TBR+TSL+TSR
@item hexadecagonal
FL+FR+FC+BL+BR+BC+SL+SR+WL+WR+TBL+TBR+TBC+TFC+TFL+TFR
@item binaural
BIL+BIR
@item downmix
DL+DR
@item 22.2
FL+FR+FC+LFE+BL+BR+FLC+FRC+BC+SL+SR+TC+TFL+TFC+TFR+TBL+TBC+TBR+LFE2+TSL+TSR+BFC+BFL+BFR
@end table

A custom channel layout can be specified as a sequence of terms, separated by '+'.
Each term can be:
@itemize
@item
the name of a single channel (e.g. @samp{FL}, @samp{FR}, @samp{FC}, @samp{LFE}, etc.),
each optionally containing a custom name after a '@@', (e.g. @samp{FL@@Left},
@samp{FR@@Right}, @samp{FC@@Center}, @samp{LFE@@Low_Frequency}, etc.)
@end itemize

A standard channel layout can be specified by the following:
@itemize
@item
the name of a single channel (e.g. @samp{FL}, @samp{FR}, @samp{FC}, @samp{LFE}, etc.)

@item
the name of a standard channel layout (e.g. @samp{mono},
@samp{stereo}, @samp{4.0}, @samp{quad}, @samp{5.0}, etc.)

@item
a number of channels, in decimal, followed by 'c', yielding the default channel
layout for that number of channels (see the function
@code{av_channel_layout_default}). Note that not all channel counts have a
default layout.

@item
a number of channels, in decimal, followed by 'C', yielding an unknown channel
layout with the specified number of channels. Note that not all channel layout
specification strings support unknown channel layouts.

@item
a channel layout mask, in hexadecimal starting with "0x" (see the
@code{AV_CH_*} macros in @file{libavutil/channel_layout.h}.
@end itemize

Before libavutil version 53 the trailing character "c" to specify a number of
channels was optional, but now it is required, while a channel layout mask can
also be specified as a decimal number (if and only if not followed by "c" or "C").

See also the function @code{av_channel_layout_from_string} defined in
@file{libavutil/channel_layout.h}.
@c man end SYNTAX

@chapter Expression Evaluation
@c man begin EXPRESSION EVALUATION

When evaluating an arithmetic expression, FFmpeg uses an internal
formula evaluator, implemented through the @file{libavutil/eval.h}
interface.

An expression may contain unary, binary operators, constants, and
functions.

Two expressions @var{expr1} and @var{expr2} can be combined to form
another expression "@var{expr1};@var{expr2}".
@var{expr1} and @var{expr2} are evaluated in turn, and the new
expression evaluates to the value of @var{expr2}.

The following binary operators are available: @code{+}, @code{-},
@code{*}, @code{/}, @code{^}.

The following unary operators are available: @code{+}, @code{-}.

Some internal variables can be used to store and load intermediary
results. They can be accessed using the @code{ld} and @code{st}
functions with an index argument varying from 0 to 9 to specify which
internal variable to access.

The following functions are available:
@table @option
@item abs(x)
Compute absolute value of @var{x}.

@item acos(x)
Compute arccosine of @var{x}.

@item asin(x)
Compute arcsine of @var{x}.

@item atan(x)
Compute arctangent of @var{x}.

@item atan2(y, x)
Compute principal value of the arc tangent of @var{y}/@var{x}.

@item between(x, min, max)
Return 1 if @var{x} is greater than or equal to @var{min} and lesser than or
equal to @var{max}, 0 otherwise.

@item bitand(x, y)
@item bitor(x, y)
Compute bitwise and/or operation on @var{x} and @var{y}.

The results of the evaluation of @var{x} and @var{y} are converted to
integers before executing the bitwise operation.

Note that both the conversion to integer and the conversion back to
floating point can lose precision. Beware of unexpected results for
large numbers (usually 2^53 and larger).

@item ceil(expr)
Round the value of expression @var{expr} upwards to the nearest
integer. For example, "ceil(1.5)" is "2.0".

@item clip(x, min, max)
Return the value of @var{x} clipped between @var{min} and @var{max}.

@item cos(x)
Compute cosine of @var{x}.

@item cosh(x)
Compute hyperbolic cosine of @var{x}.

@item eq(x, y)
Return 1 if @var{x} and @var{y} are equivalent, 0 otherwise.

@item exp(x)
Compute exponential of @var{x} (with base @code{e}, the Euler's number).

@item floor(expr)
Round the value of expression @var{expr} downwards to the nearest
integer. For example, "floor(-1.5)" is "-2.0".

@item gauss(x)
Compute Gauss function of @var{x}, corresponding to
@code{exp(-x*x/2) / sqrt(2*PI)}.

@item gcd(x, y)
Return the greatest common divisor of @var{x} and @var{y}. If both @var{x} and
@var{y} are 0 or either or both are less than zero then behavior is undefined.

@item gt(x, y)
Return 1 if @var{x} is greater than @var{y}, 0 otherwise.

@item gte(x, y)
Return 1 if @var{x} is greater than or equal to @var{y}, 0 otherwise.

@item hypot(x, y)
This function is similar to the C function with the same name; it returns
"sqrt(@var{x}*@var{x} + @var{y}*@var{y})", the length of the hypotenuse of a
right triangle with sides of length @var{x} and @var{y}, or the distance of the
point (@var{x}, @var{y}) from the origin.

@item if(x, y)
Evaluate @var{x}, and if the result is non-zero return the result of
the evaluation of @var{y}, return 0 otherwise.

@item if(x, y, z)
Evaluate @var{x}, and if the result is non-zero return the evaluation
result of @var{y}, otherwise the evaluation result of @var{z}.

@item ifnot(x, y)
Evaluate @var{x}, and if the result is zero return the result of the
evaluation of @var{y}, return 0 otherwise.

@item ifnot(x, y, z)
Evaluate @var{x}, and if the result is zero return the evaluation
result of @var{y}, otherwise the evaluation result of @var{z}.

@item isinf(x)
Return 1.0 if @var{x} is +/-INFINITY, 0.0 otherwise.

@item isnan(x)
Return 1.0 if @var{x} is NAN, 0.0 otherwise.

@item ld(idx)
Load the value of the internal variable with index @var{idx}, which was
previously stored with st(@var{idx}, @var{expr}).
The function returns the loaded value.

@item lerp(x, y, z)
Return linear interpolation between @var{x} and @var{y} by amount of @var{z}.

@item log(x)
Compute natural logarithm of @var{x}.

@item lt(x, y)
Return 1 if @var{x} is lesser than @var{y}, 0 otherwise.

@item lte(x, y)
Return 1 if @var{x} is lesser than or equal to @var{y}, 0 otherwise.

@item max(x, y)
Return the maximum between @var{x} and @var{y}.

@item min(x, y)
Return the minimum between @var{x} and @var{y}.

@item mod(x, y)
Compute the remainder of division of @var{x} by @var{y}.

@item not(expr)
Return 1.0 if @var{expr} is zero, 0.0 otherwise.

@item pow(x, y)
Compute the power of @var{x} elevated @var{y}, it is equivalent to
"(@var{x})^(@var{y})".

@item print(t)
@item print(t, l)
Print the value of expression @var{t} with loglevel @var{l}. If @var{l} is not
specified then a default log level is used.
Return the value of the expression printed.

@item random(idx)
Return a pseudo random value between 0.0 and 1.0. @var{idx} is the
index of the internal variable used to save the seed/state, which can be
previously stored with @code{st(idx)}.

To initialize the seed, you need to store the seed value as a 64-bit
unsigned integer in the internal variable with index @var{idx}.

For example, to store the seed with value @code{42} in the internal
variable with index @code{0} and print a few random values:
@example
st(0,42); print(random(0)); print(random(0)); print(random(0))
@end example

@item randomi(idx, min, max)
Return a pseudo random value in the interval between @var{min} and
@var{max}. @var{idx} is the index of the internal variable which will be used to
save the seed/state, which can be previously stored with @code{st(idx)}.

To initialize the seed, you need to store the seed value as a 64-bit
unsigned integer in the internal variable with index @var{idx}.

@item root(expr, max)
Find an input value for which the function represented by @var{expr}
with argument @var{ld(0)} is 0 in the interval 0..@var{max}.

The expression in @var{expr} must denote a continuous function or the
result is undefined.

@var{ld(0)} is used to represent the function input value, which means that the
given expression will be evaluated multiple times with various input values that
the expression can access through @code{ld(0)}. When the expression evaluates to
0 then the corresponding input value will be returned.

@item round(expr)
Round the value of expression @var{expr} to the nearest integer. For example,
"round(1.5)" is "2.0".

@item sgn(x)
Compute sign of @var{x}.

@item sin(x)
Compute sine of @var{x}.

@item sinh(x)
Compute hyperbolic sine of @var{x}.

@item sqrt(expr)
Compute the square root of @var{expr}. This is equivalent to
"(@var{expr})^.5".

@item squish(x)
Compute expression @code{1/(1 + exp(4*x))}.

@item st(idx, expr)
Store the value of the expression @var{expr} in an internal
variable. @var{idx} specifies the index of the variable where to store
the value, and it is a value ranging from 0 to 9. The function returns
the value stored in the internal variable.

The stored value can be retrieved with @code{ld(var)}.

Note: variables are currently not shared between expressions.

@item tan(x)
Compute tangent of @var{x}.

@item tanh(x)
Compute hyperbolic tangent of @var{x}.

@item taylor(expr, x)
@item taylor(expr, x, idx)
Evaluate a Taylor series at @var{x}, given an expression representing
the @code{ld(idx)}-th derivative of a function at 0.

When the series does not converge the result is undefined.

@var{ld(idx)} is used to represent the derivative order in @var{expr},
which means that the given expression will be evaluated multiple times
with various input values that the expression can access through
@code{ld(idx)}. If @var{idx} is not specified then 0 is assumed.

Note, when you have the derivatives at y instead of 0,
@code{taylor(expr, x-y)} can be used.

@item time(0)
Return the current (wallclock) time in seconds.

@item trunc(expr)
Round the value of expression @var{expr} towards zero to the nearest
integer. For example, "trunc(-1.5)" is "-1.0".

@item while(cond, expr)
Evaluate expression @var{expr} while the expression @var{cond} is
non-zero, and returns the value of the last @var{expr} evaluation, or
NAN if @var{cond} was always false.
@end table

The following constants are available:
@table @option
@item PI
area of the unit disc, approximately 3.14
@item E
exp(1) (Euler's number), approximately 2.718
@item PHI
golden ratio (1+sqrt(5))/2, approximately 1.618
@end table

Assuming that an expression is considered "true" if it has a non-zero
value, note that:

@code{*} works like AND

@code{+} works like OR

For example the construct:
@example
if (A AND B) then C
@end example
is equivalent to:
@example
if(A*B, C)
@end example

In your C code, you can extend the list of unary and binary functions,
and define recognized constants, so that they are available for your
expressions.

The evaluator also recognizes the International System unit prefixes.
If 'i' is appended after the prefix, binary prefixes are used, which
are based on powers of 1024 instead of powers of 1000.
The 'B' postfix multiplies the value by 8, and can be appended after a
unit prefix or used alone. This allows using for example 'KB', 'MiB',
'G' and 'B' as number postfix.

The list of available International System prefixes follows, with
indication of the corresponding powers of 10 and of 2.
@table @option
@item y
10^-24 / 2^-80
@item z
10^-21 / 2^-70
@item a
10^-18 / 2^-60
@item f
10^-15 / 2^-50
@item p
10^-12 / 2^-40
@item n
10^-9 / 2^-30
@item u
10^-6 / 2^-20
@item m
10^-3 / 2^-10
@item c
10^-2
@item d
10^-1
@item h
10^2
@item k
10^3 / 2^10
@item K
10^3 / 2^10
@item M
10^6 / 2^20
@item G
10^9 / 2^30
@item T
10^12 / 2^40
@item P
10^15 / 2^50
@item E
10^18 / 2^60
@item Z
10^21 / 2^70
@item Y
10^24 / 2^80
@end table

@c man end EXPRESSION EVALUATION
