/*
 * Copyright (c) 2012 <PERSON>Addesio
 * Copyright (c) 2013-2014 Mozilla Corporation
 * Copyright (c) 2016 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * This file is part of FFmpeg.
 *
 * FFmpeg is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * FFmpeg is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with FFmpeg; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
 */

#include "libavutil/mem_internal.h"

#include "tab.h"

const uint8_t ff_opus_default_coupled_streams[] = { 0, 1, 1, 2, 2, 2, 2, 3 };

const uint8_t ff_celt_band_end[] = { 13, 17, 17, 19, 21 };

const uint16_t ff_silk_model_lbrr_flags_40[] = { 256, 0, 53, 106, 256 };
const uint16_t ff_silk_model_lbrr_flags_60[] = { 256, 0, 41, 61, 90, 131, 146, 174, 256 };

const uint16_t ff_silk_model_stereo_s1[] = {
    256,   7,   9,  10,  11,  12,  22,  46,  54,  55,  56,  59,  82, 174, 197, 200,
    201, 202, 210, 234, 244, 245, 246, 247, 249, 256
};

const uint16_t ff_silk_model_stereo_s2[] = {256, 85, 171, 256};

const uint16_t ff_silk_model_stereo_s3[] = {256, 51, 102, 154, 205, 256};

const uint16_t ff_silk_model_mid_only[] = {256, 192, 256};

const uint16_t ff_silk_model_frame_type_inactive[] = {256, 26, 256};

const uint16_t ff_silk_model_frame_type_active[] = {256, 24, 98, 246, 256};

const uint16_t ff_silk_model_gain_highbits[3][9] = {
    {256,  32, 144, 212, 241, 253, 254, 255, 256},
    {256,   2,  19,  64, 124, 186, 233, 252, 256},
    {256,   1,   4,  30, 101, 195, 245, 254, 256}
};

const uint16_t ff_silk_model_gain_lowbits[] = {256, 32, 64, 96, 128, 160, 192, 224, 256};

const uint16_t ff_silk_model_gain_delta[] = {
    256,   6,  11,  22,  53, 185, 206, 214, 218, 221, 223, 225, 227, 228, 229, 230,
    231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246,
    247, 248, 249, 250, 251, 252, 253, 254, 255, 256
};
const uint16_t ff_silk_model_lsf_s1[2][2][33] = {
    {
        {    // NB or MB, unvoiced
            256,  44,  78, 108, 127, 148, 160, 171, 174, 177, 179, 195, 197, 199, 200, 205,
            207, 208, 211, 214, 215, 216, 218, 220, 222, 225, 226, 235, 244, 246, 253, 255, 256
        }, { // NB or MB, voiced
            256,   1,  11,  12,  20,  23,  31,  39,  53,  66,  80,  81,  95, 107, 120, 131,
            142, 154, 165, 175, 185, 196, 204, 213, 221, 228, 236, 237, 238, 244, 245, 251, 256
        }
    }, {
        {    // WB, unvoiced
            256,  31,  52,  55,  72,  73,  81,  98, 102, 103, 121, 137, 141, 143, 146, 147,
            157, 158, 161, 177, 188, 204, 206, 208, 211, 213, 224, 225, 229, 238, 246, 253, 256
        }, { // WB, voiced
            256,   1,   5,  21,  26,  44,  55,  60,  74,  89,  90,  93, 105, 118, 132, 146,
            152, 166, 178, 180, 186, 187, 199, 211, 222, 232, 235, 245, 250, 251, 252, 253, 256
        }
    }
};

const uint16_t ff_silk_model_lsf_s2[32][10] = {
    // NB, MB
    { 256,   1,   2,   3,  18, 242, 253, 254, 255, 256 },
    { 256,   1,   2,   4,  38, 221, 253, 254, 255, 256 },
    { 256,   1,   2,   6,  48, 197, 252, 254, 255, 256 },
    { 256,   1,   2,  10,  62, 185, 246, 254, 255, 256 },
    { 256,   1,   4,  20,  73, 174, 248, 254, 255, 256 },
    { 256,   1,   4,  21,  76, 166, 239, 254, 255, 256 },
    { 256,   1,   8,  32,  85, 159, 226, 252, 255, 256 },
    { 256,   1,   2,  20,  83, 161, 219, 249, 255, 256 },

    // WB
    { 256,   1,   2,   3,  12, 244, 253, 254, 255, 256 },
    { 256,   1,   2,   4,  32, 218, 253, 254, 255, 256 },
    { 256,   1,   2,   5,  47, 199, 252, 254, 255, 256 },
    { 256,   1,   2,  12,  61, 187, 252, 254, 255, 256 },
    { 256,   1,   5,  24,  72, 172, 249, 254, 255, 256 },
    { 256,   1,   2,  16,  70, 170, 242, 254, 255, 256 },
    { 256,   1,   2,  17,  78, 165, 226, 251, 255, 256 },
    { 256,   1,   8,  29,  79, 156, 237, 254, 255, 256 }
};

const uint16_t ff_silk_model_lsf_s2_ext[] = { 256, 156, 216, 240, 249, 253, 255, 256 };

const uint16_t ff_silk_model_lsf_interpolation_offset[] = { 256, 13, 35, 64, 75, 256 };

const uint16_t ff_silk_model_pitch_highbits[] = {
    256,   3,   6,  12,  23,  44,  74, 106, 125, 136, 146, 158, 171, 184, 196, 207,
    216, 224, 231, 237, 241, 243, 245, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256
};

const uint16_t ff_silk_model_pitch_lowbits_mb[] = { 256, 43, 85, 128, 171, 213, 256 };

const uint16_t ff_silk_model_pitch_delta[] = {
    256,  46,  48,  50,  53,  57,  63,  73,  88, 114, 152, 182, 204, 219, 229, 236,
    242, 246, 250, 252, 254, 256
};

const uint16_t ff_silk_model_pitch_contour_nb10ms[] = { 256, 143, 193, 256 };

const uint16_t ff_silk_model_pitch_contour_nb20ms[] = {
    256,  68,  80, 101, 118, 137, 159, 189, 213, 230, 246, 256
};

const uint16_t ff_silk_model_pitch_contour_mbwb10ms[] = {
    256,  91, 137, 176, 195, 209, 221, 229, 236, 242, 247, 252, 256
};

const uint16_t ff_silk_model_pitch_contour_mbwb20ms[] = {
    256,  33,  55,  73,  89, 104, 118, 132, 145, 158, 168, 177, 186, 194, 200, 206,
    212, 217, 221, 225, 229, 232, 235, 238, 240, 242, 244, 246, 248, 250, 252, 253,
    254, 255, 256
};

const uint16_t ff_silk_model_ltp_filter[] = { 256, 77, 157, 256 };

const uint16_t ff_silk_model_ltp_filter0_sel[] = {
    256, 185, 200, 213, 226, 235, 244, 250, 256
};

const uint16_t ff_silk_model_ltp_filter1_sel[] = {
    256,  57,  91, 112, 132, 147, 160, 172, 185, 195, 205, 214, 224, 233, 241, 248, 256
};

const uint16_t ff_silk_model_ltp_filter2_sel[] = {
    256,  15,  31,  45,  57,  69,  81,  92, 103, 114, 124, 133, 142, 151, 160, 168,
    176, 184, 192, 199, 206, 212, 218, 223, 227, 232, 236, 240, 244, 247, 251, 254, 256
};

const uint16_t ff_silk_model_ltp_scale_index[] = { 256, 128, 192, 256 };

const uint16_t ff_silk_model_lcg_seed[] = { 256, 64, 128, 192, 256 };

const uint16_t ff_silk_model_exc_rate[2][10] = {
    { 256,  15,  66,  78, 124, 169, 182, 215, 242, 256 }, // unvoiced
    { 256,  33,  63,  99, 116, 150, 199, 217, 238, 256 }  // voiced
};

const uint16_t ff_silk_model_pulse_count[11][19] = {
    { 256, 131, 205, 230, 238, 241, 244, 245, 246,
      247, 248, 249, 250, 251, 252, 253, 254, 255, 256 },
    { 256,  58, 151, 211, 234, 241, 244, 245, 246,
      247, 248, 249, 250, 251, 252, 253, 254, 255, 256 },
    { 256,  43,  94, 140, 173, 197, 213, 224, 232,
      238, 241, 244, 247, 249, 250, 251, 253, 254, 256 },
    { 256,  17,  69, 140, 197, 228, 240, 245, 246,
      247, 248, 249, 250, 251, 252, 253, 254, 255, 256 },
    { 256,   6,  27,  68, 121, 170, 205, 226, 237,
      243, 246, 248, 250, 251, 252, 253, 254, 255, 256 },
    { 256,   7,  21,  43,  71, 100, 128, 153, 173,
      190, 203, 214, 223, 230, 235, 239, 243, 246, 256 },
    { 256,   2,   7,  21,  50,  92, 138, 179, 210,
      229, 240, 246, 249, 251, 252, 253, 254, 255, 256 },
    { 256,   1,   3,   7,  17,  36,  65, 100, 137,
      171, 199, 219, 233, 241, 246, 250, 252, 254, 256 },
    { 256,   1,   3,   5,  10,  19,  33,  53,  77,
      104, 132, 158, 181, 201, 216, 227, 235, 241, 256 },
    { 256,   1,   2,   3,   9,  36,  94, 150, 189,
      214, 228, 238, 244, 247, 250, 252, 253, 254, 256 },
    { 256,   2,   3,   9,  36,  94, 150, 189, 214,
      228, 238, 244, 247, 250, 252, 253, 254, 256, 256 }
};

const uint16_t ff_silk_model_pulse_location[4][168] = {
    {
        256, 126, 256,
        256, 56, 198, 256,
        256, 25, 126, 230, 256,
        256, 12, 72, 180, 244, 256,
        256, 7, 42, 126, 213, 250, 256,
        256, 4, 24, 83, 169, 232, 253, 256,
        256, 3, 15, 53, 125, 200, 242, 254, 256,
        256, 2, 10, 35, 89, 162, 221, 248, 255, 256,
        256, 2, 7, 24, 63, 126, 191, 233, 251, 255, 256,
        256, 1, 5, 17, 45, 94, 157, 211, 241, 252, 255, 256,
        256, 1, 5, 13, 33, 70, 125, 182, 223, 245, 253, 255, 256,
        256, 1, 4, 11, 26, 54, 98, 151, 199, 232, 248, 254, 255, 256,
        256, 1, 3, 9, 21, 42, 77, 124, 172, 212, 237, 249, 254, 255, 256,
        256, 1, 2, 6, 16, 33, 60, 97, 144, 187, 220, 241, 250, 254, 255, 256,
        256, 1, 2, 3, 11, 25, 47, 80, 120, 163, 201, 229, 245, 253, 254, 255, 256,
        256, 1, 2, 3, 4, 17, 35, 62, 98, 139, 180, 214, 238, 252, 253, 254, 255, 256
    },{
        256, 127, 256,
        256, 53, 202, 256,
        256, 22, 127, 233, 256,
        256, 11, 72, 183, 246, 256,
        256, 6, 41, 127, 215, 251, 256,
        256, 4, 24, 83, 170, 232, 253, 256,
        256, 3, 16, 56, 127, 200, 241, 254, 256,
        256, 3, 12, 39, 92, 162, 218, 246, 255, 256,
        256, 3, 11, 30, 67, 124, 185, 229, 249, 255, 256,
        256, 3, 10, 25, 53, 97, 151, 200, 233, 250, 255, 256,
        256, 1, 8, 21, 43, 77, 123, 171, 209, 237, 251, 255, 256,
        256, 1, 2, 13, 35, 62, 97, 139, 186, 219, 244, 254, 255, 256,
        256, 1, 2, 8, 22, 48, 85, 128, 171, 208, 234, 248, 254, 255, 256,
        256, 1, 2, 6, 16, 36, 67, 107, 149, 189, 220, 240, 250, 254, 255, 256,
        256, 1, 2, 5, 13, 29, 55, 90, 128, 166, 201, 227, 243, 251, 254, 255, 256,
        256, 1, 2, 4, 10, 22, 43, 73, 109, 147, 183, 213, 234, 246, 252, 254, 255, 256
    },{
        256, 127, 256,
        256, 49, 206, 256,
        256, 20, 127, 236, 256,
        256, 11, 71, 184, 246, 256,
        256, 7, 43, 127, 214, 250, 256,
        256, 6, 30, 87, 169, 229, 252, 256,
        256, 5, 23, 62, 126, 194, 236, 252, 256,
        256, 6, 20, 49, 96, 157, 209, 239, 253, 256,
        256, 1, 16, 39, 74, 125, 175, 215, 245, 255, 256,
        256, 1, 2, 23, 55, 97, 149, 195, 236, 254, 255, 256,
        256, 1, 7, 23, 50, 86, 128, 170, 206, 233, 249, 255, 256,
        256, 1, 6, 18, 39, 70, 108, 148, 186, 217, 238, 250, 255, 256,
        256, 1, 4, 13, 30, 56, 90, 128, 166, 200, 226, 243, 252, 255, 256,
        256, 1, 4, 11, 25, 47, 76, 110, 146, 180, 209, 231, 245, 252, 255, 256,
        256, 1, 3, 8, 19, 37, 62, 93, 128, 163, 194, 219, 237, 248, 253, 255, 256,
        256, 1, 2, 6, 15, 30, 51, 79, 111, 145, 177, 205, 226, 241, 250, 254, 255, 256
    },{
        256, 128, 256,
        256, 42, 214, 256,
        256, 21, 128, 235, 256,
        256, 12, 72, 184, 245, 256,
        256, 8, 42, 128, 214, 249, 256,
        256, 8, 31, 86, 176, 231, 251, 256,
        256, 5, 20, 58, 130, 202, 238, 253, 256,
        256, 6, 18, 45, 97, 174, 221, 241, 251, 256,
        256, 6, 25, 53, 88, 128, 168, 203, 231, 250, 256,
        256, 4, 18, 40, 71, 108, 148, 185, 216, 238, 252, 256,
        256, 3, 13, 31, 57, 90, 128, 166, 199, 225, 243, 253, 256,
        256, 2, 10, 23, 44, 73, 109, 147, 183, 212, 233, 246, 254, 256,
        256, 1, 6, 16, 33, 58, 90, 128, 166, 198, 223, 240, 250, 255, 256,
        256, 1, 5, 12, 25, 46, 75, 110, 146, 181, 210, 231, 244, 251, 255, 256,
        256, 1, 3, 8, 18, 35, 60, 92, 128, 164, 196, 221, 238, 248, 253, 255, 256,
        256, 1, 3, 7, 14, 27, 48, 76, 110, 146, 180, 208, 229, 242, 249, 253, 255, 256
    }
};

const uint16_t ff_silk_model_excitation_lsb[] = {256, 136, 256};

const uint16_t ff_silk_model_excitation_sign[3][2][7][3] = {
    {    // Inactive
        {    // Low offset
            {256,   2, 256},
            {256, 207, 256},
            {256, 189, 256},
            {256, 179, 256},
            {256, 174, 256},
            {256, 163, 256},
            {256, 157, 256}
        }, { // High offset
            {256,  58, 256},
            {256, 245, 256},
            {256, 238, 256},
            {256, 232, 256},
            {256, 225, 256},
            {256, 220, 256},
            {256, 211, 256}
        }
    }, { // Unvoiced
        {    // Low offset
            {256,   1, 256},
            {256, 210, 256},
            {256, 190, 256},
            {256, 178, 256},
            {256, 169, 256},
            {256, 162, 256},
            {256, 152, 256}
        }, { // High offset
            {256,  48, 256},
            {256, 242, 256},
            {256, 235, 256},
            {256, 224, 256},
            {256, 214, 256},
            {256, 205, 256},
            {256, 190, 256}
        }
    }, { // Voiced
        {    // Low offset
            {256,   1, 256},
            {256, 162, 256},
            {256, 152, 256},
            {256, 147, 256},
            {256, 144, 256},
            {256, 141, 256},
            {256, 138, 256}
        }, { // High offset
            {256,   8, 256},
            {256, 203, 256},
            {256, 187, 256},
            {256, 176, 256},
            {256, 168, 256},
            {256, 161, 256},
            {256, 154, 256}
        }
    }
};

const int16_t ff_silk_stereo_weights[] = {
    -13732, -10050,  -8266,  -7526,  -6500,  -5000,  -2950,   -820,
       820,   2950,   5000,   6500,   7526,   8266,  10050,  13732
};

const uint8_t ff_silk_lsf_s2_model_sel_nbmb[32][10] = {
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 },
    { 1, 3, 1, 2, 2, 1, 2, 1, 1, 1 },
    { 2, 1, 1, 1, 1, 1, 1, 1, 1, 1 },
    { 1, 2, 2, 2, 2, 1, 2, 1, 1, 1 },
    { 2, 3, 3, 3, 3, 2, 2, 2, 2, 2 },
    { 0, 5, 3, 3, 2, 2, 2, 2, 1, 1 },
    { 0, 2, 2, 2, 2, 2, 2, 2, 2, 1 },
    { 2, 3, 6, 4, 4, 4, 5, 4, 5, 5 },
    { 2, 4, 5, 5, 4, 5, 4, 6, 4, 4 },
    { 2, 4, 4, 7, 4, 5, 4, 5, 5, 4 },
    { 4, 3, 3, 3, 2, 3, 2, 2, 2, 2 },
    { 1, 5, 5, 6, 4, 5, 4, 5, 5, 5 },
    { 2, 7, 4, 6, 5, 5, 5, 5, 5, 5 },
    { 2, 7, 5, 5, 5, 5, 5, 6, 5, 4 },
    { 3, 3, 5, 4, 4, 5, 4, 5, 4, 4 },
    { 2, 3, 3, 5, 5, 4, 4, 4, 4, 4 },
    { 2, 4, 4, 6, 4, 5, 4, 5, 5, 5 },
    { 2, 5, 4, 6, 5, 5, 5, 4, 5, 4 },
    { 2, 7, 4, 5, 4, 5, 4, 5, 5, 5 },
    { 2, 5, 4, 6, 7, 6, 5, 6, 5, 4 },
    { 3, 6, 7, 4, 6, 5, 5, 6, 4, 5 },
    { 2, 7, 6, 4, 4, 4, 5, 4, 5, 5 },
    { 4, 5, 5, 4, 6, 6, 5, 6, 5, 4 },
    { 2, 5, 5, 6, 5, 6, 4, 6, 4, 4 },
    { 4, 5, 5, 5, 3, 7, 4, 5, 5, 4 },
    { 2, 3, 4, 5, 5, 6, 4, 5, 5, 4 },
    { 2, 3, 2, 3, 3, 4, 2, 3, 3, 3 },
    { 1, 1, 2, 2, 2, 2, 2, 3, 2, 2 },
    { 4, 5, 5, 6, 6, 6, 5, 6, 4, 5 },
    { 3, 5, 5, 4, 4, 4, 4, 3, 3, 2 },
    { 2, 5, 3, 7, 5, 5, 4, 4, 5, 4 },
    { 4, 4, 5, 4, 5, 6, 5, 6, 5, 4 }
};

const uint8_t ff_silk_lsf_s2_model_sel_wb[32][16] = {
    {  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8 },
    { 10, 11, 11, 11, 11, 11, 10, 10, 10, 10, 10,  9,  9,  9,  8, 11 },
    { 10, 13, 13, 11, 15, 12, 12, 13, 10, 13, 12, 13, 13, 12, 11, 11 },
    {  8, 10,  9, 10, 10,  9,  9,  9,  9,  9,  8,  8,  8,  8,  8,  9 },
    {  8, 14, 13, 12, 14, 12, 15, 13, 12, 12, 12, 13, 13, 12, 12, 11 },
    {  8, 11, 13, 13, 12, 11, 11, 13, 11, 11, 11, 11, 11, 11, 10, 12 },
    {  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8 },
    {  8, 10, 14, 11, 15, 10, 13, 11, 12, 13, 13, 12, 11, 11, 10, 11 },
    {  8, 14, 10, 14, 14, 12, 13, 12, 14, 13, 12, 12, 13, 11, 11, 11 },
    { 10,  9,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8 },
    {  8,  9,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,  9 },
    { 10, 10, 11, 12, 13, 11, 11, 11, 11, 11, 11, 11, 10, 10,  9, 11 },
    { 10, 10, 11, 11, 12, 11, 11, 11, 11, 11, 11, 11, 11, 10,  9, 11 },
    { 11, 12, 12, 12, 14, 12, 12, 13, 11, 13, 12, 12, 13, 12, 11, 12 },
    {  8, 14, 12, 13, 12, 15, 13, 10, 14, 13, 15, 12, 12, 11, 13, 11 },
    {  8,  9,  8,  9,  9,  9,  9,  9,  9,  9,  8,  8,  8,  8,  9,  8 },
    {  9, 14, 13, 15, 13, 12, 13, 11, 12, 13, 12, 12, 12, 11, 11, 12 },
    {  9, 11, 11, 12, 12, 11, 11, 13, 10, 11, 11, 13, 13, 13, 11, 12 },
    { 10, 11, 11, 10, 10, 10, 11, 10,  9, 10,  9, 10,  9,  9,  9, 12 },
    {  8, 10, 11, 13, 11, 11, 10, 10, 10,  9,  9,  8,  8,  8,  8,  8 },
    { 11, 12, 11, 13, 11, 11, 10, 10,  9,  9,  9,  9,  9, 10, 10, 12 },
    { 10, 14, 11, 15, 15, 12, 13, 12, 13, 11, 13, 11, 11, 10, 11, 11 },
    { 10, 11, 13, 14, 14, 11, 13, 11, 12, 12, 11, 11, 11, 11, 10, 12 },
    {  9, 11, 11, 12, 12, 12, 12, 11, 13, 13, 13, 11,  9,  9,  9,  9 },
    { 10, 13, 11, 14, 14, 12, 15, 12, 12, 13, 11, 12, 12, 11, 11, 11 },
    {  8, 14,  9,  9,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8 },
    {  8, 14, 14, 11, 13, 10, 13, 13, 11, 12, 12, 15, 15, 12, 12, 12 },
    { 11, 11, 15, 11, 13, 12, 11, 11, 11, 10, 10, 11, 11, 11, 10, 11 },
    {  8,  8,  9,  8,  8,  8, 10,  9, 10,  9,  9, 10, 10, 10,  9,  9 },
    {  8, 11, 10, 13, 11, 11, 10, 11, 10,  9,  8,  8,  9,  8,  8,  9 },
    { 11, 13, 13, 12, 15, 13, 11, 11, 10, 11, 10, 10,  9,  8,  9,  8 },
    { 10, 11, 13, 11, 12, 11, 11, 11, 10,  9, 10, 14, 12,  8,  8,  8 }
};

const uint8_t ff_silk_lsf_pred_weights_nbmb[2][9] = {
    {179, 138, 140, 148, 151, 149, 153, 151, 163},
    {116,  67,  82,  59,  92,  72, 100,  89,  92}
};

const uint8_t ff_silk_lsf_pred_weights_wb[2][15] = {
    {175, 148, 160, 176, 178, 173, 174, 164, 177, 174, 196, 182, 198, 192, 182},
    { 68,  62,  66,  60,  72, 117,  85,  90, 118, 136, 151, 142, 160, 142, 155}
};

const uint8_t ff_silk_lsf_weight_sel_nbmb[32][9] = {
    { 0, 1, 0, 0, 0, 0, 0, 0, 0 },
    { 1, 0, 0, 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0 },
    { 1, 1, 1, 0, 0, 0, 0, 1, 0 },
    { 0, 1, 0, 0, 0, 0, 0, 0, 0 },
    { 0, 1, 0, 0, 0, 0, 0, 0, 0 },
    { 1, 0, 1, 1, 0, 0, 0, 1, 0 },
    { 0, 1, 1, 0, 0, 1, 1, 0, 0 },
    { 0, 0, 1, 1, 0, 1, 0, 1, 1 },
    { 0, 0, 1, 1, 0, 0, 1, 1, 1 },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0 },
    { 0, 1, 0, 1, 1, 1, 1, 1, 0 },
    { 0, 1, 0, 1, 1, 1, 1, 1, 0 },
    { 0, 1, 1, 1, 1, 1, 1, 1, 0 },
    { 1, 0, 1, 1, 0, 1, 1, 1, 1 },
    { 0, 1, 1, 1, 1, 1, 0, 1, 0 },
    { 0, 0, 1, 1, 0, 1, 0, 1, 0 },
    { 0, 0, 1, 1, 1, 0, 1, 1, 1 },
    { 0, 1, 1, 0, 0, 1, 1, 1, 0 },
    { 0, 0, 0, 1, 1, 1, 0, 1, 0 },
    { 0, 1, 1, 0, 0, 1, 0, 1, 0 },
    { 0, 1, 1, 0, 0, 0, 1, 1, 0 },
    { 0, 0, 0, 0, 0, 1, 1, 1, 1 },
    { 0, 0, 1, 1, 0, 0, 0, 1, 1 },
    { 0, 0, 0, 1, 0, 1, 1, 1, 1 },
    { 0, 1, 1, 1, 1, 1, 1, 1, 0 },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0 },
    { 0, 0, 1, 0, 1, 1, 0, 1, 0 },
    { 1, 0, 0, 1, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 1, 1, 0, 1, 0, 1 },
    { 1, 0, 1, 1, 0, 1, 1, 1, 1 }
};

const uint8_t ff_silk_lsf_weight_sel_wb[32][15] = {
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1 },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 },
    { 0, 0, 1, 0, 0, 1, 1, 1, 0, 1, 1, 1, 1, 0, 0 },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0 },
    { 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 0 },
    { 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 },
    { 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0 },
    { 0, 1, 1, 0, 0, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1 },
    { 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 1, 1, 1, 1 },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1 },
    { 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 },
    { 0, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0 },
    { 0, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 0 },
    { 0, 0, 0, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 0, 0 },
    { 0, 1, 0, 0, 0, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1 },
    { 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0 },
    { 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 0 },
    { 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 1, 1, 1, 0, 0 },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1 },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0 },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 },
    { 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 1, 0 },
    { 0, 0, 1, 1, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 0 },
    { 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 1, 0 },
    { 0, 0, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1 },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1 },
    { 0, 1, 1, 0, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1 },
    { 0, 0, 0, 0, 0, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1 },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1 },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1 },
    { 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0 },
    { 0, 0, 1, 0, 0, 1, 1, 1, 0, 0, 1, 0, 0, 1, 0 }
};

const uint8_t ff_silk_lsf_codebook_nbmb[32][10] = {
    { 12,  35,  60,  83, 108, 132, 157, 180, 206, 228 },
    { 15,  32,  55,  77, 101, 125, 151, 175, 201, 225 },
    { 19,  42,  66,  89, 114, 137, 162, 184, 209, 230 },
    { 12,  25,  50,  72,  97, 120, 147, 172, 200, 223 },
    { 26,  44,  69,  90, 114, 135, 159, 180, 205, 225 },
    { 13,  22,  53,  80, 106, 130, 156, 180, 205, 228 },
    { 15,  25,  44,  64,  90, 115, 142, 168, 196, 222 },
    { 19,  24,  62,  82, 100, 120, 145, 168, 190, 214 },
    { 22,  31,  50,  79, 103, 120, 151, 170, 203, 227 },
    { 21,  29,  45,  65, 106, 124, 150, 171, 196, 224 },
    { 30,  49,  75,  97, 121, 142, 165, 186, 209, 229 },
    { 19,  25,  52,  70,  93, 116, 143, 166, 192, 219 },
    { 26,  34,  62,  75,  97, 118, 145, 167, 194, 217 },
    { 25,  33,  56,  70,  91, 113, 143, 165, 196, 223 },
    { 21,  34,  51,  72,  97, 117, 145, 171, 196, 222 },
    { 20,  29,  50,  67,  90, 117, 144, 168, 197, 221 },
    { 22,  31,  48,  66,  95, 117, 146, 168, 196, 222 },
    { 24,  33,  51,  77, 116, 134, 158, 180, 200, 224 },
    { 21,  28,  70,  87, 106, 124, 149, 170, 194, 217 },
    { 26,  33,  53,  64,  83, 117, 152, 173, 204, 225 },
    { 27,  34,  65,  95, 108, 129, 155, 174, 210, 225 },
    { 20,  26,  72,  99, 113, 131, 154, 176, 200, 219 },
    { 34,  43,  61,  78,  93, 114, 155, 177, 205, 229 },
    { 23,  29,  54,  97, 124, 138, 163, 179, 209, 229 },
    { 30,  38,  56,  89, 118, 129, 158, 178, 200, 231 },
    { 21,  29,  49,  63,  85, 111, 142, 163, 193, 222 },
    { 27,  48,  77, 103, 133, 158, 179, 196, 215, 232 },
    { 29,  47,  74,  99, 124, 151, 176, 198, 220, 237 },
    { 33,  42,  61,  76,  93, 121, 155, 174, 207, 225 },
    { 29,  53,  87, 112, 136, 154, 170, 188, 208, 227 },
    { 24,  30,  52,  84, 131, 150, 166, 186, 203, 229 },
    { 37,  48,  64,  84, 104, 118, 156, 177, 201, 230 }
};

const uint8_t ff_silk_lsf_codebook_wb[32][16] = {
    {  7,  23,  38,  54,  69,  85, 100, 116, 131, 147, 162, 178, 193, 208, 223, 239 },
    { 13,  25,  41,  55,  69,  83,  98, 112, 127, 142, 157, 171, 187, 203, 220, 236 },
    { 15,  21,  34,  51,  61,  78,  92, 106, 126, 136, 152, 167, 185, 205, 225, 240 },
    { 10,  21,  36,  50,  63,  79,  95, 110, 126, 141, 157, 173, 189, 205, 221, 237 },
    { 17,  20,  37,  51,  59,  78,  89, 107, 123, 134, 150, 164, 184, 205, 224, 240 },
    { 10,  15,  32,  51,  67,  81,  96, 112, 129, 142, 158, 173, 189, 204, 220, 236 },
    {  8,  21,  37,  51,  65,  79,  98, 113, 126, 138, 155, 168, 179, 192, 209, 218 },
    { 12,  15,  34,  55,  63,  78,  87, 108, 118, 131, 148, 167, 185, 203, 219, 236 },
    { 16,  19,  32,  36,  56,  79,  91, 108, 118, 136, 154, 171, 186, 204, 220, 237 },
    { 11,  28,  43,  58,  74,  89, 105, 120, 135, 150, 165, 180, 196, 211, 226, 241 },
    {  6,  16,  33,  46,  60,  75,  92, 107, 123, 137, 156, 169, 185, 199, 214, 225 },
    { 11,  19,  30,  44,  57,  74,  89, 105, 121, 135, 152, 169, 186, 202, 218, 234 },
    { 12,  19,  29,  46,  57,  71,  88, 100, 120, 132, 148, 165, 182, 199, 216, 233 },
    { 17,  23,  35,  46,  56,  77,  92, 106, 123, 134, 152, 167, 185, 204, 222, 237 },
    { 14,  17,  45,  53,  63,  75,  89, 107, 115, 132, 151, 171, 188, 206, 221, 240 },
    {  9,  16,  29,  40,  56,  71,  88, 103, 119, 137, 154, 171, 189, 205, 222, 237 },
    { 16,  19,  36,  48,  57,  76,  87, 105, 118, 132, 150, 167, 185, 202, 218, 236 },
    { 12,  17,  29,  54,  71,  81,  94, 104, 126, 136, 149, 164, 182, 201, 221, 237 },
    { 15,  28,  47,  62,  79,  97, 115, 129, 142, 155, 168, 180, 194, 208, 223, 238 },
    {  8,  14,  30,  45,  62,  78,  94, 111, 127, 143, 159, 175, 192, 207, 223, 239 },
    { 17,  30,  49,  62,  79,  92, 107, 119, 132, 145, 160, 174, 190, 204, 220, 235 },
    { 14,  19,  36,  45,  61,  76,  91, 108, 121, 138, 154, 172, 189, 205, 222, 238 },
    { 12,  18,  31,  45,  60,  76,  91, 107, 123, 138, 154, 171, 187, 204, 221, 236 },
    { 13,  17,  31,  43,  53,  70,  83, 103, 114, 131, 149, 167, 185, 203, 220, 237 },
    { 17,  22,  35,  42,  58,  78,  93, 110, 125, 139, 155, 170, 188, 206, 224, 240 },
    {  8,  15,  34,  50,  67,  83,  99, 115, 131, 146, 162, 178, 193, 209, 224, 239 },
    { 13,  16,  41,  66,  73,  86,  95, 111, 128, 137, 150, 163, 183, 206, 225, 241 },
    { 17,  25,  37,  52,  63,  75,  92, 102, 119, 132, 144, 160, 175, 191, 212, 231 },
    { 19,  31,  49,  65,  83, 100, 117, 133, 147, 161, 174, 187, 200, 213, 227, 242 },
    { 18,  31,  52,  68,  88, 103, 117, 126, 138, 149, 163, 177, 192, 207, 223, 239 },
    { 16,  29,  47,  61,  76,  90, 106, 119, 133, 147, 161, 176, 193, 209, 224, 240 },
    { 15,  21,  35,  50,  61,  73,  86,  97, 110, 119, 129, 141, 175, 198, 218, 237 }
};

const uint16_t ff_silk_lsf_min_spacing_nbmb[] = {
    250, 3, 6, 3, 3, 3, 4, 3, 3, 3, 461
};

const uint16_t ff_silk_lsf_min_spacing_wb[] = {
    100, 3, 40, 3, 3, 3, 5, 14, 14, 10, 11, 3, 8, 9, 7, 3, 347
};

const uint8_t ff_silk_lsf_ordering_nbmb[] = {
    0, 9, 6, 3, 4, 5, 8, 1, 2, 7
};

const uint8_t ff_silk_lsf_ordering_wb[] = {
    0, 15, 8, 7, 4, 11, 12, 3, 2, 13, 10, 5, 6, 9, 14, 1
};

const int16_t ff_silk_cosine[] = { /* (0.12) */
     4096,  4095,  4091,  4085,
     4076,  4065,  4052,  4036,
     4017,  3997,  3973,  3948,
     3920,  3889,  3857,  3822,
     3784,  3745,  3703,  3659,
     3613,  3564,  3513,  3461,
     3406,  3349,  3290,  3229,
     3166,  3102,  3035,  2967,
     2896,  2824,  2751,  2676,
     2599,  2520,  2440,  2359,
     2276,  2191,  2106,  2019,
     1931,  1842,  1751,  1660,
     1568,  1474,  1380,  1285,
     1189,  1093,   995,   897,
      799,   700,   601,   501,
      401,   301,   201,   101,
        0,  -101,  -201,  -301,
     -401,  -501,  -601,  -700,
     -799,  -897,  -995, -1093,
    -1189, -1285, -1380, -1474,
    -1568, -1660, -1751, -1842,
    -1931, -2019, -2106, -2191,
    -2276, -2359, -2440, -2520,
    -2599, -2676, -2751, -2824,
    -2896, -2967, -3035, -3102,
    -3166, -3229, -3290, -3349,
    -3406, -3461, -3513, -3564,
    -3613, -3659, -3703, -3745,
    -3784, -3822, -3857, -3889,
    -3920, -3948, -3973, -3997,
    -4017, -4036, -4052, -4065,
    -4076, -4085, -4091, -4095,
    -4096
};

const uint16_t ff_silk_pitch_scale[]   = {  4,   6,   8};

const uint16_t ff_silk_pitch_min_lag[] = { 16,  24,  32};

const uint16_t ff_silk_pitch_max_lag[] = {144, 216, 288};

const int8_t ff_silk_pitch_offset_nb10ms[3][2] = {
    { 0,  0},
    { 1,  0},
    { 0,  1}
};

const int8_t ff_silk_pitch_offset_nb20ms[11][4] = {
    { 0,  0,  0,  0},
    { 2,  1,  0, -1},
    {-1,  0,  1,  2},
    {-1,  0,  0,  1},
    {-1,  0,  0,  0},
    { 0,  0,  0,  1},
    { 0,  0,  1,  1},
    { 1,  1,  0,  0},
    { 1,  0,  0,  0},
    { 0,  0,  0, -1},
    { 1,  0,  0, -1}
};

const int8_t ff_silk_pitch_offset_mbwb10ms[12][2] = {
    { 0,  0},
    { 0,  1},
    { 1,  0},
    {-1,  1},
    { 1, -1},
    {-1,  2},
    { 2, -1},
    {-2,  2},
    { 2, -2},
    {-2,  3},
    { 3, -2},
    {-3,  3}
};

const int8_t ff_silk_pitch_offset_mbwb20ms[34][4] = {
    { 0,  0,  0,  0},
    { 0,  0,  1,  1},
    { 1,  1,  0,  0},
    {-1,  0,  0,  0},
    { 0,  0,  0,  1},
    { 1,  0,  0,  0},
    {-1,  0,  0,  1},
    { 0,  0,  0, -1},
    {-1,  0,  1,  2},
    { 1,  0,  0, -1},
    {-2, -1,  1,  2},
    { 2,  1,  0, -1},
    {-2,  0,  0,  2},
    {-2,  0,  1,  3},
    { 2,  1, -1, -2},
    {-3, -1,  1,  3},
    { 2,  0,  0, -2},
    { 3,  1,  0, -2},
    {-3, -1,  2,  4},
    {-4, -1,  1,  4},
    { 3,  1, -1, -3},
    {-4, -1,  2,  5},
    { 4,  2, -1, -3},
    { 4,  1, -1, -4},
    {-5, -1,  2,  6},
    { 5,  2, -1, -4},
    {-6, -2,  2,  6},
    {-5, -2,  2,  5},
    { 6,  2, -1, -5},
    {-7, -2,  3,  8},
    { 6,  2, -2, -6},
    { 5,  2, -2, -5},
    { 8,  3, -2, -7},
    {-9, -3,  3,  9}
};

const int8_t ff_silk_ltp_filter0_taps[8][5] = {
    {  4,   6,  24,   7,   5},
    {  0,   0,   2,   0,   0},
    { 12,  28,  41,  13,  -4},
    { -9,  15,  42,  25,  14},
    {  1,  -2,  62,  41,  -9},
    {-10,  37,  65,  -4,   3},
    { -6,   4,  66,   7,  -8},
    { 16,  14,  38,  -3,  33}
};

const int8_t ff_silk_ltp_filter1_taps[16][5] = {
    { 13,  22,  39,  23,  12},
    { -1,  36,  64,  27,  -6},
    { -7,  10,  55,  43,  17},
    {  1,   1,   8,   1,   1},
    {  6, -11,  74,  53,  -9},
    {-12,  55,  76, -12,   8},
    { -3,   3,  93,  27,  -4},
    { 26,  39,  59,   3,  -8},
    {  2,   0,  77,  11,   9},
    { -8,  22,  44,  -6,   7},
    { 40,   9,  26,   3,   9},
    { -7,  20, 101,  -7,   4},
    {  3,  -8,  42,  26,   0},
    {-15,  33,  68,   2,  23},
    { -2,  55,  46,  -2,  15},
    {  3,  -1,  21,  16,  41}
};

const int8_t ff_silk_ltp_filter2_taps[32][5] = {
    { -6,  27,  61,  39,   5},
    {-11,  42,  88,   4,   1},
    { -2,  60,  65,   6,  -4},
    { -1,  -5,  73,  56,   1},
    { -9,  19,  94,  29,  -9},
    {  0,  12,  99,   6,   4},
    {  8, -19, 102,  46, -13},
    {  3,   2,  13,   3,   2},
    {  9, -21,  84,  72, -18},
    {-11,  46, 104, -22,   8},
    { 18,  38,  48,  23,   0},
    {-16,  70,  83, -21,  11},
    {  5, -11, 117,  22,  -8},
    { -6,  23, 117, -12,   3},
    {  3,  -8,  95,  28,   4},
    {-10,  15,  77,  60, -15},
    { -1,   4, 124,   2,  -4},
    {  3,  38,  84,  24, -25},
    {  2,  13,  42,  13,  31},
    { 21,  -4,  56,  46,  -1},
    { -1,  35,  79, -13,  19},
    { -7,  65,  88,  -9, -14},
    { 20,   4,  81,  49, -29},
    { 20,   0,  75,   3, -17},
    {  5,  -9,  44,  92,  -8},
    {  1,  -3,  22,  69,  31},
    { -6,  95,  41, -12,   5},
    { 39,  67,  16,  -4,   1},
    {  0,  -6, 120,  55, -36},
    {-13,  44, 122,   4, -24},
    { 81,   5,  11,   3,   7},
    {  2,   0,   9,  10,  88}
};

const uint16_t ff_silk_ltp_scale_factor[] = {15565, 12288, 8192};

const uint8_t ff_silk_shell_blocks[3][2] = {
    { 5, 10}, // NB
    { 8, 15}, // MB
    {10, 20}  // WB
};

const uint8_t ff_silk_quant_offset[2][2] = { /* (0.23) */
    {25, 60}, // Inactive or Unvoiced
    { 8, 25}  // Voiced
};

const int ff_silk_stereo_interp_len[3] = {
    64, 96, 128
};

const uint16_t ff_celt_model_tapset[] = { 4, 2, 3, 4 };

const uint16_t ff_celt_model_spread[] = { 32, 7, 9, 30, 32 };

const uint16_t ff_celt_model_alloc_trim[] = {
    128,   2,   4,   9,  19,  41,  87, 109, 119, 124, 126, 128
};

const uint8_t ff_celt_freq_bands[] = { /* in steps of 200Hz */
    0,  1,  2,  3,  4,  5,  6,  7,  8, 10, 12, 14, 16, 20, 24, 28, 34, 40, 48, 60, 78, 100
};

const uint8_t ff_celt_freq_range[] = {
    1,  1,  1,  1,  1,  1,  1,  1,  2,  2,  2,  2,  4,  4,  4,  6,  6,  8, 12, 18, 22
};

const uint8_t ff_celt_log_freq_range[] = {
    0,  0,  0,  0,  0,  0,  0,  0,  8,  8,  8,  8, 16, 16, 16, 21, 21, 24, 29, 34, 36
};

/* Positive - increased freqeuency resolution (only possible on transients)
 * Negative - increased time resolution */
const int8_t ff_celt_tf_select[4][2][2][2] = {
    /*          OFF                        ON                Transient frame */
    /*     OFF        ON             OFF        ON           TF select flag  */
    /*   OFF  ON    OFF  ON        OFF  ON    OFF  ON        TF change flag  */
    { { { 0, -1 }, { 0, -1 } }, { { 0, -1 }, { 0, -1 } } }, /* 120 */
    { { { 0, -1 }, { 0, -2 } }, { { 1,  0 }, { 1, -1 } } }, /* 240 */
    { { { 0, -2 }, { 0, -3 } }, { { 2,  0 }, { 1, -1 } } }, /* 480 */
    { { { 0, -2 }, { 0, -3 } }, { { 3,  0 }, { 1, -1 } } }  /* 960 */
};

const float ff_celt_mean_energy[] = {
    6.437500f, 6.250000f, 5.750000f, 5.312500f, 5.062500f,
    4.812500f, 4.500000f, 4.375000f, 4.875000f, 4.687500f,
    4.562500f, 4.437500f, 4.875000f, 4.625000f, 4.312500f,
    4.500000f, 4.375000f, 4.625000f, 4.750000f, 4.437500f,
    3.750000f, 3.750000f, 3.750000f, 3.750000f, 3.750000f
};

const float ff_celt_alpha_coef[] = {
    29440.0f/32768.0f,    26112.0f/32768.0f,    21248.0f/32768.0f,    16384.0f/32768.0f
};

const float ff_celt_beta_coef[] = {
    1.0f - (30147.0f/32768.0f), 1.0f - (22282.0f/32768.0f), 1.0f - (12124.0f/32768.0f), 1.0f - (6554.0f/32768.0f),
};

const uint8_t ff_celt_coarse_energy_dist[4][2][42] = {
    {
        {       // 120-sample inter
             72, 127,  65, 129,  66, 128,  65, 128,  64, 128,  62, 128,  64, 128,
             64, 128,  92,  78,  92,  79,  92,  78,  90,  79, 116,  41, 115,  40,
            114,  40, 132,  26, 132,  26, 145,  17, 161,  12, 176,  10, 177,  11
        }, {    // 120-sample intra
             24, 179,  48, 138,  54, 135,  54, 132,  53, 134,  56, 133,  55, 132,
             55, 132,  61, 114,  70,  96,  74,  88,  75,  88,  87,  74,  89,  66,
             91,  67, 100,  59, 108,  50, 120,  40, 122,  37,  97,  43,  78,  50
        }
    }, {
        {       // 240-sample inter
             83,  78,  84,  81,  88,  75,  86,  74,  87,  71,  90,  73,  93,  74,
             93,  74, 109,  40, 114,  36, 117,  34, 117,  34, 143,  17, 145,  18,
            146,  19, 162,  12, 165,  10, 178,   7, 189,   6, 190,   8, 177,   9
        }, {    // 240-sample intra
             23, 178,  54, 115,  63, 102,  66,  98,  69,  99,  74,  89,  71,  91,
             73,  91,  78,  89,  86,  80,  92,  66,  93,  64, 102,  59, 103,  60,
            104,  60, 117,  52, 123,  44, 138,  35, 133,  31,  97,  38,  77,  45
        }
    }, {
        {       // 480-sample inter
             61,  90,  93,  60, 105,  42, 107,  41, 110,  45, 116,  38, 113,  38,
            112,  38, 124,  26, 132,  27, 136,  19, 140,  20, 155,  14, 159,  16,
            158,  18, 170,  13, 177,  10, 187,   8, 192,   6, 175,   9, 159,  10
        }, {    // 480-sample intra
             21, 178,  59, 110,  71,  86,  75,  85,  84,  83,  91,  66,  88,  73,
             87,  72,  92,  75,  98,  72, 105,  58, 107,  54, 115,  52, 114,  55,
            112,  56, 129,  51, 132,  40, 150,  33, 140,  29,  98,  35,  77,  42
        }
    }, {
        {       // 960-sample inter
             42, 121,  96,  66, 108,  43, 111,  40, 117,  44, 123,  32, 120,  36,
            119,  33, 127,  33, 134,  34, 139,  21, 147,  23, 152,  20, 158,  25,
            154,  26, 166,  21, 173,  16, 184,  13, 184,  10, 150,  13, 139,  15
        }, {    // 960-sample intra
             22, 178,  63, 114,  74,  82,  84,  83,  92,  82, 103,  62,  96,  72,
             96,  67, 101,  73, 107,  72, 113,  55, 118,  52, 125,  52, 118,  52,
            117,  55, 135,  49, 137,  39, 157,  32, 145,  29,  97,  33,  77,  40
        }
    }
};

const uint8_t ff_celt_static_alloc[11][21] = {  /* 1/32 bit/sample */
    {   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0 },
    {  90,  80,  75,  69,  63,  56,  49,  40,  34,  29,  20,  18,  10,   0,   0,   0,   0,   0,   0,   0,   0 },
    { 110, 100,  90,  84,  78,  71,  65,  58,  51,  45,  39,  32,  26,  20,  12,   0,   0,   0,   0,   0,   0 },
    { 118, 110, 103,  93,  86,  80,  75,  70,  65,  59,  53,  47,  40,  31,  23,  15,   4,   0,   0,   0,   0 },
    { 126, 119, 112, 104,  95,  89,  83,  78,  72,  66,  60,  54,  47,  39,  32,  25,  17,  12,   1,   0,   0 },
    { 134, 127, 120, 114, 103,  97,  91,  85,  78,  72,  66,  60,  54,  47,  41,  35,  29,  23,  16,  10,   1 },
    { 144, 137, 130, 124, 113, 107, 101,  95,  88,  82,  76,  70,  64,  57,  51,  45,  39,  33,  26,  15,   1 },
    { 152, 145, 138, 132, 123, 117, 111, 105,  98,  92,  86,  80,  74,  67,  61,  55,  49,  43,  36,  20,   1 },
    { 162, 155, 148, 142, 133, 127, 121, 115, 108, 102,  96,  90,  84,  77,  71,  65,  59,  53,  46,  30,   1 },
    { 172, 165, 158, 152, 143, 137, 131, 125, 118, 112, 106, 100,  94,  87,  81,  75,  69,  63,  56,  45,  20 },
    { 200, 200, 200, 200, 200, 200, 200, 200, 198, 193, 188, 183, 178, 173, 168, 163, 158, 153, 148, 129, 104 }
};

const uint8_t ff_celt_static_caps[4][2][21] = {
    {       // 120-sample
        {224, 224, 224, 224, 224, 224, 224, 224, 160, 160,
         160, 160, 185, 185, 185, 178, 178, 168, 134,  61,  37},
        {224, 224, 224, 224, 224, 224, 224, 224, 240, 240,
         240, 240, 207, 207, 207, 198, 198, 183, 144,  66,  40},
    }, {    // 240-sample
        {160, 160, 160, 160, 160, 160, 160, 160, 185, 185,
         185, 185, 193, 193, 193, 183, 183, 172, 138,  64,  38},
        {240, 240, 240, 240, 240, 240, 240, 240, 207, 207,
         207, 207, 204, 204, 204, 193, 193, 180, 143,  66,  40},
    }, {    // 480-sample
        {185, 185, 185, 185, 185, 185, 185, 185, 193, 193,
         193, 193, 193, 193, 193, 183, 183, 172, 138,  65,  39},
        {207, 207, 207, 207, 207, 207, 207, 207, 204, 204,
         204, 204, 201, 201, 201, 188, 188, 176, 141,  66,  40},
    }, {    // 960-sample
        {193, 193, 193, 193, 193, 193, 193, 193, 193, 193,
         193, 193, 194, 194, 194, 184, 184, 173, 139,  65,  39},
        {204, 204, 204, 204, 204, 204, 204, 204, 201, 201,
         201, 201, 198, 198, 198, 187, 187, 175, 140,  66,  40}
    }
};

const uint8_t ff_celt_cache_bits[392] = {
    40, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
    7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
    7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 40, 15, 23, 28,
    31, 34, 36, 38, 39, 41, 42, 43, 44, 45, 46, 47, 47, 49, 50,
    51, 52, 53, 54, 55, 55, 57, 58, 59, 60, 61, 62, 63, 63, 65,
    66, 67, 68, 69, 70, 71, 71, 40, 20, 33, 41, 48, 53, 57, 61,
    64, 66, 69, 71, 73, 75, 76, 78, 80, 82, 85, 87, 89, 91, 92,
    94, 96, 98, 101, 103, 105, 107, 108, 110, 112, 114, 117, 119, 121, 123,
    124, 126, 128, 40, 23, 39, 51, 60, 67, 73, 79, 83, 87, 91, 94,
    97, 100, 102, 105, 107, 111, 115, 118, 121, 124, 126, 129, 131, 135, 139,
    142, 145, 148, 150, 153, 155, 159, 163, 166, 169, 172, 174, 177, 179, 35,
    28, 49, 65, 78, 89, 99, 107, 114, 120, 126, 132, 136, 141, 145, 149,
    153, 159, 165, 171, 176, 180, 185, 189, 192, 199, 205, 211, 216, 220, 225,
    229, 232, 239, 245, 251, 21, 33, 58, 79, 97, 112, 125, 137, 148, 157,
    166, 174, 182, 189, 195, 201, 207, 217, 227, 235, 243, 251, 17, 35, 63,
    86, 106, 123, 139, 152, 165, 177, 187, 197, 206, 214, 222, 230, 237, 250,
    25, 31, 55, 75, 91, 105, 117, 128, 138, 146, 154, 161, 168, 174, 180,
    185, 190, 200, 208, 215, 222, 229, 235, 240, 245, 255, 16, 36, 65, 89,
    110, 128, 144, 159, 173, 185, 196, 207, 217, 226, 234, 242, 250, 11, 41,
    74, 103, 128, 151, 172, 191, 209, 225, 241, 255, 9, 43, 79, 110, 138,
    163, 186, 207, 227, 246, 12, 39, 71, 99, 123, 144, 164, 182, 198, 214,
    228, 241, 253, 9, 44, 81, 113, 142, 168, 192, 214, 235, 255, 7, 49,
    90, 127, 160, 191, 220, 247, 6, 51, 95, 134, 170, 203, 234, 7, 47,
    87, 123, 155, 184, 212, 237, 6, 52, 97, 137, 174, 208, 240, 5, 57,
    106, 151, 192, 231, 5, 59, 111, 158, 202, 243, 5, 55, 103, 147, 187,
    224, 5, 60, 113, 161, 206, 248, 4, 65, 122, 175, 224, 4, 67, 127,
    182, 234
};

const int16_t ff_celt_cache_index[105] = {
    -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, 0, 41, 41, 41,
    82, 82, 123, 164, 200, 222, 0, 0, 0, 0, 0, 0, 0, 0, 41,
    41, 41, 41, 123, 123, 123, 164, 164, 240, 266, 283, 295, 41, 41, 41,
    41, 41, 41, 41, 41, 123, 123, 123, 123, 240, 240, 240, 266, 266, 305,
    318, 328, 336, 123, 123, 123, 123, 123, 123, 123, 123, 240, 240, 240, 240,
    305, 305, 305, 318, 318, 343, 351, 358, 364, 240, 240, 240, 240, 240, 240,
    240, 240, 305, 305, 305, 305, 343, 343, 343, 351, 351, 370, 376, 382, 387,
};

const uint8_t ff_celt_log2_frac[] = {
    0, 8, 13, 16, 19, 21, 23, 24, 26, 27, 28, 29, 30, 31, 32, 32, 33, 34, 34, 35, 36, 36, 37, 37
};

const uint8_t ff_celt_bit_interleave[] = {
    0, 1, 1, 1, 2, 3, 3, 3, 2, 3, 3, 3, 2, 3, 3, 3
};

const uint8_t ff_celt_bit_deinterleave[] = {
    0x00, 0x03, 0x0C, 0x0F, 0x30, 0x33, 0x3C, 0x3F,
    0xC0, 0xC3, 0xCC, 0xCF, 0xF0, 0xF3, 0xFC, 0xFF
};

const uint8_t ff_celt_hadamard_order[] = {
    1,   0,
    3,   0,  2,  1,
    7,   0,  4,  3,  6,  1,  5,  2,
    15,  0,  8,  7, 12,  3, 11,  4, 14,  1,  9,  6, 13,  2, 10,  5,
    0,   1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15
};

const uint16_t ff_celt_qn_exp2[] = {
    16384, 17866, 19483, 21247, 23170, 25267, 27554, 30048
};

static const uint32_t celt_pvq_u[1272] = {
    /* N = 0, K = 0...176 */
    1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    /* N = 1, K = 1...176 */
    1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
    /* N = 2, K = 2...176 */
    3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41,
    43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 67, 69, 71, 73, 75, 77, 79,
    81, 83, 85, 87, 89, 91, 93, 95, 97, 99, 101, 103, 105, 107, 109, 111, 113,
    115, 117, 119, 121, 123, 125, 127, 129, 131, 133, 135, 137, 139, 141, 143,
    145, 147, 149, 151, 153, 155, 157, 159, 161, 163, 165, 167, 169, 171, 173,
    175, 177, 179, 181, 183, 185, 187, 189, 191, 193, 195, 197, 199, 201, 203,
    205, 207, 209, 211, 213, 215, 217, 219, 221, 223, 225, 227, 229, 231, 233,
    235, 237, 239, 241, 243, 245, 247, 249, 251, 253, 255, 257, 259, 261, 263,
    265, 267, 269, 271, 273, 275, 277, 279, 281, 283, 285, 287, 289, 291, 293,
    295, 297, 299, 301, 303, 305, 307, 309, 311, 313, 315, 317, 319, 321, 323,
    325, 327, 329, 331, 333, 335, 337, 339, 341, 343, 345, 347, 349, 351,
    /* N = 3, K = 3...176 */
    13, 25, 41, 61, 85, 113, 145, 181, 221, 265, 313, 365, 421, 481, 545, 613,
    685, 761, 841, 925, 1013, 1105, 1201, 1301, 1405, 1513, 1625, 1741, 1861,
    1985, 2113, 2245, 2381, 2521, 2665, 2813, 2965, 3121, 3281, 3445, 3613, 3785,
    3961, 4141, 4325, 4513, 4705, 4901, 5101, 5305, 5513, 5725, 5941, 6161, 6385,
    6613, 6845, 7081, 7321, 7565, 7813, 8065, 8321, 8581, 8845, 9113, 9385, 9661,
    9941, 10225, 10513, 10805, 11101, 11401, 11705, 12013, 12325, 12641, 12961,
    13285, 13613, 13945, 14281, 14621, 14965, 15313, 15665, 16021, 16381, 16745,
    17113, 17485, 17861, 18241, 18625, 19013, 19405, 19801, 20201, 20605, 21013,
    21425, 21841, 22261, 22685, 23113, 23545, 23981, 24421, 24865, 25313, 25765,
    26221, 26681, 27145, 27613, 28085, 28561, 29041, 29525, 30013, 30505, 31001,
    31501, 32005, 32513, 33025, 33541, 34061, 34585, 35113, 35645, 36181, 36721,
    37265, 37813, 38365, 38921, 39481, 40045, 40613, 41185, 41761, 42341, 42925,
    43513, 44105, 44701, 45301, 45905, 46513, 47125, 47741, 48361, 48985, 49613,
    50245, 50881, 51521, 52165, 52813, 53465, 54121, 54781, 55445, 56113, 56785,
    57461, 58141, 58825, 59513, 60205, 60901, 61601,
    /* N = 4, K = 4...176 */
    63, 129, 231, 377, 575, 833, 1159, 1561, 2047, 2625, 3303, 4089, 4991, 6017,
    7175, 8473, 9919, 11521, 13287, 15225, 17343, 19649, 22151, 24857, 27775,
    30913, 34279, 37881, 41727, 45825, 50183, 54809, 59711, 64897, 70375, 76153,
    82239, 88641, 95367, 102425, 109823, 117569, 125671, 134137, 142975, 152193,
    161799, 171801, 182207, 193025, 204263, 215929, 228031, 240577, 253575,
    267033, 280959, 295361, 310247, 325625, 341503, 357889, 374791, 392217,
    410175, 428673, 447719, 467321, 487487, 508225, 529543, 551449, 573951,
    597057, 620775, 645113, 670079, 695681, 721927, 748825, 776383, 804609,
    833511, 863097, 893375, 924353, 956039, 988441, 1021567, 1055425, 1090023,
    1125369, 1161471, 1198337, 1235975, 1274393, 1313599, 1353601, 1394407,
    1436025, 1478463, 1521729, 1565831, 1610777, 1656575, 1703233, 1750759,
    1799161, 1848447, 1898625, 1949703, 2001689, 2054591, 2108417, 2163175,
    2218873, 2275519, 2333121, 2391687, 2451225, 2511743, 2573249, 2635751,
    2699257, 2763775, 2829313, 2895879, 2963481, 3032127, 3101825, 3172583,
    3244409, 3317311, 3391297, 3466375, 3542553, 3619839, 3698241, 3777767,
    3858425, 3940223, 4023169, 4107271, 4192537, 4278975, 4366593, 4455399,
    4545401, 4636607, 4729025, 4822663, 4917529, 5013631, 5110977, 5209575,
    5309433, 5410559, 5512961, 5616647, 5721625, 5827903, 5935489, 6044391,
    6154617, 6266175, 6379073, 6493319, 6608921, 6725887, 6844225, 6963943,
    7085049, 7207551,
    /* N = 5, K = 5...176 */
    321, 681, 1289, 2241, 3649, 5641, 8361, 11969, 16641, 22569, 29961, 39041,
    50049, 63241, 78889, 97281, 118721, 143529, 172041, 204609, 241601, 283401,
    330409, 383041, 441729, 506921, 579081, 658689, 746241, 842249, 947241,
    1061761, 1186369, 1321641, 1468169, 1626561, 1797441, 1981449, 2179241,
    2391489, 2618881, 2862121, 3121929, 3399041, 3694209, 4008201, 4341801,
    4695809, 5071041, 5468329, 5888521, 6332481, 6801089, 7295241, 7815849,
    8363841, 8940161, 9545769, 10181641, 10848769, 11548161, 12280841, 13047849,
    13850241, 14689089, 15565481, 16480521, 17435329, 18431041, 19468809,
    20549801, 21675201, 22846209, 24064041, 25329929, 26645121, 28010881,
    29428489, 30899241, 32424449, 34005441, 35643561, 37340169, 39096641,
    40914369, 42794761, 44739241, 46749249, 48826241, 50971689, 53187081,
    55473921, 57833729, 60268041, 62778409, 65366401, 68033601, 70781609,
    73612041, 76526529, 79526721, 82614281, 85790889, 89058241, 92418049,
    95872041, 99421961, 103069569, 106816641, 110664969, 114616361, 118672641,
    122835649, 127107241, 131489289, 135983681, 140592321, 145317129, 150160041,
    155123009, 160208001, 165417001, 170752009, 176215041, 181808129, 187533321,
    193392681, 199388289, 205522241, 211796649, 218213641, 224775361, 231483969,
    238341641, 245350569, 252512961, 259831041, 267307049, 274943241, 282741889,
    290705281, 298835721, 307135529, 315607041, 324252609, 333074601, 342075401,
    351257409, 360623041, 370174729, 379914921, 389846081, 399970689, 410291241,
    420810249, 431530241, 442453761, 453583369, 464921641, 476471169, 488234561,
    500214441, 512413449, 524834241, 537479489, 550351881, 563454121, 576788929,
    590359041, 604167209, 618216201, 632508801,
    /* N = 6, K = 6...96 (technically V(109,5) fits in 32 bits, but that can't be
     achieved by splitting an Opus band) */
    1683, 3653, 7183, 13073, 22363, 36365, 56695, 85305, 124515, 177045, 246047,
    335137, 448427, 590557, 766727, 982729, 1244979, 1560549, 1937199, 2383409,
    2908411, 3522221, 4235671, 5060441, 6009091, 7095093, 8332863, 9737793,
    11326283, 13115773, 15124775, 17372905, 19880915, 22670725, 25765455,
    29189457, 32968347, 37129037, 41699767, 46710137, 52191139, 58175189,
    64696159, 71789409, 79491819, 87841821, 96879431, 106646281, 117185651,
    128542501, 140763503, 153897073, 167993403, 183104493, 199284183, 216588185,
    235074115, 254801525, 275831935, 298228865, 322057867, 347386557, 374284647,
    402823977, 433078547, 465124549, 499040399, 534906769, 572806619, 612825229,
    655050231, 699571641, 746481891, 795875861, 847850911, 902506913, 959946283,
    1020274013, 1083597703, 1150027593, 1219676595, 1292660325, 1369097135,
    1449108145, 1532817275, 1620351277, 1711839767, 1807415257, 1907213187,
    2011371957, 2120032959,
    /* N = 7, K = 7...54 (technically V(60,6) fits in 32 bits, but that can't be
     achieved by splitting an Opus band) */
    8989, 19825, 40081, 75517, 134245, 227305, 369305, 579125, 880685, 1303777,
    1884961, 2668525, 3707509, 5064793, 6814249, 9041957, 11847485, 15345233,
    19665841, 24957661, 31388293, 39146185, 48442297, 59511829, 72616013,
    88043969, 106114625, 127178701, 151620757, 179861305, 212358985, 249612805,
    292164445, 340600625, 395555537, 457713341, 527810725, 606639529, 695049433,
    793950709, 904317037, 1027188385, 1163673953, 1314955181, 1482288821,
    1667010073, 1870535785, 2094367717,
    /* N = 8, K = 8...37 (technically V(40,7) fits in 32 bits, but that can't be
     achieved by splitting an Opus band) */
    48639, 108545, 224143, 433905, 795455, 1392065, 2340495, 3800305, 5984767,
    9173505, 13726991, 20103025, 28875327, 40754369, 56610575, 77500017,
    104692735, 139703809, 184327311, 240673265, 311207743, 398796225, 506750351,
    638878193, 799538175, 993696769, 1226990095, 1505789553, 1837271615,
    2229491905,
    /* N = 9, K = 9...28 (technically V(29,8) fits in 32 bits, but that can't be
     achieved by splitting an Opus band) */
    265729, 598417, 1256465, 2485825, 4673345, 8405905, 14546705, 24331777,
    39490049, 62390545, 96220561, 145198913, 214828609, 312193553, 446304145,
    628496897, 872893441, 1196924561, 1621925137, 2173806145,
    /* N = 10, K = 10...24 */
    1462563, 3317445, 7059735, 14218905, 27298155, 50250765, 89129247, 152951073,
    254831667, 413442773, 654862247, 1014889769, 1541911931, 2300409629,
    3375210671,
    /* N = 11, K = 11...19 (technically V(20,10) fits in 32 bits, but that can't be
     achieved by splitting an Opus band) */
    8097453, 18474633, 39753273, 81270333, 158819253, 298199265, 540279585,
    948062325, 1616336765,
    /* N = 12, K = 12...18 */
    45046719, 103274625, 224298231, 464387817, 921406335, 1759885185,
    3248227095,
    /* N = 13, K = 13...16 */
    251595969, 579168825, 1267854873, 2653649025,
    /* N = 14, K = 14 */
    1409933619
};

const float ff_celt_postfilter_taps[3][3] = {
    { 0.3066406250f, 0.2170410156f, 0.1296386719f },
    { 0.4638671875f, 0.2680664062f, 0.0           },
    { 0.7998046875f, 0.1000976562f, 0.0           }
};

DECLARE_ALIGNED(32, const float, ff_celt_window_padded)[136] = {
    0.00000000f, 0.00000000f, 0.00000000f, 0.00000000f,
    0.00000000f, 0.00000000f, 0.00000000f, 0.00000000f,
    6.7286966e-05f, 0.00060551348f, 0.0016815970f, 0.0032947962f, 0.0054439943f,
    0.0081276923f, 0.011344001f, 0.015090633f, 0.019364886f, 0.024163635f,
    0.029483315f, 0.035319905f, 0.041668911f, 0.048525347f, 0.055883718f,
    0.063737999f, 0.072081616f, 0.080907428f, 0.090207705f, 0.099974111f,
    0.11019769f, 0.12086883f, 0.13197729f, 0.14351214f, 0.15546177f,
    0.16781389f, 0.18055550f, 0.19367290f, 0.20715171f, 0.22097682f,
    0.23513243f, 0.24960208f, 0.26436860f, 0.27941419f, 0.29472040f,
    0.31026818f, 0.32603788f, 0.34200931f, 0.35816177f, 0.37447407f,
    0.39092462f, 0.40749142f, 0.42415215f, 0.44088423f, 0.45766484f,
    0.47447104f, 0.49127978f, 0.50806798f, 0.52481261f, 0.54149077f,
    0.55807973f, 0.57455701f, 0.59090049f, 0.60708841f, 0.62309951f,
    0.63891306f, 0.65450896f, 0.66986776f, 0.68497077f, 0.69980010f,
    0.71433873f, 0.72857055f, 0.74248043f, 0.75605424f, 0.76927895f,
    0.78214257f, 0.79463430f, 0.80674445f, 0.81846456f, 0.82978733f,
    0.84070669f, 0.85121779f, 0.86131698f, 0.87100183f, 0.88027111f,
    0.88912479f, 0.89756398f, 0.90559094f, 0.91320904f, 0.92042270f,
    0.92723738f, 0.93365955f, 0.93969656f, 0.94535671f, 0.95064907f,
    0.95558353f, 0.96017067f, 0.96442171f, 0.96834849f, 0.97196334f,
    0.97527906f, 0.97830883f, 0.98106616f, 0.98356480f, 0.98581869f,
    0.98784191f, 0.98964856f, 0.99125274f, 0.99266849f, 0.99390969f,
    0.99499004f, 0.99592297f, 0.99672162f, 0.99739874f, 0.99796667f,
    0.99843728f, 0.99882195f, 0.99913147f, 0.99937606f, 0.99956527f,
    0.99970802f, 0.99981248f, 0.99988613f, 0.99993565f, 0.99996697f,
    0.99998518f, 0.99999457f, 0.99999859f, 0.99999982f, 1.00000000f,
    1.00000000f, 1.00000000f, 1.00000000f, 1.00000000f, 1.00000000f,
    1.00000000f, 1.00000000f, 1.00000000f,
};

/* square of the window, used for the postfilter */
const float ff_celt_window2[120] = {
    4.5275357e-09f, 3.66647e-07f, 2.82777e-06f, 1.08557e-05f, 2.96371e-05f, 6.60594e-05f,
    0.000128686f, 0.000227727f, 0.000374999f, 0.000583881f, 0.000869266f, 0.0012475f,
    0.0017363f, 0.00235471f, 0.00312299f, 0.00406253f, 0.00519576f, 0.00654601f,
    0.00813743f, 0.00999482f, 0.0121435f, 0.0146093f, 0.017418f, 0.0205957f, 0.0241684f,
    0.0281615f, 0.0326003f, 0.0375092f, 0.0429118f, 0.0488308f, 0.0552873f, 0.0623012f,
    0.0698908f, 0.0780723f, 0.0868601f, 0.0962664f, 0.106301f, 0.11697f, 0.12828f,
    0.140231f, 0.152822f, 0.166049f, 0.179905f, 0.194379f, 0.209457f, 0.225123f, 0.241356f,
    0.258133f, 0.275428f, 0.293212f, 0.311453f, 0.330116f, 0.349163f, 0.368556f, 0.388253f,
    0.40821f, 0.428382f, 0.448723f, 0.469185f, 0.48972f, 0.51028f, 0.530815f, 0.551277f,
    0.571618f, 0.59179f, 0.611747f, 0.631444f, 0.650837f, 0.669884f, 0.688547f, 0.706788f,
    0.724572f, 0.741867f, 0.758644f, 0.774877f, 0.790543f, 0.805621f, 0.820095f, 0.833951f,
    0.847178f, 0.859769f, 0.87172f, 0.88303f, 0.893699f, 0.903734f, 0.91314f, 0.921928f,
    0.930109f, 0.937699f, 0.944713f, 0.951169f, 0.957088f, 0.962491f, 0.9674f, 0.971838f,
    0.975832f, 0.979404f, 0.982582f, 0.985391f, 0.987857f, 0.990005f, 0.991863f, 0.993454f,
    0.994804f, 0.995937f, 0.996877f, 0.997645f, 0.998264f, 0.998753f, 0.999131f, 0.999416f,
    0.999625f, 0.999772f, 0.999871f, 0.999934f, 0.99997f, 0.999989f, 0.999997f, 0.99999964f, 1.0f,
};

const uint32_t * const ff_celt_pvq_u_row[15] = {
    celt_pvq_u +    0, celt_pvq_u +  176, celt_pvq_u +  351,
    celt_pvq_u +  525, celt_pvq_u +  698, celt_pvq_u +  870,
    celt_pvq_u + 1041, celt_pvq_u + 1131, celt_pvq_u + 1178,
    celt_pvq_u + 1207, celt_pvq_u + 1226, celt_pvq_u + 1240,
    celt_pvq_u + 1248, celt_pvq_u + 1254, celt_pvq_u + 1257
};

/* Deemphasis constant (alpha_p), as specified in RFC6716 as 0.8500061035.
 * libopus uses a slighly rounded constant, set to 0.85 exactly,
 * to simplify its fixed-point version, but it's not significant to impact
 * compliance. */
#define CELT_EMPH_COEFF 0.8500061035

DECLARE_ALIGNED(16, const float, ff_opus_deemph_weights)[] = {
    CELT_EMPH_COEFF,
    CELT_EMPH_COEFF*CELT_EMPH_COEFF,
    CELT_EMPH_COEFF*CELT_EMPH_COEFF*CELT_EMPH_COEFF,
    CELT_EMPH_COEFF*CELT_EMPH_COEFF*CELT_EMPH_COEFF*CELT_EMPH_COEFF,

    0,
    CELT_EMPH_COEFF,
    CELT_EMPH_COEFF*CELT_EMPH_COEFF,
    CELT_EMPH_COEFF*CELT_EMPH_COEFF*CELT_EMPH_COEFF,

    0,
    0,
    CELT_EMPH_COEFF,
    CELT_EMPH_COEFF*CELT_EMPH_COEFF,

    0,
    0,
    0,
    CELT_EMPH_COEFF,
};
