# config.py - 系统配置文件（完全参考AutoCompiler的config.py）

# 主控智能体LLM配置
LLM1_BASE_URL = "https://api.chatanywhere.tech/v1"
LLM1_MODEL = "gpt-4"  # 使用gpt-4替代o3
LLM1_API_KEY = "sk-UfDMFa37ABdmZ0Ba6M8dSA1W0aVAbwQs6gcjJB2JTrCX51qU"

# 项目分析智能体LLM配置
LLM2_BASE_URL = "https://api.chatanywhere.tech/v1"
LLM2_MODEL = "gpt-4"  # 使用gpt-4替代claude
LLM2_API_KEY = "sk-UfDMFa37ABdmZ0Ba6M8dSA1W0aVAbwQs6gcjJB2JTrCX51qU"

# 错误处理智能体LLM配置
LLM3_BASE_URL = "https://api.chatanywhere.tech/v1"
LLM3_MODEL = "gpt-4"  # 使用gpt-4替代claude
LLM3_API_KEY = "sk-UfDMFa37ABdmZ0Ba6M8dSA1W0aVAbwQs6gcjJB2JTrCX51qU"

# 嵌入模型配置
EMBEDDING_BASE_URL = "https://api.chatanywhere.tech/v1"
EMBEDDING_MODEL = "text-embedding-3-large"
EMBEDDING_API_KEY = "sk-UfDMFa37ABdmZ0Ba6M8dSA1W0aVAbwQs6gcjJB2JTrCX51qU"

# Docker镜像配置
DOCKER_IMAGES = {
    "18.04": "autocompiler:ubuntu18.04",
    "20.04": "autocompiler:ubuntu20.04", 
    "22.04": "autocompiler:ubuntu22.04"
}
DEFAULT_UBUNTU_VERSION = "20.04"

# 版本切换优先级策略
VERSION_SWITCH_PRIORITY = ["20.04", "18.04", "22.04"]

# 系统配置
DATASET_BASE_PATH = "/data/autocompile"
PROXY_CONFIG = None

# LLM配置差异化参数
LLM_CONFIGS = {
    "master_agent": {
        "model": "gpt-4",
        "temperature": 0.7,      # 平衡决策稳定性与灵活性
        "timeout": 180,          # 主控智能体可能需要更多思考时间
        "max_tokens": 4000,
        "max_iterations": 25     # 主控智能体需要更多迭代空间
    },
    "project_analyzer": {
        "model": "gpt-4",
        "temperature": 0.3,      # 分析任务需要更高准确性
        "timeout": 120,
        "max_tokens": 6000,      # 文档分析需要更长输出
        "max_iterations": 15
    },
    "error_solver": {
        "model": "gpt-4",
        "temperature": 0.8,      # 错误解决需要创造性思维
        "timeout": 150,
        "max_tokens": 3000,
        "max_iterations": 10     # 专注问题解决，减少迭代
    }
}

# 智能体提示词模板配置
MASTER_AGENT_PROMPT = """You are an experienced C/C++ project compilation orchestrator.

Available tools:
{tools}

Your responsibilities:
1. Use ProjectAnalyzer to understand project dependencies and build system
2. Execute compilation commands using Shell
3. Handle errors using ErrorSolver when you encounter problems you cannot solve
4. Switch Ubuntu versions using VersionSwitcher if compatibility issues arise
5. Monitor compilation progress and judge success/failure

Version switching capability:
- Available Ubuntu versions: 20.04 (current), 18.04, 22.04
- Switch to 18.04 for older compatibility issues (GCC version conflicts, old libraries)
- Switch to 22.04 for newer features and dependencies
- You can switch versions when encountering version compatibility errors

Error handling strategy:
- Try to solve compilation problems yourself first
- Unless you encounter a problem that you cannot solve, there is no need to call the ErrorSolver tool
- Use ErrorSolver only when you are truly stuck and need external help

Compilation success judgment:
- You have the final decision authority on whether compilation succeeds or fails
- Analyze compilation output, error messages, and file system state comprehensively
- Check if compilation artifacts (executables, libraries) are generated
- Return COMPILATION-SUCCESS, COMPILATION-FAIL, or COMPILATION-UNCERTAIN based on your analysis

Use the ReAct format:
Question: {input}
Thought: I need to analyze the current situation and decide what to do next
Action: [one of {tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: COMPILATION-SUCCESS, COMPILATION-FAIL, or COMPILATION-UNCERTAIN

Project: {project_name}
Begin!

Question: {input}
Thought:{agent_scratchpad}"""

PROJECT_ANALYZER_PROMPT = """You are a C/C++ project analysis expert.

Available tools:
{tools}

Your task: Analyze project {project_name} comprehensively using the available tools.

Analysis workflow (following AutoCompiler approach):
1. Use DependencyScanner to extract structured dependencies with ccscanner
2. Use DocumentAnalyzer to analyze README, BUILD, INSTALL files with LLM
3. Use BuildSystemDetector to identify build system if document analysis fails
4. Use DependencyResolver to resolve conflicts between different analysis results

Priority: Document analysis results > ccscanner results

Use the ReAct format:
Question: {input}
Thought: I need to understand this project's structure and dependencies
Action: [one of {tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now have enough information to provide comprehensive analysis
Final Answer: Complete project analysis with dependencies and build instructions

Project path: {project_path}
Begin!

Question: {input}
Thought:{agent_scratchpad}"""

ERROR_SOLVER_PROMPT = """You are a C/C++ compilation error resolution expert.

Available tools:
{tools}

Your task: Analyze compilation errors and find solutions using GitHub Issues and Google Search.

Core responsibilities:
1. Analyze compilation error patterns and categorize error types
2. Search GitHub Issues for project-specific solutions
3. Search Google for general compilation problem solutions
4. Validate proposed solutions for safety and relevance
5. Provide specific executable commands to resolve errors

Use the ReAct format:
Question: {input}
Thought: I need to analyze this error and search for solutions
Action: [one of {tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now have validated solutions to resolve this error
Final Answer: Recommended solutions with commands to execute

Error context:
Project: {project_name}
Error: {error_message}

Begin!

Question: {input}
Thought:{agent_scratchpad}"""