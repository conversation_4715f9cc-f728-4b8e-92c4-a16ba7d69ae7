@chapter Resampler Options
@c man begin RESAMPLER OPTIONS

The audio resampler supports the following named options.

Options may be set by specifying -@var{option} @var{value} in the
FFmpeg tools, @var{option}=@var{value} for the aresample filter,
by setting the value explicitly in the
@code{SwrContext} options or using the @file{libavutil/opt.h} API for
programmatic use.

@table @option

@item uchl, used_chlayout
Set used input channel layout. Default is unset. This option is
only used for special remapping.

@item isr, in_sample_rate
Set the input sample rate. Default value is 0.

@item osr, out_sample_rate
Set the output sample rate. Default value is 0.

@item isf, in_sample_fmt
Specify the input sample format. It is set by default to @code{none}.

@item osf, out_sample_fmt
Specify the output sample format. It is set by default to @code{none}.

@item tsf, internal_sample_fmt
Set the internal sample format. Default value is @code{none}.
This will automatically be chosen when it is not explicitly set.

@item ichl, in_chlayout
@item ochl, out_chlayout
Set the input/output channel layout.

See @ref{channel layout syntax,,the Channel Layout section in the ffmpeg-utils(1) manual,ffmpeg-utils}
for the required syntax.

@item clev, center_mix_level
Set the center mix level. It is a value expressed in deciBel, and must be
in the interval [-32,32].

@item slev, surround_mix_level
Set the surround mix level. It is a value expressed in deciBel, and must
be in the interval [-32,32].

@item lfe_mix_level
Set LFE mix into non LFE level. It is used when there is a LFE input but no
LFE output. It is a value expressed in deciBel, and must
be in the interval [-32,32].

@item rmvol, rematrix_volume
Set rematrix volume. Default value is 1.0.

@item rematrix_maxval
Set maximum output value for rematrixing.
This can be used to prevent clipping vs. preventing volume reduction.
A value of 1.0 prevents clipping.

@item flags, swr_flags
Set flags used by the converter. Default value is 0.

It supports the following individual flags:
@table @option
@item res
force resampling, this flag forces resampling to be used even when the
input and output sample rates match.
@end table

@item dither_scale
Set the dither scale. Default value is 1.

@item dither_method
Set dither method. Default value is 0.

Supported values:
@table @samp
@item rectangular
select rectangular dither
@item triangular
select triangular dither
@item triangular_hp
select triangular dither with high pass
@item lipshitz
select Lipshitz noise shaping dither.
@item shibata
select Shibata noise shaping dither.
@item low_shibata
select low Shibata noise shaping dither.
@item high_shibata
select high Shibata noise shaping dither.
@item f_weighted
select f-weighted noise shaping dither
@item modified_e_weighted
select modified-e-weighted noise shaping dither
@item improved_e_weighted
select improved-e-weighted noise shaping dither

@end table

@item resampler
Set resampling engine. Default value is swr.

Supported values:
@table @samp
@item swr
select the native SW Resampler; filter options precision and cheby are not
applicable in this case.
@item soxr
select the SoX Resampler (where available); compensation, and filter options
filter_size, phase_shift, exact_rational, filter_type & kaiser_beta, are not
applicable in this case.
@end table

@item filter_size
For swr only, set resampling filter size, default value is 32.

@item phase_shift
For swr only, set resampling phase shift, default value is 10, and must be in
the interval [0,30].

@item linear_interp
Use linear interpolation when enabled (the default). Disable it if you want
to preserve speed instead of quality when exact_rational fails.

@item exact_rational
For swr only, when enabled, try to use exact phase_count based on input and
output sample rate. However, if it is larger than @code{1 << phase_shift},
the phase_count will be @code{1 << phase_shift} as fallback. Default is enabled.

@item cutoff
Set cutoff frequency (swr: 6dB point; soxr: 0dB point) ratio; must be a float
value between 0 and 1.  Default value is 0.97 with swr, and 0.91 with soxr
(which, with a sample-rate of 44100, preserves the entire audio band to 20kHz).

@item precision
For soxr only, the precision in bits to which the resampled signal will be
calculated.  The default value of 20 (which, with suitable dithering, is
appropriate for a destination bit-depth of 16) gives SoX's 'High Quality'; a
value of 28 gives SoX's 'Very High Quality'.

@item cheby
For soxr only, selects passband rolloff none (Chebyshev) & higher-precision
approximation for 'irrational' ratios. Default value is 0.

@item async
For swr only, simple 1 parameter audio sync to timestamps using stretching,
squeezing, filling and trimming. Setting this to 1 will enable filling and
trimming, larger values represent the maximum amount in samples that the data
may be stretched or squeezed for each second.
Default value is 0, thus no compensation is applied to make the samples match
the audio timestamps.

@item first_pts
For swr only, assume the first pts should be this value. The time unit is 1 / sample rate.
This allows for padding/trimming at the start of stream. By default, no
assumption is made about the first frame's expected pts, so no padding or
trimming is done. For example, this could be set to 0 to pad the beginning with
silence if an audio stream starts after the video stream or to trim any samples
with a negative pts due to encoder delay.

@item min_comp
For swr only, set the minimum difference between timestamps and audio data (in
seconds) to trigger stretching/squeezing/filling or trimming of the
data to make it match the timestamps. The default is that
stretching/squeezing/filling and trimming is disabled
(@option{min_comp} = @code{FLT_MAX}).

@item min_hard_comp
For swr only, set the minimum difference between timestamps and audio data (in
seconds) to trigger adding/dropping samples to make it match the
timestamps.  This option effectively is a threshold to select between
hard (trim/fill) and soft (squeeze/stretch) compensation. Note that
all compensation is by default disabled through @option{min_comp}.
The default is 0.1.

@item comp_duration
For swr only, set duration (in seconds) over which data is stretched/squeezed
to make it match the timestamps. Must be a non-negative double float value,
default value is 1.0.

@item max_soft_comp
For swr only, set maximum factor by which data is stretched/squeezed to make it
match the timestamps. Must be a non-negative double float value, default value
is 0.

@item matrix_encoding
Select matrixed stereo encoding.

It accepts the following values:
@table @samp
@item none
select none
@item dolby
select Dolby
@item dplii
select Dolby Pro Logic II
@end table

Default value is @code{none}.

@item filter_type
For swr only, select resampling filter type. This only affects resampling
operations.

It accepts the following values:
@table @samp
@item cubic
select cubic
@item blackman_nuttall
select Blackman Nuttall windowed sinc
@item kaiser
select Kaiser windowed sinc
@end table

@item kaiser_beta
For swr only, set Kaiser window beta value. Must be a double float value in the
interval [2,16], default value is 9.

@item output_sample_bits
For swr only, set number of used output sample bits for dithering. Must be an integer in the
interval [0,64], default value is 0, which means it's not used.

@end table

@c man end RESAMPLER OPTIONS
