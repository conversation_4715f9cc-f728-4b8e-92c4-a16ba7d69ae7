# Dockerfile for Ubuntu 18.04 - 支持较旧项目
FROM ubuntu:18.04

ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1

# 更新软件源（18.04需要使用archive源）
RUN sed -i 's/archive.ubuntu.com/old-releases.ubuntu.com/g' /etc/apt/sources.list && \
    sed -i 's/security.ubuntu.com/old-releases.ubuntu.com/g' /etc/apt/sources.list && \
    apt-get update

# 安装完整C/C++开发环境
RUN apt-get install -y \
    # 编译工具链 (GCC 7.x-9.x)
    build-essential gcc-7 g++-7 gcc-8 g++-8 gcc-9 g++-9 clang \
    make cmake autoconf automake libtool \
    pkg-config ninja-build \
    # SSH和系统工具
    openssh-server tree vim git \
    # 构建依赖库
    zlib1g-dev libssl-dev libcurl4-openssl-dev \
    libncurses5-dev libreadline-dev libffi-dev \
    libbz2-dev libgdbm-dev libexpat1-dev \
    # 脚本语言
    python3 python3-pip python3-dev perl ruby \
    # 网络和下载工具
    wget curl unzip tar \
    # 汇编和调试工具
    yasm nasm gdb valgrind \
    # 文档和帮助工具
    texinfo help2man m4 bison flex patch \
    # 代理工具
    proxychains \
    && rm -rf /var/lib/apt/lists/*

# 设置默认GCC版本为8
RUN update-alternatives --install /usr/bin/gcc gcc /usr/bin/gcc-8 80 \
                        --slave /usr/bin/g++ g++ /usr/bin/g++-8 && \
    update-alternatives --install /usr/bin/gcc gcc /usr/bin/gcc-7 70 \
                        --slave /usr/bin/g++ g++ /usr/bin/g++-7 && \
    update-alternatives --install /usr/bin/gcc gcc /usr/bin/gcc-9 90 \
                        --slave /usr/bin/g++ g++ /usr/bin/g++-9

# SSH配置
RUN mkdir -p /var/run/sshd && \
    echo 'root:root' | chpasswd && \
    sed -i 's/#PermitRootLogin prohibit-password/PermitRootLogin yes/' /etc/ssh/sshd_config && \
    sed -i 's/#PasswordAuthentication yes/PasswordAuthentication yes/' /etc/ssh/sshd_config && \
    ssh-keygen -A

# Git全局配置
RUN git config --global user.email "<EMAIL>" && \
    git config --global user.name "AutoCompile" && \
    git config --global init.defaultBranch main && \
    git config --global safe.directory '*'

# Python基础包
RUN python3 -m pip install --no-cache-dir setuptools wheel

# 代理配置
RUN echo "strict_chain\nproxy_dns\nremote_dns_subnet 224\ntcp_read_time_out 15000\ntcp_connect_time_out 8000\n[ProxyList]\nsocks5 127.0.0.1 1080" > /etc/proxychains.conf

# 复制编译器wrapper脚本（用于IDA Pro分析的符号保护）
COPY wrapper_compiler.sh /
COPY wrapper_strip.sh /
RUN chmod +x /wrapper_compiler.sh /wrapper_strip.sh

# 设置编译器wrapper（保护符号信息用于IDA Pro分析）
RUN mv /usr/bin/gcc /usr/bin/gcc-real && ln -s /wrapper_compiler.sh /usr/bin/gcc
RUN mv /usr/bin/g++ /usr/bin/g++-real && ln -s /wrapper_compiler.sh /usr/bin/g++
RUN mv /usr/bin/cc /usr/bin/cc-real && ln -s /wrapper_compiler.sh /usr/bin/cc
RUN mv /usr/bin/c++ /usr/bin/c++-real && ln -s /wrapper_compiler.sh /usr/bin/c++
RUN mv /usr/bin/strip /usr/bin/strip-real && ln -s /wrapper_strip.sh /usr/bin/strip

# 工作目录
WORKDIR /work
RUN echo "cd /work" >> ~/.bashrc

# 编译环境优化（为IDA Pro分析优化）
ENV MAKEFLAGS="-j$(nproc)"
ENV CC=gcc
ENV CXX=g++
# 默认启用wrapper，保留符号信息用于IDA Pro分析
ENV ENABLE_COMPILER_WRAPPER=True
ENV WRAPPER_OPTI="-g -gdwarf-4"
ENV PRESERVE_SYMBOLS_FOR_IDA=True

EXPOSE 22
CMD ["/bin/bash"]
