\input texinfo @c -*- texinfo -*-
@documentencoding UTF-8

@settitle FFmpeg Bitstream Filters Documentation
@titlepage
@center @titlefont{FFmpeg Bitstream Filters Documentation}
@end titlepage

@top

@contents

@chapter Description
@c man begin DESCRIPTION

This document describes the bitstream filters provided by the
libavcodec library.

A bitstream filter operates on the encoded stream data, and performs
bitstream level modifications without performing decoding.

@c man end DESCRIPTION

@include bitstream_filters.texi

@chapter See Also

@ifhtml
@url{ffmpeg.html,ffmpeg}, @url{ffplay.html,ffplay}, @url{ffprobe.html,ffprobe},
@url{libavcodec.html,libavcodec}
@end ifhtml

@ifnothtml
ffmpeg(1), ffplay(1), ffprobe(1), libavcodec(3)
@end ifnothtml

@include authors.texi

@ignore

@setfilename ffmpeg-bitstream-filters
@settitle FFmpeg bitstream filters

@end ignore

@bye
