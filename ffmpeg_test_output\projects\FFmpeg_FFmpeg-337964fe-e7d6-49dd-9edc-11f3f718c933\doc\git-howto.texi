\input texinfo @c -*- texinfo -*-
@documentencoding UTF-8

@settitle Using Git to develop FFmpeg

@titlepage
@center @titlefont{Using Git to develop FFmpeg}
@end titlepage

@top

@contents

@chapter Introduction

This document aims in giving some quick references on a set of useful Git
commands. You should always use the extensive and detailed documentation
provided directly by Git:

@example
git --help
man git
@end example

shows you the available subcommands,

@example
git <command> --help
man git-<command>
@end example

shows information about the subcommand <command>.

Additional information could be found on the
@url{http://gitref.org, Git Reference} website.

For more information about the Git project, visit the
@url{http://git-scm.com/, Git website}.

Consult these resources whenever you have problems, they are quite exhaustive.

What follows now is a basic introduction to Git and some FFmpeg-specific
guidelines to ease the contribution to the project.

@chapter Basics Usage

@section Get Git

You can get Git from @url{http://git-scm.com/}
Most distribution and operating system provide a package for it.


@section Cloning the source tree

@example
git clone https://git.ffmpeg.org/ffmpeg.git <target>
@end example

This will put the FFmpeg sources into the directory @var{<target>}.

@example
git clone git@@source.ffmpeg.org:ffmpeg <target>
@end example

This will put the FFmpeg sources into the directory @var{<target>} and let
you push back your changes to the remote repository.

@example
git clone git@@ffmpeg.org:ffmpeg-web <target>
@end example

This will put the source of the FFmpeg website into the directory
@var{<target>} and let you push back your changes to the remote repository.

If you don't have write-access to the ffmpeg-web repository, you can
create patches after making a read-only ffmpeg-web clone:

@example
git clone git://ffmpeg.org/ffmpeg-web <target>
@end example

Make sure that you do not have Windows line endings in your checkouts,
otherwise you may experience spurious compilation failures. One way to
achieve this is to run

@example
git config --global core.autocrlf false
@end example


@anchor{Updating the source tree to the latest revision}
@section Updating the source tree to the latest revision

@example
git pull (--rebase)
@end example

pulls in the latest changes from the tracked branch. The tracked branch
can be remote. By default the master branch tracks the branch master in
the remote origin.

@float IMPORTANT
@command{--rebase} (see below) is recommended.
@end float

@section Rebasing your local branches

@example
git pull --rebase
@end example

fetches the changes from the main repository and replays your local commits
over it. This is required to keep all your local changes at the top of
FFmpeg's master tree. The master tree will reject pushes with merge commits.


@section Adding/removing files/directories

@example
git add [-A] <filename/dirname>
git rm [-r] <filename/dirname>
@end example

Git needs to get notified of all changes you make to your working
directory that makes files appear or disappear.
Line moves across files are automatically tracked.


@section Showing modifications

@example
git diff <filename(s)>
@end example

will show all local modifications in your working directory as unified diff.


@section Inspecting the changelog

@example
git log <filename(s)>
@end example

You may also use the graphical tools like @command{gitview} or @command{gitk}
or the web interface available at @url{https://git.ffmpeg.org/ffmpeg.git}.

@section Checking source tree status

@example
git status
@end example

detects all the changes you made and lists what actions will be taken in case
of a commit (additions, modifications, deletions, etc.).


@section Committing

@example
git diff --check
@end example

to double check your changes before committing them to avoid trouble later
on. All experienced developers do this on each and every commit, no matter
how small.

Every one of them has been saved from looking like a fool by this many times.
It's very easy for stray debug output or cosmetic modifications to slip in,
please avoid problems through this extra level of scrutiny.

For cosmetics-only commits you should get (almost) empty output from

@example
git diff -w -b <filename(s)>
@end example

Also check the output of

@example
git status
@end example

to make sure you don't have untracked files or deletions.

@example
git add [-i|-p|-A] <filenames/dirnames>
@end example

Make sure you have told Git your name, email address and GPG key

@example
git config --global user.name "My Name"
git config --global user.email my@@email.invalid
git config --global user.signingkey ABCDEF0123245
@end example

Enable signing all commits or use -S

@example
git config --global commit.gpgsign true
@end example

Use @option{--global} to set the global configuration for all your Git checkouts.

Git will select the changes to the files for commit. Optionally you can use
the interactive or the patch mode to select hunk by hunk what should be
added to the commit.


@example
git commit
@end example

Git will commit the selected changes to your current local branch.

You will be prompted for a log message in an editor, which is either
set in your personal configuration file through

@example
git config --global core.editor
@end example

or set by one of the following environment variables:
@var{GIT_EDITOR}, @var{VISUAL} or @var{EDITOR}.

@section Writing a commit message

Log messages should be concise but descriptive.

The first line must contain the context, a colon and a very short
summary of what the commit does. Details can be added, if necessary,
separated by an empty line. These details should not exceed 60-72 characters
per line, except when containing code.

Example of a good commit message:

@example
avcodec/cbs: add a helper to read extradata within packet side data

Using ff_cbs_read() on the raw buffer will not parse it as extradata,
resulting in parsing errors for example when handling ISOBMFF avcC.
This helper works around that.
@end example

@example
ptr might be NULL
@end example

If the summary on the first line is not enough, in the body of the message,
explain why you made a change, what you did will be obvious from the changes
themselves most of the time. Saying just "bug fix" or "10l" is bad. Remember
that people of varying skill levels look at and educate themselves while
reading through your code. Don't include filenames in log messages except in
the context, Git provides that information.

If the commit fixes a registered issue, state it in a separate line of the
body: @code{Fix Trac ticket #42.}

The first line will be used to name
the patch by @command{git format-patch}.

Common mistakes for the first line, as seen in @command{git log --oneline}
include: missing context at the beginning; description of what the code did
before the patch; line too long or wrapped to the second line.

@section Preparing a patchset

@example
git format-patch <commit> [-o directory]
@end example

will generate a set of patches for each commit between @var{<commit>} and
current @var{HEAD}. E.g.

@example
git format-patch origin/master
@end example

will generate patches for all commits on current branch which are not
present in upstream.
A useful shortcut is also

@example
git format-patch -n
@end example

which will generate patches from last @var{n} commits.
By default the patches are created in the current directory.

@section Sending patches for review

@example
git send-email <commit list|directory>
@end example

will send the patches created by @command{git format-patch} or directly
generates them. All the email fields can be configured in the global/local
configuration or overridden by command line.
Note that this tool must often be installed separately (e.g. @var{git-email}
package on Debian-based distros).


@section Renaming/moving/copying files or contents of files

Git automatically tracks such changes, making those normal commits.

@example
mv/cp path/file otherpath/otherfile
git add [-A] .
git commit
@end example


@chapter Git configuration

In order to simplify a few workflows, it is advisable to configure both
your personal Git installation and your local FFmpeg repository.

@section Personal Git installation

Add the following to your @file{~/.gitconfig} to help @command{git send-email}
and @command{git format-patch} detect renames:

@example
[diff]
        renames = copy
@end example

@section Repository configuration

In order to have @command{git send-email} automatically send patches
to the ffmpeg-devel mailing list, add the following stanza
to @file{/path/to/ffmpeg/repository/.git/config}:

@example
[sendemail]
        to = ffmpeg-devel@@ffmpeg.org
@end example

@chapter FFmpeg specific

@section Reverting broken commits

@example
git reset <commit>
@end example

@command{git reset} will uncommit the changes till @var{<commit>} rewriting
the current branch history.

@example
git commit --amend
@end example

allows one to amend the last commit details quickly.

@example
git rebase -i origin/master
@end example

will replay local commits over the main repository allowing to edit, merge
or remove some of them in the process.

@float NOTE
@command{git reset}, @command{git commit --amend} and @command{git rebase}
rewrite history, so you should use them ONLY on your local or topic branches.
The main repository will reject those changes.
@end float

@example
git revert <commit>
@end example

@command{git revert} will generate a revert commit. This will not make the
faulty commit disappear from the history.

@section Pushing changes to remote trees

@example
git push origin master --dry-run
@end example

Will simulate a push of the local master branch to the default remote
(@var{origin}). And list which branches and ranges or commits would have been
pushed.
Git will prevent you from pushing changes if the local and remote trees are
out of sync. Refer to @ref{Updating the source tree to the latest revision}.

@example
git remote add <name> <url>
@end example

Will add additional remote with a name reference, it is useful if you want
to push your local branch for review on a remote host.

@example
git push <remote> <refspec>
@end example

Will push the changes to the @var{<remote>} repository.
Omitting @var{<refspec>} makes @command{git push} update all the remote
branches matching the local ones.

@section Finding a specific svn revision

Since version 1.7.1 Git supports @samp{:/foo} syntax for specifying commits
based on a regular expression. see man gitrevisions

@example
git show :/'as revision 23456'
@end example

will show the svn changeset @samp{r23456}. With older Git versions searching in
the @command{git log} output is the easiest option (especially if a pager with
search capabilities is used).

This commit can be checked out with

@example
git checkout -b svn_23456 :/'as revision 23456'
@end example

or for Git < 1.7.1 with

@example
git checkout -b svn_23456 $SHA1
@end example

where @var{$SHA1} is the commit hash from the @command{git log} output.


@chapter gpg key generation

If you have no gpg key yet, we recommend that you create a ed25519 based key as it
is small, fast and secure. Especially it results in small signatures in git.

@example
gpg --default-new-key-algo "ed25519/cert,sign+cv25519/encr" --quick-generate-key "human@@server.com"
@end example

When generating a key, make sure the email specified matches the email used in git as some sites like
github consider mismatches a reason to declare such commits unverified. After generating a key you
can add it to the MAINTAINER file and upload it to a keyserver.

@chapter Pre-push checklist

Once you have a set of commits that you feel are ready for pushing,
work through the following checklist to doublecheck everything is in
proper order. This list tries to be exhaustive. In case you are just
pushing a typo in a comment, some of the steps may be unnecessary.
Apply your common sense, but if in doubt, err on the side of caution.

First, make sure that the commits and branches you are going to push
match what you want pushed and that nothing is missing, extraneous or
wrong. You can see what will be pushed by running the git push command
with @option{--dry-run} first. And then inspecting the commits listed with
@command{git log -p 1234567..987654}. The @command{git status} command
may help in finding local changes that have been forgotten to be added.

Next let the code pass through a full run of our test suite.

@itemize
@item @command{make distclean}
@item @command{/path/to/ffmpeg/configure}
@item @command{make fate}
@item if fate fails due to missing samples run @command{make fate-rsync} and retry
@end itemize

Make sure all your changes have been checked before pushing them, the
test suite only checks against regressions and that only to some extend. It does
obviously not check newly added features/code to be working unless you have
added a test for that (which is recommended).

Also note that every single commit should pass the test suite, not just
the result of a series of patches.

Once everything passed, push the changes to your public ffmpeg clone and post a
merge request to ffmpeg-devel. You can also push them directly but this is not
recommended.

@chapter Server Issues

Contact the project admins at @email{root@@ffmpeg.org} if you have technical
problems with the Git server.
