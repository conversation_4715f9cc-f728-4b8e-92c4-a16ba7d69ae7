#!/bin/bash

# Docker镜像构建脚本
# 为IDA Pro分析优化的自动编译环境

set -e

echo "Building AutoCompiler Docker images optimized for IDA Pro analysis..."

# 构建Ubuntu 18.04镜像
echo "Building Ubuntu 18.04 image..."
docker build -f Dockerfile.ubuntu18.04 -t autocompiler:ubuntu18.04 .

# 构建Ubuntu 20.04镜像（默认版本）
echo "Building Ubuntu 20.04 image..."
docker build -f Dockerfile.ubuntu20.04 -t autocompiler:ubuntu20.04 .

# 构建Ubuntu 22.04镜像
echo "Building Ubuntu 22.04 image..."
docker build -f Dockerfile.ubuntu22.04 -t autocompiler:ubuntu22.04 .

echo "All Docker images built successfully!"
echo ""
echo "Available images:"
echo "  - autocompiler:ubuntu18.04 (GCC 7.x-9.x)"
echo "  - autocompiler:ubuntu20.04 (GCC 9.x-11.x) [Default]"
echo "  - autocompiler:ubuntu22.04 (GCC 11.x-12.x)"
echo ""
echo "All images are optimized for IDA Pro analysis with:"
echo "  - Symbol information preserved"
echo "  - Debug information included (-g -gdwarf-4)"
echo "  - Strip operations intercepted"
echo ""
echo "Usage example:"
echo "  docker run -it --rm -v \$(pwd):/work autocompiler:ubuntu20.04"
