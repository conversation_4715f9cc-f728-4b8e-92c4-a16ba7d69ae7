/*
 * Copyright (c) The FFmpeg developers
 *
 * This file is part of FFmpeg.
 *
 * FFmpeg is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * FFmpeg is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with FFmpeg; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
 */

#ifndef FFTOOLS_TEXTFORMAT_TF_MERMAID_H
#define FFTOOLS_TEXTFORMAT_TF_MERMAID_H

typedef enum {
    AV_DIAGRAMTYPE_GRAPH,
    AV_DIAGRAMTYPE_ENTITYRELATIONSHIP,
} AVDiagramType;

typedef struct AVDiagramConfig {
    AVDiagramType diagram_type;
    const char *diagram_css;
    const char *html_template;
} AVDiagramConfig;


void av_diagram_init(AVTextFormatContext *tfc, AVDiagramConfig *diagram_config);

void av_mermaid_set_html_template(AVTextFormatContext *tfc, const char *html_template);


#endif /* FFTOOLS_TEXTFORMAT_TF_MERMAID_H */
