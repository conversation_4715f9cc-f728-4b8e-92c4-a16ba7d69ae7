@anchor{scaler_options}
@chapter Scaler Options
@c man begin SCALER OPTIONS

The video scaler supports the following named options.

Options may be set by specifying -@var{option} @var{value} in the
FFmpeg tools, with a few API-only exceptions noted below.
For programmatic use, they can be set explicitly in the
@code{SwsContext} options or through the @file{libavutil/opt.h} API.

@table @option

@anchor{sws_flags}
@item sws_flags
Set the scaler flags. This is also used to set the scaling
algorithm. Only a single algorithm should be selected. Default
value is @samp{bicubic}.

It accepts the following values:
@table @samp
@item fast_bilinear
Select fast bilinear scaling algorithm.

@item bilinear
Select bilinear scaling algorithm.

@item bicubic
Select bicubic scaling algorithm.

@item experimental
Select experimental scaling algorithm.

@item neighbor
Select nearest neighbor rescaling algorithm.

@item area
Select averaging area rescaling algorithm.

@item bicublin
Select bicubic scaling algorithm for the luma component, bilinear for
chroma components.

@item gauss
Select Gaussian rescaling algorithm.

@item sinc
Select sinc rescaling algorithm.

@item lanczos
Select Lanczos rescaling algorithm. The default width (alpha) is 3 and can be
changed by setting @code{param0}.

@item spline
Select natural bicubic spline rescaling algorithm.

@item print_info
Enable printing/debug logging.

@item accurate_rnd
Enable accurate rounding.

@item full_chroma_int
Enable full chroma interpolation.

@item full_chroma_inp
Select full chroma input.

@item bitexact
Enable bitexact output.
@end table

@item srcw @var{(API only)}
Set source width.

@item srch @var{(API only)}
Set source height.

@item dstw @var{(API only)}
Set destination width.

@item dsth @var{(API only)}
Set destination height.

@item src_format @var{(API only)}
Set source pixel format (must be expressed as an integer).

@item dst_format @var{(API only)}
Set destination pixel format (must be expressed as an integer).

@item src_range @var{(boolean)}
If value is set to @code{1}, indicates source is full range. Default value is
@code{0}, which indicates source is limited range.

@item dst_range @var{(boolean)}
If value is set to @code{1}, enable full range for destination. Default value
is @code{0}, which enables limited range.

@item gamma @var{(boolean)}
If value is set to @code{1}, enable gamma correct scaling. Default value is @code{0}.

@anchor{sws_params}
@item param0, param1
Set scaling algorithm parameters. The specified values are specific of
some scaling algorithms and ignored by others. The specified values
are floating point number values.

@item sws_dither
Set the dithering algorithm. Accepts one of the following
values. Default value is @samp{auto}.

@table @samp
@item auto
automatic choice

@item none
no dithering

@item bayer
bayer dither

@item ed
error diffusion dither

@item a_dither
arithmetic dither, based using addition

@item x_dither
arithmetic dither, based using xor (more random/less apparent patterning that
a_dither).

@end table

@item alphablend
Set the alpha blending to use when the input has alpha but the output does not.
Default value is @samp{none}.

@table @samp
@item uniform_color
Blend onto a uniform background color

@item checkerboard
Blend onto a checkerboard

@item none
No blending

@end table

@end table

@c man end SCALER OPTIONS
