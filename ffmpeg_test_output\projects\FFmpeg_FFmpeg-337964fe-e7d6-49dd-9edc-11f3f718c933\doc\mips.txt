MIPS optimizations info
===============================================

MIPS optimizations of codecs are targeting MIPS 74k family of
CPUs. Some of these optimizations are relying more on properties of
this architecture and some are relying less (and can be used on most
MIPS architectures without degradation in performance).

Along with FFMPEG copyright notice, there is MIPS copyright notice in
all the files that are created by people from MIPS Technologies.

Example of copyright notice:
===============================================
/*
 * Copyright (c) 2012
 *      MIPS Technologies, Inc., California.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 * 3. Neither the name of the MIPS Technologies, Inc., nor the names of its
 *    contributors may be used to endorse or promote products derived from
 *    this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE MIPS TECHNOLOGIES, INC. ``AS IS'' AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE MIPS TECHNOLOGIES, INC. BE LIABLE
 * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 *
 * Author:  Author Name (author_name@@mips.com)
 */

Files that have MIPS copyright notice in them:
===============================================
* libavutil/mips/
      float_dsp_mips.c
      libm_mips.h
      softfloat_tables.h
* libavcodec/mips/
      ac3dsp_mips.c
      acelp_filters_mips.c
      acelp_vectors_mips.c
      amrwbdec_mips.c
      amrwbdec_mips.h
      celp_filters_mips.c
      celp_math_mips.c
      compute_antialias_fixed.h
      compute_antialias_float.h
      lsp_mips.h
      fmtconvert_mips.c
      mpegaudiodsp_mips_fixed.c
      mpegaudiodsp_mips_float.c
