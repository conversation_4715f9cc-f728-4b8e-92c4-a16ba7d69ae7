<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <title>FFmpeg Graph</title>
</head>
<body>
<style>
    html {
        color: #666;
        font-family: <PERSON>o;
        height: 100%;
    }

    body {
        background-color: #f9f9f9;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        height: 100%;
        margin: 0;
        padding: 1.7rem 1.7rem 3.5rem 1.7rem;
    }

    div#banner {
        align-items: center;
        display: flex;
        flex-direction: row;
        margin-bottom: 1.5rem;
        margin-left: 0.6vw;
    }

    div#header {
        aspect-ratio: 1/1;
        background-image: url(https://trac.ffmpeg.org/ffmpeg-logo.png);
        background-size: cover;
        width: 1.6rem;
    }

    h1 {
        font-size: 1.2rem;
        margin: 0 0.5rem;
    }

    pre.mermaid {
        align-items: center;
        background-color: white;
        box-shadow: 2px 2px 25px 0px #00000010;
        color: transparent;
        display: flex;
        flex: 1;
        justify-content: center;
        margin: 0;
        overflow: overlay;
    }

    pre.mermaid svg {
        height: auto;
        margin: 0;
        max-width: unset !important;
        width: auto;
    }

    pre.mermaid svg * {
        user-select: none;
    }
</style>
<div id="banner">
    <div id="header"></div>
    <h1>FFmpeg Execution Graph</h1>
</div>
<pre class="mermaid">
__###__
</pre>
<script type="module">
        import vanillaJsWheelZoom from 'https://cdn.jsdelivr.net/npm/vanilla-js-wheel-zoom@9.0.4/+esm';
        import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@11/dist/mermaid.esm.min.mjs';
        function initViewer() {
            var element = document.querySelector('.mermaid svg')
            vanillaJsWheelZoom.create('pre.mermaid svg', { type: 'html', smoothTimeDrag: 0, width: element.clientWidth, height: element.clientHeight, maxScale: 3 });
        }
        mermaid.initialize({ startOnLoad: false });
        document.fonts.ready.then(() => { mermaid.run({ querySelector: '.mermaid', postRenderCallback: initViewer }); });
    </script>
</body>
</html>
