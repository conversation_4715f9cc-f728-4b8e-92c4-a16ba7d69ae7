{"mosra@@toolchains": "CMake", "SimonLarsen@@mmlgb": "C", "madler@@zlib": "C", "google@@googletest": "C++", "rlabrecque@@SteamworksParser": "Python", "Skycoder42@@QHotkey": "C++", "kamailio@@kamailio-ci": "Shell", "kairyu@@tmk_core_custom": "C", "xxxajk@@generic_storage": "C", "xxxajk@@xmem2": "C++", "xxxajk@@Arduino_Makefile_master": null, "stellar@@libsodium": "C", "xdrpp@@xdrpp": "C++", "stellar@@medida": "C++", "USCiLab@@cereal": "C++", "chriskohlhoff@@asio": "C++", "fmtlib@@fmt": "C++", "stellar@@tracy": "C++", "gabime@@spdlog": "C++", "Tvangeste@@goldendict-winlibs-prebuilt": null, "vmg@@sundown": "C", "jmendeth@@v8u": "C++", "vmg@@houdini": "C", "Nitrokey@@libnitrokey": "C++", "tplgy@@cppcodec": "C++", "libuv@@libuv": "C", "LuaJIT@@LuaJIT": "C", "lua@@lua": "C", "keplerproject@@lua-compat-5.3": "C", "neurolabusc@@dcm_qa": "Shell", "neurolabusc@@dcm_qa_nih": "Shell", "neurolabusc@@dcm_qa_uih": "Shell", "openssl@@openssl": "C", "simonlindholm@@asm-processor": "Python", "simonlindholm@@asm-differ": "Python", "rasmusgo@@apriltag": "C", "wxWidgets@@wxWidgets": "C++", "ValveSoftware@@openvr": "C++", "stenzek@@duckstation-ext-qt-minimal": "C++", "mit-fast@@multicopterDynamicsSim": "C++", "mit-aera@@carDynamicsSim": "C++", "riscv@@riscv-opcodes": "Python", "riscv@@riscv-gnu-toolchain": "C", "riscv@@riscv-compliance": "HTML", "riscv@@docs-templates": "TeX", "ben-marshall@@riscv-bitmanip": "<PERSON><PERSON><PERSON>", "ben-marshall@@sail-riscv": null, "riscv@@riscv-isa-sim": "C", "hszhao@@PSPNet": "C++", "PazerOP@@ValveFileVDF": "C++", "PazerOP@@SourceRCON": "C++", "PazerOP@@imgui_desktop": "C++", "PazerOP@@stuff": "C++", "microsoft@@vcpkg": "CMake", "deemru@@msspi": "C++", "saturneric@@gpgme": null, "saturneric@@libassuan": null, "saturneric@@libgpg-error": null, "Microsoft@@snmalloc": "C++", "CompilerTeaching@@Pegmatite": "C++", "CLIUtils@@CLI11": "C++", "llvm@@llvm-project": null, "DragonJoker@@CMakeUtils": "CMake", "KhronosGroup@@SPIRV-Cross": "GLSL", "KhronosGroup@@glslang": "C++", "facebook@@rocksdb": "C++", "vinniefalco@@docca": "XSLT", "pybind@@pybind11": "C++", "libjpeg-turbo@@libjpeg-turbo": "C", "EdgeTX@@libopenui": "C++", "raphaelcoeffic@@AccessDenied": "C", "jbeder@@yaml-cpp": "C++", "onqtam@@doctest": "C++", "clab@@dynet": "C++", "patrikhuber@@eos": "C++", "libigl@@libigl": "C++", "tangrams@@harfbuzz-icu-freetype": "C++", "glfw@@glfw": "C", "Perlmint@@glew-cmake": "C", "behdad@@harfbuzz": "C++", "cubicdaiya@@dtl": "C++", "google@@AFL": "C", "w23@@atto": "C", "cruppstahl@@libfor": "C", "Cyan4973@@lz4": "C", "kiyo-masui@@bitshuffle": "C", "lemire@@LittleIntPacker": "C", "lemire@@streamvbyte": "C", "cruppstahl@@libvbyte": "C", "lemire@@MaskedVByte": "C", "lemire@@simdcomp": "C", "lemire@@FastPFor": "C++", "Blosc@@c-blosc": "C", "andrewtrotman@@JASSv2": "C++", "cburstedde@@libsc": "C", "P0cL4bs@@3vilTwinAttacker": "Python", "ewa@@802.11-data": "Python", "CTU-IIG@@802.11p-wireless-regdb": "Python", "flupzor@@80211-fun": "Python", "mcgrof@@acs": "C", "SaltwaterC@@aircrack-db": "Smarty", "aircrack-ng@@aircrack-ng": "C", "Frozenbox@@airmode": "Python", "ivanlei@@airodump-iv": "Python", "zhovner@@airport-sniffer": "Python", "trou@@airscan": "C", "Crypt0s@@airview": "Python", "balle@@airxploit": "Python", "0x90@@auto-reaver": "Shell", "atechdad@@badkarma": "Python", "0x90@@banjax": "C++", "enkore@@basiciw": "C", "mothran@@bunny": "Python", "xiao106347@@chap2asleap": "Python", "tjetzinger@@CloudCrackInstaller": "Shell", "0x90@@cookie-monster": "Python", "4ZM@@cornuprobia": "Python", "calvinmetcalf@@COUCHFI": "Python", "mcgrof@@crda": "C", "eldraco@@darm": "Python", "mfontanini@@dot11decrypt": "C++", "timow@@dot11er": "Python", "DepthDeluxe@@dot11sniffer": "Python", "Raiton@@eap_detect": "Python", "securestate@@eapeak": "Python", "brav0hax@@easy-creds": "Shell", "DanMcInerney@@fakeAP": "Python", "0x90@@fern-wifi-cracker": "Python", "mike-albano@@frame-randomizer": "Python", "xtr4nge@@FruityWifi": "PHP", "lostincynicism@@FuzzAP": "Python", "szehl@@FWAP": "C", "nseetharaman@@GrapplingHook": "Python", "jedahan@@haiku-wifi": "Python", "br101@@horst": "C", "nims11@@hostapd.py": "Python", "OpenSecurityResearch@@hostapd-wpe": "<PERSON><PERSON><PERSON>", "davux@@huawei_wifi": "Python", "hubert3@@iSniff-GPS": "Python", "atimorin@@karma": "TeX", "andreagrandi@@kismeth2earth": "Python", "DanMcInerney@@LANs.py": "Python", "adambregenzer@@libcowpatty": "C", "tgraf@@libnl": "C", "0x90@@lorcon": "C", "kvalo@@ath10k-firmware": "<PERSON><PERSON>", "rboninsegna@@ath9k-4W-patch": null, "doom5@@ath9k_ath5k_full_permissive_unlock_all_channels.patch": null, "Noltari@@ath9k_caldata": "C", "erikarn@@ath9k-docs": null, "LorenzoBianconi@@ath9k_dynack": null, "valkjsaaa@@ath9k_grt": "C", "abnarain@@mac-analyzer": "C", "hughobrien@@ath9k-nav": "C", "aelam@@ath9k": "C++", "kazikcz@@ath9k-spectral-scan": "C", "andyvand@@AtherosROMKit": "C", "simonwunderlich@@FFT_eval": "C", "qca@@open-ath9k-htc-firmware": "C", "mcgrof@@qca-swiss-army-knife": "C", "thuehn@@RegMon": "R", "bcopeland@@speccy": "Python", "0x90@@bcmon": "C", "the-darkvoid@@BrcmPatchRAM": "C++", "timofurrer@@broadcom-wl-monitormode": "C", "ransford@@phystats": "R", "RehabMan@@OS-X-Realtek-Network": "C++", "gnab@@rtl8812au": "C", "0x0d@@lrc": "C", "jeffThompson@@MappingWirelessNetworks": "Python", "vanhoefm@@modwifi": "Shell", "6e726d@@Native-WiFi-API-Beacon-Sniffer": "C++", "konker@@openrtls": "Python", "0x90@@openwips-ng": "C", "securitytube@@pcap2xml": null, "wiire@@pixiewps": "C", "jjb3tee3@@probemon": "Python", "SilverFoxx@@PwnSTAR": "Shell", "0x90@@py80211": "Python", "0x90@@pykismetstats": "Python", "tom5760@@pylorcon2": "Python", "bcopeland@@python-radiotap": "Python", "6e726d@@PyWiWi": "Python", "AlexanderSelzer@@Radioparse": "JavaScript", "gabrielrcouto@@reaver-wps": "C", "t6x@@reaver-wps-fork-t6x": "C", "zackiles@@Rspoof": "Python", "AravinthPanch@@rssi": "HTML", "rpp0@@scapy-fakeap": "Python", "azz2k@@scapy-rssi": "Python", "tuomasb@@scapy-survey": null, "rs91092@@Scapy-wireless-scanner": "Python", "0x90@@skybluetero": "Python", "andrewhilts@@snifflab": "Python", "catalyst256@@sniffMyPackets": "Python", "sensepost@@snoopy-ng": "Python", "SYWorks@@waidps": "Python", "0x0d@@wallofshame": "Python", "hack1thu7ch@@WAPMap": "Python", "0x90@@warcarrier": "<PERSON><PERSON>", "catalyst256@@Watcher": "Python", "rockymeza@@wifi": "Python", "bertabus@@wifi-contour": "Python", "abnarain@@wifi-dump-analysis": "Python", "abnarain@@wifi_dump_parser-v3": "Python", "SYWorks@@wifi-harvester": "Python", "beaugunderson@@wifi-heatmap": "Python", "dixel@@wifi-linux": "Python", "clockfort@@wifi-locator": "C", "genekogan@@wifi_geolocation": "Python", "utexas-air-fri@@wifi_localization": "Python", "twitchyliquid64@@wifidec": "Python", "DanMcInerney@@wifijammer": "Python", "flankerhqd@@wifimonster": "Python", "sophron@@wifiphisher": "Python", "cyberpython@@WifiScanAndMap": "JavaScript", "viscousliquid@@wifitap": "Python", "derv82@@wifite": "Python", "aanarchyy@@wifite-mod-pixiewps": "Python", "mitdbg@@wifivis": "Python", "meeuw@@wifiwho": "Python", "LionSec@@wifresti": "Python", "0x90@@wifuzz": "Python", "0xd012@@wifuzzit": "Python", "cmpxchg8@@wifi_decode": "C++", "dioh@@wipi": "Python", "toleda@@wireless_half-mini": null, "SYWorks@@wireless-ids": "Python", "stef@@wireless-radar": "Python", "0x90@@wireless-regdb": "Python", "gauravpatwardhan@@Wireless-Sniffer": "C", "acidprime@@WirelessConfig": "Python", "sajjanbh@@WLAN-Monitoring": "Python", "0x90@@wlan-pos": "Python", "hughobrien@@wlan-stats": "Python", "securestate@@wmd": "Python", "wmon@@wmon": "C++", "SYWorks@@wpa-bruteforcer": "Python", "dxa4481@@WPA2-HalfHandshake-Crack": "Python", "anyfi@@wperf": "C", "devttys0@@wps": "Python", "0x90@@wps-scripts": "Shell", "ml31415@@wpscrack": "Python", "wraith-wireless@@wraith": "Python", "Nan-Do@@wspy": "C", "cozybit@@wtf": "Python", "jenssegers@@RTL8188-hostapd": "C", "fabiocannizzo@@monit-hostapd": "C++", "joseccnet@@DebAPoint": "Shell", "xtr4nge@@hostapd-karma": "C", "coolshou@@wifisoftap": "C++", "TarlogicSecurity@@hostapd-wpe-openwrt": "C", "Bananian@@hostapd-ap6210": "C", "bagsofnarcissism@@RougeDetection": "Python", "vanhoefm@@apbleed": "C", "otherview@@IndoorPositionr": "JavaScript", "hydrogen18@@probecap": "Python", "sh0@@airown": "C++", "derosier@@packetvector": "C", "AndrewGomes@@ADSLPT-WPA": "Python", "atiti@@airodump-logger": "Python", "Demontager@@txpower": "Shell", "Slickness@@pythonAir": "HTML", "pritambaral@@hostapd-rtl871xdrv": null, "jekader@@hostapd-rtl": "C", "shagrath89m@@WPA_DECRYPTION_MPI": "C", "JackAHall@@Auto-Airodump-NG": "Python", "fopina@@reaver-webui": "Python", "NORMA-Inc@@AtEar": "JavaScript", "vnik5287@@wpa-autopwn": "Python", "4ZM@@datasamalen": "Python", "jahrome@@scapybase": "Python", "wshen0123@@mitm_rogue_wifi_ap": null, "vollero@@openCAPWAP": "C", "Marchrius@@RT73-USB-Wireless-": "C", "zeplios@@atheros-patches": null, "mickflemm@@ath-info": "C", "joycechu@@fixsum": null, "Noltari@@atheros_library": "C", "mzhaase@@py_DD_WRT_Remote_Mac_Adder": "Python", "cl4u2@@ap51flash": "C", "olerem@@ath9k-htc-firmware-blob": null, "vanhoefm@@modwifi-tools": "C++", "hellais@@VX": "Python", "Crypt0s@@PiWAT": "JavaScript", "Jtfinlay@@Burgess": "JavaScript", "jesseDtucker@@aircrack-bws": "C", "openwrt@@mt76": "C", "kuba-moo@@mt7601u": null, "kaloz@@mwlwifi": "C", "abnarain@@wifi_beacons": "C", "abnarain@@oculus": "C", "abnarain@@covert_channel": "C", "abnarain@@wifi_dump-tmpfs": "C", "wlanslovenija@@wireless-info": "C", "fhector@@make-a-new-mac80211-to-wirelessAP": "C", "sheenhx@@setbssid": "C", "iitis@@iitis-generator": "C", "Kanel@@CloudMAC-Misc": "Python", "romebop@@wi-finder": "HTML", "jgumbley@@mr-nosy": "Python", "DHNishi@@wifitracker": "Python", "rednaks@@airfree-wt": "Python", "terbo@@sigmon": "Python", "sensepost@@WiFi-Rifle": "Python", "frankxu2004@@emc-contest-data-visualization": "JavaScript", "5alt@@lianwifi": "Python", "hbock@@libairpcap-nl": "C", "bakerface@@wireless-tools": "JavaScript", "saintkepha@@airtraf": "C", "paulpatras@@cac": "C", "paulpatras@@madwifi-be": "C", "jiixyj@@list-aps": "Python", "brycethomas@@liber80211": null, "tillwo@@80211ping": "C", "tillwo@@wifi-beeper": "C", "dutchcoders@@gopacket-80211": "Go", "travisgoodspeed@@80211scrambler": "Verilog", "phildom@@80211mgmtDoS": "C", "arend@@brcm80211-trace-cmd": "Python", "MinimumLaw@@80211_raw": "C", "allanmatthew@@80211p_raw": "C", "enukane@@pcap80211analyzer": "<PERSON>", "mengning@@agentapd": "C", "mengning@@cloudap": "C", "wifidog@@wifidog-gateway": "C", "mengning@@remoteapd": "C", "rhodey@@frame-utils.js": "JavaScript", "darizotas@@ciscowebauth": "Python", "bcopeland@@ath10k-mesh": "C", "bcopeland@@android_packetspammer": "C", "bcopeland@@android_iw": "C", "juzna@@packet-injector": "JavaScript", "sail308@@fRobot": "C++", "olanb7@@rogueDetect": "C++", "veenfang@@naive_project": null, "gvnn3@@PCS": "Python", "flupzor@@packetparser": "Python", "derv82@@wifite2": "Python", "derv82@@wpacrack": null, "yunus@@Hostapd-with-WebID": "C", "younextvictim@@Wps-Ultimate-Cracker": "Shell", "SilentGhostX@@HT-WPS-Breaker": "Shell", "kriswebdev@@android_reaver-wps": "C", "deoxxa@@reaver-ui": "JavaScript", "sigginet@@greaver": "Python", "phpreaver@@phpreaver": "PHP", "nxxxu@@AutoPixieWps": "Python", "xXx-stalin-666-money-xXx@@penetrator-wps": "C", "jgilhutton@@pyxiewps": "Python", "bendemott@@captiveportal": "Python", "nathanshimp@@AirLibre": "Python", "hiteshchoudhary@@Airvengers": "Python", "awhitehatter@@fake-ap3.py": "Python", "ahhh@@Wifi_Trojans": "PowerShell", "agnostino@@wireless_RSSI": "C", "bram-glasswall@@rogueap": "Python", "dweinstein@@dockerfile-hostapd": null, "lgrangeia@@cupid": null, "NerdyProjects@@hostapd-wpe-extended": "Smarty", "swaminathanvasanth@@hostapd-acs": "C", "kurokid@@connme": "Python", "foosel@@wifi-ap": "Python", "hatRiot@@zarp": "Python", "moha99sa@@EvilAP_Defender": "Python", "ragmondo@@BitcoinWifi": "HTML", "jazoza@@milicone": "Pure Data", "jedivind@@gtaiad": "C", "JackieXie168@@como": "C", "paulpatras@@madwifi-hopping": "C", "freifunk@@openwifimap-api": "Python", "ogreworld@@PyScapy": "Python", "jedivind@@aircrack-ng-iphone": "C", "hacker404@@AIRBASE-NG-SSLSTRIP-AIRSTRIP-": "Shell", "d4rkcat@@killosx": "Shell", "d4rkcat@@HandShaker": "Shell", "d4rkcat@@apflood": "Shell", "shpala@@MulitFi": "Python", "jakev@@mitm-helper-wifi": "Python", "0x90@@hotspotd": "Python", "mehdilauters@@wifiScanMap": "Python", "pupi1985@@marfil": "PHP", "sinistermachine@@sly-fi": "Shell", "CoreSecurity@@wiwo": "Python", "bennett-elder@@curscout": "Python", "XayOn@@pyrcrack": "Python", "joshvillbrandt@@wireless": "Python", "kornysietsma@@osx-wifi-scan": "Clojure", "binarymaster@@3WiFi": "PHP", "P0cL4bs@@WiFi-Pumpkin": "Python", "v1s1t0r1sh3r3@@airgeddon": "Shell", "d33tah@@call-for-wpa3": null, "Vivek-Ramachandran@@wi-door": "C++", "Alf-Alfa@@uploadwpa": "C++", "tuter@@monmob": "Python", "moepinet@@moepdefend": "C", "ymah@@kismet-fork": "JavaScript", "0x0d@@hijack": "Python", "maroviher@@airodump_mod": "C", "aanarchyy@@bully": "C", "0x90@@crda-ct": "C", "erstrom@@iwraw": "C", "wshen0123@@mitm-rogue-WiFi-AP": null, "DE-IBH@@mupe": null, "doctaweeks@@ap-notify": "C", "AdamKnube@@haircrack": "Python", "oblique@@create_ap": "Shell", "almondg@@win32wifi": "Python", "wraith-wireless@@captiv8": "Python", "wraith-wireless@@itamae": "Python", "rpp0@@aggr-inject": "Python", "rpp0@@peapwn": "C", "kismetwireless@@kismet": "C++", "PaulMcMillan@@kismetclient": "Python", "sensepost@@mana": "HTML", "smoz1986@@WHAT-PRO": "C", "bwoolf1122@@TCP-SeqNum": "C", "justinbeatz@@Armory": null, "Wifimon@@Wifimon": "C", "CTU-IIG@@802.11p-iw": "C", "bastibl@@gr-ieee802-11": "C++", "zitouni@@gr-ieee80211ah": "C++", "6e726d@@WIG": "Python", "adelashraf@@cenarius": "Python", "weaknetlabs@@libpcap-80211-c": "C", "chillancezen@@mac80211-user": "C", "ph4r05@@kismet-deauth-wpa2-handshake-plugin": "Python", "Kismon@@kismon": "Python", "bmegli@@wifi-scan": "C", "jessebrizzi@@USB-WiFi-Autoreset": "C", "simonwunderlich@@wifi_statistics": "C", "sa7mon@@wpe-parse": "Shell", "JPaulMora@@Pyrit": "Python", "seemoo-lab@@bcm-public": "C", "resfi@@resfi": "C", "seemoo-lab@@bcm-rpi3": "C", "xdavidhu@@mitmAP": "Python", "violentshell@@Rollmac": "Python", "hd1de@@802.11r": null, "gat3way@@AirPirate": "Java", "fabriziogiuliano@@react80211": "Python", "wirelesshack@@DeSniffer": "Python", "PeppeMir@@802.11i-Analysis": null, "ndyakov@@dw": "C", "dcrisan@@WiFi-802.11-Demo-Sniffer": "Python", "b00sti@@WiFi-Analyzer": "Java", "haknmcaobin@@Dot11Attacker": "Python", "teddyyy@@libfcap": "C", "CTurt@@NiFiCapture": "C", "claymichaels@@disable-802.11b-snmp": "Python", "jkingsman@@classIV": null, "SamClarke2012@@SSIDentity": "C", "iceowl@@801.11Project": "C++", "sbxfc@@wlan-macos": "C", "scottjpack@@kismet_baro": "Python", "observ3r@@wobs": "C", "pasdesignal@@autokwaker": "Python", "wouterbudding@@ScapyGELFtoGraylog2": "Python", "Wi5@@wi5-aggregation": "C", "esc0rtd3w@@wifi-hacker": "Shell", "smoz1986@@WHAT-Pi": "C", "smoz1986@@WHAT": "C", "LorenzoBianconi@@ath_spectral": "C++", "trama@@ieiit-kbp4111-80211": "C", "skullkey@@wbc-utils": "C", "hkparker@@Wave": "Go", "Konsole512@@Crippled": "Python", "risataimpt@@hostapd-wpe": "Python", "sa7mon@@parsecaps": "Python", "wouter-glasswall@@rogueap": "Python", "sa7mon@@startools": "C", "Bob-King@@WifiTrafficAnalyzer": "Java", "edelahozuah@@awesome-wifi-security": null, "UtkMSNL@@Side-channel": "C++", "UtkMSNL@@WiFi-scheduling": "C++", "chrisk44@@Hijacker": "Java", "Yokai-Seishinkage@@Auto-mdk3_v02": "Shell", "Andy-Maclachlan@@mass-deauth": "Shell", "ytisf@@mdk3_6.1": "C", "dinosec@@iStupid": "Python", "shunghsiyu@@mass-deauth-attack": "Python", "probr@@probr-analysis": "JavaScript", "probr@@probr-core": "JavaScript", "h3pr5tq@@get-rssi": "C", "h3pr5tq@@accumulation-rssi": "C", "yzfedora@@geowifi": "C", "substack@@wit": "JavaScript", "R2dR@@linux-wifi-tools": "Shell", "dcorking@@linux-wifi-reconnector": "Python", "br101@@libuwifi": "C", "s7jones@@Wifi-Signal-Plotter": "Python", "anburocky3@@wime": "Python", "phr34k0@@wirelessjammer": "Python", "bliz937@@WiPy": "Python", "experimental-platform@@platform-hostapd": "Go", "mytechia@@linux_wifi_config": "Python", "maroviher@@airodump_mar_attack": "C", "musket33@@Pwnstar9.0-for-WPA-Phishing": null, "cyrus-and@@zizzania": "C", "singe@@wifi-frequency-hacker": null, "kootenpv@@access_points": "Python", "kootenpv@@whereami": "Python", "violentshell@@rollmac": "Python", "dhalperi@@linux-80211n-csitool": "C", "Viralmaniar@@Wifi-Dumper": "Python", "Revimal@@WifiDeauth": "C++", "historypeats@@wpa2hc": "Python", "rgupta9@@WIRELESSINFO": "Python", "nipunjaswal@@Wireless-forensics-framework": "Python", "bradleykirwan@@disassociatedWiFi": "C++", "inguardians@@VistaRFmon": null, "rajkotraja@@wifiJamMac": "Objective-C", "anchigel@@ee202a": "Python", "nosmo@@Eircog": "Python", "Nick-the-Greek@@Aerial": "Shell", "rringler@@kali-wireless": null, "mousam05@@connect-wifi": "Shell", "siriuxy@@wifi_based_population_estimator": "Python", "llazzaro@@python3-wifi": "Python", "cnlohr@@wifirxpower": "C", "HalfdanJ@@ofxSniffer": "C++", "dave5623@@wifi_monitor": "JavaScript", "Geovation@@wifispy": "Python", "sillent@@RadiusStats": "<PERSON><PERSON><PERSON>", "dappiu@@rifsniff": "Python", "baggybin@@RogueDetection": "Python", "securitytube@@wifiscanvisualizer": null, "unixpickle@@JamWiFi": "Objective-C", "jordan-wright@@python-wireless-attacks": "Python", "axilirator@@cherry": "JavaScript", "Mi-Al@@WiFi-autopwner": "Shell", "initbrain@@Python-Wi-Fi-Positioning-System": "Python", "schollz@@find-lf": "Go", "faizann24@@wifi-bruteforcer-fsecurify": "Java", "DanMcInerney@@WPSmash": "Python", "kylemcdonald@@FreeWifi": "Python", "cyclo-techtwister@@BRUTEIT": "Shell", "musket33@@Pwnstar9-0-for-Kali-2016R1-2": null, "IGRSoft@@KisMac2": "Objective-C", "kbeflo@@wifite-openwrt": null, "mubix@@osx-wificleaner": "Python", "hkm@@whoishere.py": "Python", "Brom95@@WiBroute": "Python", "killswitch-GUI@@Domain-WIFILocate": "PowerShell", "B4ckP0r7@@RogueSploit": "Shell", "xdavidhu@@probeSniffer": "Python", "chrizator@@netattack": "Python", "ZerBea@@hcxtools": "C", "brannondorsey@@wifi-cracking": null, "MisterBianco@@BoopSuite": "Python", "viluhaxor@@wifikicker": "Python", "MarkLalor@@WiFiStat": "Python", "WiPi-Hunter@@PiDense": "Python", "Manouchehri@@wifi-txpower-unlocker": "Shell", "ICSec@@packetEssentials": null, "ICSec@@pyDot11": "Python", "ICSec@@airpwn-ng": "Python", "wavestone-cdt@@wavecrack": "Python", "WiPi-Hunter@@PiKarma": "Python", "WiPi-Hunter@@PiSavar": "Python", "WiPi-Hunter@@PiFinger": "Python", "360PegasusTeam@@WiFi-Miner-Detector": "Python", "ghostop14@@sparrow-wifi": "Python", "mame82@@P4wnP1_WiFi_covert_channel_client": "C#", "hash3liZer@@WiFiJammer.py": "Python", "hash3liZer@@WiFiBroot": "Python", "hash3liZer@@airpydump": "Python", "s0lst1c3@@eaphammer": "C", "soxrok2212@@PSKracker": "C", "AresS31@@wirespy": "Shell", "RealEnder@@dwpa": "PHP", "wifiphisher@@roguehostapd": "C", "SkypLabs@@probequest": "Python", "InfamousSYN@@rogue": "Python", "vanhoefm@@krackattacks-scripts": "C", "kbeflo@@evilportals": "CSS", "roglew@@wifikill": "Python", "aircrack-ng@@mdk4": "C", "kristate@@krackinfo": null, "securingsam@@krackdetector": "Python", "IronMage@@KRACKDetection": "Python", "vanhoefm@@krackattacks-poc-zerokey": "C", "Programmuser@@Krack-wps": "C", "omaidf@@KRACK-toolkit": "C", "jiansiting@@WPA2-KRACK": "Python", "h4ckzard@@wpseyes": "Python", "grazianomarallo@@Thesis": "C", "BroadbentT@@PCAP-CRACKER": "Python", "p5panam@@krackattacks-Test-Vulnerability": "Python", "lucascouto@@krackattack-all-zero-tk-key": "C", "fwalloe@@KrackPlus": "C", "Guppster@@KrackAttack": "JavaScript", "Hackndo@@krack-poc": "Python", "chinatso@@KRACK": "Python", "adde88@@krackattacks-pineapple": "C", "nuncan@@wifite2mod": "Python", "random-robbie@@wifite2-docker": "Dockerfile", "zonesecure@@wifiteintaller": "Python", "awesome-pentest-gadgets@@WiFite2-RPi3-nexmon": "Python", "********SECURITY@@CODEX": "Python", "KhasMek@@nabui": "Python", "mrusme@@ninjaberry": "Python", "bootes-void@@easy-bettercap": "Python", "f1rsty@@piwifipineapple": "Shell", "charlieporth1@@PINEAPPLE": null, "BadAppsDevelopment@@Wifi-Cracker": null, "drygdryg@@OneShot": "Python", "t3chnocat@@easy-hcx": "Shell", "davidetestoni@@pmkid-extractor": "Python", "ricardojoserf@@wpa2-enterprise-attack": "Shell", "c0mix@@WirelessMayhem": "Python", "open-sdr@@openwifi": "C", "sensepost@@hostapd-mana": "C", "eye9poob@@Wifi-monitor": "Python", "wraith-wireless@@PyRIC": "Python", "Nirei@@geoprobe": "Python", "adriangranados@@wifiexplorer-sensor": "Python", "mgp25@@Probe-Hunter": "Python", "NoobieDog@@Peanuts": "Python", "vanhoefm@@krackattacks": "HTML", "Squuv@@WifiBF": "Python", "P0cL4bs@@wifipumpkin3": "Python", "pbalogh-sa@@wids-wips": "C", "anotherik@@RogueAP-Detector": "Python", "teambi0s@@Wall-of-Shame": "CSS", "adde88@@ManaToolkit": "HTML", "stavinski@@etd": "Python", "MA24th@@WiFiHunter": "Python", "overmod1@@hcx-wifite": "Shell", "d30sa1@@krackattacks-test-ap-ft": "Python", "FXShu@@Wireless_peeker": "C", "4nth0nySLT@@AircrackPy": "Python", "dredge17@@wpasnff": "Shell", "valvesss@@invasit-network": "Shell", "rofl0r@@wpakey": "C", "Leviathan36@@wifibang": "Shell", "techge@@eewids": "Python", "sethleedy@@Auto-Besside-Capturer": "Shell", "phalloc@@phalloc-sniffer": "C++", "cjcase@@beaconleak": "Python", "s0m3-1@@wifiBuddy": "Python", "philcryer@@wpa2own": "Shell", "ghsi10@@capbreaker": "Java", "SecHeart@@Dot11Hunter": "Python", "sundaysec@@anubis": "Python", "mvondracek@@wifimitm": "Python", "rpp0@@wifi-mac-tracking": "Python", "vtr0n@@FakeAP": "Python", "jduck@@jfap": "C", "IsmaelRLG@@pyfi": "Python", "adriangranados@@dot11anonymizer": "Python", "argaon@@wifi_landing_2": "C++", "WeareJoker@@deauth_dot11decrypt": "C++", "zachMelody@@scapy-dot11-toolkit": "Python", "Inkln@@Dot11Monitoring": "JavaScript", "violentshell@@widowmaker": "Python", "pinecone-wifi@@pinecone": "Python", "scriptedp0ison@@FakeAuth": "Python", "chinarulezzz@@refluxion": "HTML", "NickSanzotta@@WiFiSuite": "Python", "jantman@@python-wifi-survey-heatmap": "Python", "ConnectBox@@wifi-configurator": "Python", "staz0t@@hashcatch": "Shell", "lennartkoopmann@@nzyme": "Java", "ricardojoserf@@wifi-pentesting-guide": "Python", "raw-packet@@raw-packet": "Python", "RavSS@@Curfew": "C", "Perdu@@wombat": "Python", "Perdu@@panoptiphone": "Python", "mcgregol@@split": "Shell", "redoverture@@PACE": "Python", "proxyanon@@WireCrack": "Python", "ajmwagar@@boa": "Python", "harshadms@@ESSID-Uncover": "Python", "teknoraver@@wifight": "C", "pmsosa@@EagleEye": "C++", "KeyofBlueS@@airgeddon-plugins": "Shell", "vy@@wapi": "C", "R33V@@wifite3": null, "kimocoder@@realtek_rtwifi": "C", "1N3@@PRISM-AP": "Shell", "websploit@@websploit": "Python", "ankit0183@@Wifi-Hacking": "Python", "bavxhack@@wlanMonGenerator": "Python", "brangerbriz@@wifi-data-safari": "JavaScript", "jankubatt@@wifite-patcher": "Python", "rkhunt3r@@manafix": "Shell", "KatzeMau@@wifite2-requirements": "Shell", "Bojak4616@@Mobile_Phone_Tracking": "Python", "GlennPegden2@@probe-stalker": "Python", "stefankueng@@sktoolslib": "C++", "codeplea@@tinyexpr": "C", "ScintillaOrg@@lexilla": "C++", "freedesktop@@uchardet": "C++", "Unity-Technologies@@HLSLcc": "C++", "aras-p@@glsl-optimizer": "C++", "aras-p@@hlsl2glslfork": "C++", "aras-p@@smol-v": "C++", "google@@clspv": "LLVM", "tomleavy@@crypt_blowfish": "C", "tomleavy@@libscrypt": "C", "protobuf-c@@protobuf-c": "C++", "tomleavy@@cspec": "C", "jrfonseca@@libdwarf": "C", "camgunz@@cmp": "C", "Intel-HLS@@htslib": "C", "Intel-HLS@@TileDB": "C++", "miloyip@@rapidjson": "C++", "juribeparada@@STM32F4XX_Lib": "C", "juribeparada@@STM32F7XX_Lib": "C", "shawnchain@@STM32F10X_Lib": "C", "washingtondc-emu@@sh4asm": "C", "aquynh@@capstone": "C", "ocornut@@imgui": "C++", "libevent@@libevent": "C", "AirGuanZ@@imgui-filebrowser": "C++", "lzfse@@lzfse": "C", "card-io@@card.io-dmz": "C++", "alliedmodders@@amtl": "C++", "scoopr@@vectorial": "C++", "google@@flatui": "C++", "google@@fplutil": "Python", "googlesamples@@cardboard-java": null, "google@@pindrop": "C++", "google@@mathfu": "C++", "google@@motive": "C++", "google@@breadboard": "C++", "google@@scene_lab": "C++", "adah1972@@libunibreak": "C", "google@@flatbuffers": "C++", "eigenteam@@eigen-git-mirror": "C++", "SuperV1234@@vrm_cmake": "CMake", "Jorgen-VikingGod@@Qt-Frameless-Window-DarkStyle": "C++", "Dax89@@QHexView": "C++", "dr-soft@@miniaudio": "C", "Kolkir@@plotcpp": "C++", "dmlc@@mshadow": "C++", "ben-strasser@@fast-cpp-csv-parser": "C++", "QuantStack@@xtl": "C++", "QuantStack@@xtensor": "C++", "QuantStack@@xtensor-blas": "C++", "Tencent@@rapidjson": "C++", "phhusson@@sepolicy-inject": "C", "OpenXRay@@LuaJIT": "C", "OpenXRay@@luabind-deboostified": "C++", "OpenXRay@@GameSpy": "C", "GPUOpen-LibrariesAndSDKs@@AGS_SDK": "C++", "alexgdi@@lzo": "C", "xiph@@vorbis": "C", "xiph@@ogg": "C", "xiph@@theora": "C", "OpenXRay@@libjpeg": "C", "OpenXRay@@BugTrap": "C++", "weidai11@@cryptopp": "C++", "OpenXRay@@OpenAutomate": "C++", "OpenXRay@@FreeMagic": "C++", "g-truc@@gli": "C++", "OpenXRay@@mimalloc": "C", "DLTcollab@@sse2neon": "C", "joyent@@libuv": "C", "benhoyt@@inih": "C", "kinetiknz@@cubeb": "C++", "MerryMage@@dynarmic": "C++", "citra-emu@@ext-soundtouch": "C++", "citra-emu@@ext-libressl-portable": "C", "libusb@@libusb": "C", "discord@@discord-rpc": "C++", "KhronosGroup@@Vulkan-Headers": "C++", "ReinUsesLisp@@sirit": "C++", "yuzu-emu@@mbedtls": "C", "herumi@@xbyak": "C++", "xiph@@opus": "C", "libsdl-org@@SDL": "C", "yhirose@@cpp-httplib": "C++", "maidsafe@@MaidSafe-API": "C++", "maidsafe@@MaidSafe-Common": "C++", "maidsafe@@MaidSafe-Drive": "C++", "maidsafe@@MaidSafe-Encrypt": "C++", "maidsafe@@MaidSafe-Network-Filesystem": "C++", "maidsafe@@MaidSafe-Passport": "C++", "maidsafe@@MaidSafe-Routing": "C++", "maidsafe@@MaidSafe-Vault": "C++", "maidsafe@@MaidSafe-Vault-Manager": "C++", "maidsafe@@MaidSafe-Launcher": "C++", "maidsafe@@MaidSafe-CRUX": "C++", "wbenny@@DetoursNT": "C++", "processhacker@@phnt": "C", "nullgemm@@argoat": "C", "nullgemm@@configator": "C", "nullgemm@@dragonfail": "C", "nullgemm@@termbox_next": "C", "angt@@mud": "C", "angt@@argz": "C", "smistad@@OpenCLUtilities": "C++", "kohler@@masstree-beta": "C++", "yyzybb537@@libgo": "C++", "horsicq@@Detect-It-Easy": "JavaScript", "horsicq@@Formats": "C++", "horsicq@@SpecAbstract": "C++", "horsicq@@StaticScan": "C++", "horsicq@@XArchive": "C++", "horsicq@@XQwt": "CMake", "horsicq@@XOptions": "C++", "horsicq@@XStyles": null, "horsicq@@XTranslation": null, "horsicq@@XDEX": "C++", "horsicq@@FormatDialogs": "C++", "horsicq@@FormatWidgets": "C++", "horsicq@@Controls": "C++", "horsicq@@XMemoryMapWidget": "C++", "horsicq@@XEntropyWidget": "C++", "horsicq@@XDisasm": "C++", "horsicq@@XCapstone": "C++", "horsicq@@XHashWidget": "C++", "horsicq@@die_script": "C++", "horsicq@@die_widget": "C++", "horsicq@@nfd_widget": "C++", "horsicq@@archive_widget": "C++", "horsicq@@XMIME": "C++", "horsicq@@XSingleApplication": "C++", "horsicq@@XMIMEWidget": "C++", "horsicq@@XHexView": "C++", "horsicq@@XDisasmView": "C++", "horsicq@@XGithub": "C++", "horsicq@@XShortcuts": "C++", "horsicq@@XHexEdit": "C++", "horsicq@@signatures": null, "horsicq@@XDemangle": "C++", "horsicq@@XDemangleWidget": "C++", "horsicq@@XLLVMDemangler": "QMake", "horsicq@@build_tools": "Shell", "horsicq@@XCppfilt": "C++", "horsicq@@XDynStructs": null, "horsicq@@XDynStructsEngine": "C++", "horsicq@@XDynStructsWidget": "C++", "horsicq@@XFileInfo": "C++", "projectatomic@@bubblewrap": "C", "Friendly0Fire@@imgui": "C++", "brofield@@simpleini": "C++", "Cyan4973@@xxHash": "C", "nlohmann@@json": "C++", "easz@@cpp-semver": "C++", "gw2-addon-loader@@loader-core": "C++", "gw2-addon-loader@@d3d9_wrapper": "C++", "Friendly0Fire@@ziplib": "C", "Friendly0Fire@@minhook": "C", "Friendly0Fire@@tinyxml2": "C++", "juliettef@@IconFontCppHeaders": "C", "FrodeSolheim@@capsimg": "C++", "RobSmithDev@@FloppyDriveBridge": "C++", "leethomason@@tinyxml2": "C++", "hoshi10@@astar-algorithm-cpp": "C++", "emilk@@emilib": "C++", "ARMmbed@@mbedtls": "C", "cypresssemiconductorco@@mtb-pdl-cat1": "C", "cypresssemiconductorco@@psoc6pdl": "C", "cypresssemiconductorco@@retarget-io": "C", "cypresssemiconductorco@@core-lib": "C", "cypresssemiconductorco@@psoc6hal": "C", "cypresssemiconductorco@@cy-mbedtls-acceleration": "C", "NordicSemiconductor@@cddl-gen": "C", "espressif@@esp-idf": "C", "cpvrlab@@ImagePlay_lib": "CMake", "JuliaStrings@@utf8proc": "C", "g-truc@@glm": "C++", "miniupnp@@miniupnp": "C", "nemtrif@@utfcpp": "C++", "past-due@@launchinfo": "C++", "google@@re2": "C++", "past-due@@EmbeddedJSONSignature": "C++", "HowardHinnant@@date": "C++", "Warzone2100@@data-texpages": null, "Warzone2100@@data-music": null, "past-due@@discord-rpc": "C++", "SRombauts@@SQLiteCpp": "C", "cameron314@@readerwriterqueue": "C++", "veandco@@go-sdl2-examples": null, "nnen@@doxygen-theme": "CSS", "zaphoyd@@websocketpp": "C++", "nayuki@@QR-Code-generator": "Java", "mid-kid@@CakeBrah": "C", "mid-kid@@CakeHax": "C", "Reisyukaku@@loader": "C", "matcornic@@hugo-theme-learn": "HTML", "CalcProgrammer1@@KeyboardVisualizerVCUI_Release": null, "solettaproject@@duktape-release": "C", "01org@@tinycbor": "C", "mavlink@@c_library": "C", "OpenInterConnect@@IoTDataModels": null, "xxxajk@@RTClib": "C", "iondbproject@@planck-unit": "C", "iondbproject@@utility-scripts": "Shell", "pyca@@cryptography": "Python", "zyantific@@zydis": "C", "asmjit@@asmjit": "C++", "x64dbg@@Translations": "Batchfile", "x64dbg@@btparser": "C++", "x64dbg@@deps": null, "x64dbg@@zydis": "C++", "nemequ@@liblzf": "C", "nemequ@@doboz": "C", "svn2github@@fastlz": "C", "nemequ@@lzjb": "C", "tinycthread@@tinycthread": "C", "davidcatt@@FastARI": "C", "google@@gipfeli": "C++", "richgel999@@lzham_codec_devel": "C++", "google@@brotli": "C", "zpaq@@zpaq": "C++", "richox@@libzling": "C++", "Cyan4973@@zstd": null, "coderforlife@@ms-compress": "C++", "IlyaGrebnov@@libbsc": "C", "centaurean@@density": "C", "fusiyuan2010@@CSC": "C++", "nemequ@@bzip2": "C", "nemequ@@lzo": "C", "google@@snappy": "C++", "ShaneWF@@wflz": "C", "mbitsnbites@@liblzg": "C", "Dead2@@zlib-ng": null, "jibsen@@brieflz": "C", "atomicobject@@heatshrink": "C", "ebiggers@@libdeflate": "C", "jibsen@@parg": "C", "win-iconv@@win-iconv": "C", "richgel999@@miniz": "C", "nemequ@@munit": "C", "nemequ@@hedley": "C++", "cnlohr@@esp82xx": "C", "lrse@@bow_vocabularies": null, "Pulse-Eight@@platform": "C++", "Pulse-Eight@@libcec-support": "Batchfile", "Pulse-Eight@@cec-dotnet": "C#", "fanout@@common": "C++", "jkarneges@@qzmq": "C++", "jbarczak@@glsl-optimizer": "C++", "jbarczak@@GLSlang": "C++", "arvidn@@libtorrent": "C++", "cpp-netlib@@cpp-netlib": "C++", "hadouken@@webui": "JavaScript", "ThrowTheSwitch@@CMock": "C", "ThrowTheSwitch@@Unity": "C", "madsen@@free-getopt": "C++", "ahtn@@avr-makefile": "<PERSON><PERSON><PERSON>", "ahtn@@kp_boot_32u4": "C", "ahtn@@xusb-boot": "C", "ahtn@@nrf24lu1p-512-bootloader": "Assembly", "VerySleepy@@wine": "C", "jrfonseca@@drmingw": "C++", "VerySleepy@@tests": "Batchfile", "ddiakopoulos@@libnyquist": "C++", "libsndfile@@libsamplerate": "C", "mackron@@miniaudio": "C", "wbenny@@ia32-doc": "C", "philsquared@@Catch": "C++", "Microsoft@@DXUT": "C++", "AmokHuginnsson@@replxx": "C++", "emscripten-core@@posixtestsuite": "C", "intel-iot-devkit@@doxygen2jsdoc": "HTML", "intel-iot-devkit@@doxyport": "Python", "cgutman@@enet": "C", "deeplearningais@@ndarray": "C++", "comaeio@@CommonLibLight": "C++", "NVlabs@@cub": "<PERSON><PERSON>", "me-no-dev@@AsyncTCP": "C++", "me-no-dev@@ESPAsyncWebServer": "C++", "bblanchon@@ArduinoJson": "C++", "marvinroger@@async-mqtt-client": "C++", "NatronGitHub@@openfx": "C++", "NatronGitHub@@openfx-supportext": "C++", "dvorka@@mindforger-repository": null, "dvorka@@MITIE": "C++", "dvorka@@cmark": "C", "zaps166@@QmVk": "C++", "nothings@@stb": "C", "google@@fplbase": "C++", "whoenig@@crazyflie_cpp": "C++", "whoenig@@crazyflie_tools": "C++", "colmap@@colmap.github.io": "HTML", "EvilPudding@@Nuklear": "C", "cxong@@tinydir": "C", "EvilPudding@@glfw": "C", "Orc@@discount": "C", "smuellerDD@@jitterentropy-library": "C", "crosire@@d3d8to9": "C++", "elishacloud@@Logging": "C++", "elishacloud@@Hooking": "C++", "thelink2012@@injector": "C++", "ThirteenAG@@Hooking.Patterns": "C++", "elishacloud@@reshade": "C++", "KhronosGroup@@SPIRV-Headers": "C++", "ThirteenAG@@WidescreenFixesPack": "C++", "mark14wu@@cod-labs": "Verilog", "msysgit@@git": "C", "gitster@@git-htmldocs": "HTML", "msysgit@@Git-Cheetah": "C", "actboy168@@bee.lua": "C++", "actboy168@@luaffi": "C", "jemalloc@@jemalloc": "C", "google@@wycheproof": "Java", "obsproject@@libdshowcapture": "C++", "palana@@Syphon-Framework": "Objective-C", "obsproject@@obs-amd-encoder": "C++", "obsproject@@obs-browser": "C++", "obsproject@@obs-vst": "C++", "Mixer@@ftl-sdk": "C", "jaredhoberock@@nvcc-scons": "Python", "Manu343726@@cmake": "CMake", "soedinglab@@MMseqs2-Regression": "Shell", "DeviationTx@@libopencm3": "C", "mruby@@mruby": "C", "Immediate-Mode-UI@@Nuklear": "C", "veyon@@ultravnc": "C", "veyon@@libvncserver": "C", "veyon@@x11vnc": "C", "veyon@@libfakekey": "C", "nayutaco@@jsonrpc-c": "C", "luke-jr@@libbase58": "C", "LMDB@@lmdb": "C", "enki@@libev": "Shell", "akheron@@jansson": "C", "curl@@curl": "C", "LibreVR@@ext-win-sparkle": "C", "zeux@@microprofile": "C++", "KhronosGroup@@Vulkan-Docs": "JavaScript", "bukka@@phpc": "C", "deanproxy@@dlib": "C", "chadmv@@cgcmake": "CMake", "marqs85@@pulpino_qsys": "Tcl", "telegramdesktop@@libtgvoip": "C++", "Microsoft@@GSL": "C++", "desktop-app@@rlottie": "C++", "lz4@@lz4": "C", "desktop-app@@lib_crl": "C++", "desktop-app@@lib_rpl": "C++", "desktop-app@@lib_base": "C++", "desktop-app@@codegen": "C++", "desktop-app@@lib_ui": "C++", "desktop-app@@lib_rlottie": "Python", "desktop-app@@lib_lottie": "C++", "desktop-app@@lib_tl": "Python", "desktop-app@@lib_spellcheck": "C++", "desktop-app@@lib_storage": "C++", "desktop-app@@cmake_helpers": "CMake", "TartanLlama@@expected": "C++", "desktop-app@@lib_qr": "C++", "desktop-app@@libdbusmenu-qt": "C++", "hunspell@@hunspell": "C++", "ericniebler@@range-v3": "C++", "fcitx@@fcitx-qt5": "C++", "hamonikr@@nimf": "C", "hime-ime@@hime": "C", "fcitx@@fcitx5-qt": "C++", "desktop-app@@lib_webrtc": "C++", "TelegramMessenger@@tgcalls": "C++", "desktop-app@@lib_webview": "C++", "desktop-app@@lib_waylandshells": "C++", "KDE@@kwayland": "C++", "apple@@swift-corelibs-libdispatch": "C", "gnulib-modules@@bootstrap": "Shell", "snoyberg@@libyaml": "C", "google@@gumbo-parser": "HTML", "sheredom@@utf8.h": "C", "Dav1dde@@glad": "Python", "gulrak@@filesystem": "C++", "abseil@@abseil-cpp": "C++", "kazuho@@picojson": "C++", "scrossuk@@llvm-abi": "C++", "nodejitsu@@nexpect": "JavaScript", "mjrusso@@node-mime": "JavaScript", "felixge@@node-formidable": "JavaScript", "cinder@@TinderBox-Mac": "C++", "cinder@@TinderBox-Win": "C++", "getdnsapi@@jsmn": "C", "getdnsapi@@yxml": "C", "getdnsapi@@stubby": "C", "getdnsapi@@ssl_dane": "C", "google@@boringssl": "C", "PX4@@libopencm3": "C", "PX4-Works@@NXP_Kinetis_Bootloader_2_0_0": "C", "LoupVaillant@@Monocypher": "C", "staysail@@nng-wolfssl": "C", "MultiMC@@libnbtplusplus": "C++", "MultiMC@@quazip": "C++", "sui77@@rc-switch": "C++", "mapbox@@wagyu": "C++", "mapbox@@geometry.hpp": "C++", "mapbox@@protozero": "C++", "google@@googlemock": null, "michaeljones@@breathe": "Python", "jarro2783@@cxxopts": "C++", "cpp-netlib@@uri": "C++", "swisspol@@Cooliris-ToolKit": "Objective-C", "swisspol@@GCDWebServer": "Objective-C", "swisspol@@XLFacility": "Objective-C", "google@@benchmark": "C++", "GPUOpen-LibrariesAndSDKs@@VulkanMemoryAllocator": "C", "wolfpld@@tracy": "C++", "editorconfig@@editorconfig-core-test": "CMake", "FFmpeg@@FFmpeg": "C", "google@@liquidfun": "C++", "craigsapp@@midifile": "C++", "spsoft@@spxml": "C++", "xLightsSequencer@@xLights-macOS": "Objective-C++", "RomanKubiak@@Panels": null, "maierfelix@@nvk-examples": "JavaScript", "plutinosoft@@Neptune": "C++", "perfsonar@@i2util": "C", "kvasir-io@@mpl": "C++", "avplayer@@libavim": "C++", "avplayer@@avhttp": "C++", "avplayer@@acceptor": "C++", "avplayer@@avhtml": "HTML", "tomstuart@@computationbook": "<PERSON>", "RenWenshan@@emacs-lisp-intro-solutions": "Emacs <PERSON>", "rcnbapp@@librcnb": "C", "shadowsocks@@libcork": "C", "WiringPi@@WiringPi": "C", "synthetos@@Motate": "C", "emmericp@@dpdk": "C", "chrislim2888@@IP2Location-C-Library": "Shell", "json-c@@json-c": "C", "billziss-gh@@secfs.test": "C", "spdk@@dpdk": "C", "spdk@@intel-ipsec-mb": "Assembly", "spdk@@isa-l": "C", "Open-CAS@@ocf": "C", "nutanix@@libvfio-user": "C", "kometbomb@@klystron": "C", "joyent@@http-parser": "C", "hdeller@@seabios-hppa": "C", "cota@@berkeley-testfloat-3": "C", "cota@@berkeley-softfloat-3": "C", "tianocore@@edk2": "C", "pichenettes@@avril": "C++", "pichenettes@@avrilx": "C++", "pichenettes@@stmlib": "C++", "pichenettes@@stm-audio-bootloader": "C++", "pichenettes@@avr-audio-bootloader": "Python", "yesco@@imacs": "C", "cocos2d@@cocos2d-x": "C++", "xiangshouding@@gyp": "Python", "AshampooSystems@@json-releases": "Shell", "facebook@@yoga": "C++", "airbnb@@lottie-ios": "Swift", "codership@@wsrep-API": "C", "bombela@@backward-cpp": "C++", "DigitalInBlue@@Celero": "C++", "3Hren@@modules": "CMake", "ionescu007@@winipt": "C", "01org@@processor-trace": "C", "contiki-os@@mspsim": "Java", "cetic@@cooja-serial2pty": "Java", "cetic@@cooja-radiologger-headless": "Java", "cetic@@tinydtls": "C", "JelmerT@@cc2538-bsl": "Python", "contiki-os@@cc26xxware": "C", "cetic@@inih": "C", "contiki-os@@cc13xxware": "C", "STclab@@stm32nucleo-spirit1-lib": "C", "g-oikonomou@@sensniff": "Python", "zhaozg@@lua-openssl": "C", "luvit@@luv": "C", "luvit@@zlib": "C", "brimworks@@lua-zlib": "C", "rrthomas@@lrexlib": "C", "luvit@@pcre": "C", "luvit@@lpeg": "C", "AFLplusplus@@AFLplusplus": "C", "PX4@@GpsDrivers": "C++", "mavlink@@c_library_v2": "C", "Auterion@@android_openssl": "C", "mavlink@@gst-plugins-good": "C", "Auterion@@xz-embedded": "C", "mavlink@@libevents": "C++", "patrickelectric@@qmdnsengine": "C++", "r-lyeh@@oak": "C++", "r-lyeh@@route66": "C++", "r-lyeh@@heal": "C++", "Microsoft@@multiverso": "C++", "libfuse@@sshfs": "C", "RandyGaul@@player2d": "C", "compuphase@@minIni": "C", "KangLin@@RabbitThirdLibrary": "Shell", "KangLin@@QtAndroidUtils": "C++", "c9s@@bench": "C", "imkira@@node-db": "C++", "pelya@@android-shmem": "C", "flathub@@shared-modules": "CMake", "zardoru@@raindrop-data": "<PERSON><PERSON>", "mupen64plus@@mupen64plus-win32-deps": "C++", "TASEmulators@@GLideN64": "C", "TASEmulators@@mupen64plus-rsp-cxd4": "C", "TASEmulators@@snes9x": "C++", "TASEmulators@@mgba": "C", "TASEmulators@@melonDS": "C", "nattthebear@@musl": "C", "TASEmulators@@mednafen": "Objective-C", "pokemon-speedrunning@@gambatte-core": "Assembly", "nattthebear@@DobieStation": "C++", "jbremer@@darm": "C", "TASEmulators@@fwunpack": null, "StanfordLegion@@rdir": "Terra", "thp@@hidapi": "C", "inspirit@@PS3EYEDriver": "C++", "open-license-manager@@lcc-license-generator": "C++", "davidstutz@@seeds-revised": "C++", "davidstutz@@graph-based-image-segmentation": "C++", "catchorg@@Catch2": "C++", "msgpack@@msgpack-c": null, "progschj@@ThreadPool": "C++", "MisterTea@@easyloggingpp": null, "arsenm@@sanitizers-cmake": "CMake", "sakra@@cotire": "CMake", "MisterTea@@UniversalStacktrace": "C++", "getsentry@@sentry-native": "C", "sago007@@PlatformFolders": "C++", "r-lyeh-archived@@sole": "C++", "tkislan@@base64": "C++", "tlx@@tlx": "C++", "Microsoft@@Multiverso": "C++", "onnx@@onnx": "C++", "Microsoft@@onnxruntime": "C++", "ssrg-vt@@hermitux-kernel": "C", "ssrg-vt@@hermitux-musl": "C", "llvm-mirror@@openmp": "C++", "mozilla@@DeepSpeech-examples": "Python", "mozilla@@tensorflow": "C++", "kpu@@kenlm": "C++", "InsightSoftwareConsortium@@ITK": "C++", "Kitware@@VTK": "C++", "team-charls@@charls": "C++", "malaterre@@GDCM": "C++", "uclouvain@@openjpeg": "C", "GrokImageCompression@@grok": "C++", "DCMTK@@dcmtk": "C++", "f4exb@@sdrangel-windows-libraries": "C++", "RedisLabs@@RedisModulesSDK": "C", "RedisLabsModules@@readies": "Python", "lemire@@fast_double_parser": "C++", "RedisGears@@LibMR": "C", "OracleChain@@codebase": "C", "ChaiScript@@ChaiScript": "C++", "Tw1ddle@@geometrize-lib": "C++", "Tw1ddle@@geometrize-templates": "Python", "Tw1ddle@@geometrize-web-export": "Haxe", "Tw1ddle@@geometrize-scripts": null, "Tw1ddle@@geometrize-translations": "Python", "Tw1ddle@@BurstLinker": "C++", "Tw1ddle@@dataslinger-lib": "C++", "thennequin@@EasyWindow": "C++", "floooh@@sokol": "C", "thennequin@@UtilsCollection": "C++", "miniupnp@@libnatpmp": "C", "libopencm3@@libopencm3": "C", "technion@@libscrypt": "C", "artygus@@libplist": "C", "artygus@@libimobiledevice": "C", "artygus@@usbmuxd": "C", "pjreddie@@darknet": "C", "patrikhuber@@superviseddescent": "C++", "bulletphysics@@bullet3": "C++", "recastnavigation@@recastnavigation": "C++", "skarupke@@flat_hash_map": "C++", "cucumber@@cucumber-tck": "<PERSON>", "bitshares@@vendor": "C", "cryptonomex@@fc": "C++", "bitshares@@web_wallet": "CoffeeScript", "bitshares@@qt_wallet": "C++", "bitcoin@@leveldb": "C++", "vilbeyli@@VQUtils": "C", "vilbeyli@@D3DX12": "C", "vilbeyli@@D3D12MemoryAllocator": null, "assimp@@assimp": "C++", "vilbeyli@@VQModels": null, "vilbeyli@@imgui": "C++", "alexa@@avs-device-sdk": "C++", "espressif@@esptool": "Python", "nanopb@@nanopb": "C", "jedisct1@@libsodium": "C", "SmartThingsCommunity@@st-device-sdk-c": "C", "jerryscript-project@@jerryscript": "C", "Samsung@@http-parser": "C", "corkami@@pocs": "Assembly", "ShadowsocksR-Live@@json-c": "C", "ShadowsocksR-Live@@libsodium": "C", "ShadowsocksR-Live@@libuv": "C", "ShadowsocksR-Live@@mbedtls": "C", "ShadowsocksR-Live@@http-parser": "C", "ShadowsocksR-Live@@uv-mbed": "C", "ShadowsocksR-Live@@libbloom": "C", "AppImage@@AppImageUpdate": "C++", "AppImage@@libappimage": "C++", "quarnster@@parsehelp": "Python", "reupen@@foobar2000-sdk-modified": "C++", "reupen@@ui_helpers": "C++", "reupen@@mmh": "C++", "reupen@@pfc-modified": "C++", "reupen@@columns_ui-sdk": "C++", "reupen@@fbh": "C++", "eclipse@@tinydtls": "C", "LibrePCB@@hoedown": "C", "LibrePCB@@quazip": "C++", "LibrePCB@@googletest": "C++", "LibrePCB@@librepcb-test-data": "<PERSON><PERSON>", "LibrePCB@@parseagle": "C++", "fontobene@@fontobene-qt5": "C++", "LibrePCB@@fontobene-fonts": null, "LibrePCB@@delaunay-triangulation": "C++", "LibrePCB@@librepcb-fonts": null, "LibrePCB@@optional": "C++", "LibrePCB@@type_safe": "C++", "LibrePCB@@librepcb-i18n": null, "LibrePCB@@muparser": "C++", "LibrePCB@@dxflib": "C++", "acpica@@acpica": "ASL", "mavlink@@MAVSDK-Proto": "Python", "stevenlovegrove@@Pangolin": "C++", "appunite@@tropicssl": "C", "libass@@libass": "C", "appunite@@fribidi": "C", "psgroove@@lufa-lib": "C", "kakaroto@@PL3": "Assembly", "timblechmann@@nova-simd": "C++", "timblechmann@@nova-tt": "C++", "supercollider@@hidapi": "C", "supercollider@@scvim": "Vim script", "supercollider@@yaml-cpp": "C++", "supercollider@@scel": "Emacs <PERSON>", "Ableton@@link": "C++", "PortAudio@@portaudio": "C", "rth7680@@qemu-palcode": "C", "the-tcpdump-group@@libpcap": "C", "dropbox@@json11": "C++", "seladb@@PcapPlusPlus": "C++", "PyMesh@@carve": "C++", "PyMesh@@cork": "C", "PyMesh@@tetgen": "C++", "PyMesh@@triangle": "C", "PyMesh@@qhull": "C", "PyMesh@@Clipper": "C++", "PyMesh@@eigen": "C++", "PyMesh@@quartet": "C++", "PyMesh@@cgal": "C++", "PyMesh@@pybind11": "C++", "PyMesh@@mmg": "C", "PyMesh@@geogram": "C++", "PyMesh@@draco": "C++", "PyMesh@@TetWild": "C++", "PyMesh@@WindingNumber": "C++", "PyMesh@@tbb": "C++", "PyMesh@@libigl": "C++", "PyMesh@@jigsaw": "C++", "herumi@@ate-pairing": "C++", "mbbarbosa@@libsnark-supercop": "Assembly", "scipr-lab@@libff": "C++", "scipr-lab@@libfqfft": "C++", "modm-io@@cmsis-header-stm32": "C", "modm-io@@modm-devices": "Python", "modm-io@@ros-lib": "C++", "modm-ext@@cmsis-5-partial": "C", "modm-io@@avr-libstdcpp": "C++", "modm-ext@@printf": "C", "modm-ext@@freertos-partial": "C", "modm-ext@@CrashCatcher-partial": "C", "modm-io@@cmsis-header-sam": "C", "modm-ext@@tinyusb-partial": "C", "modm-ext@@fatfs-partial": "C", "modm-ext@@lvgl-partial": "C", "modm-ext@@etl-partial": "C++", "tensorflow@@tensorflow": "C++", "samtools@@htslib": "C", "Azure@@sonic-swss-common": "C++", "Azure@@sonic-linux-kernel": "<PERSON><PERSON><PERSON>", "Azure@@sonic-sairedis": "C++", "Azure@@sonic-swss": "C++", "krambn@@p4c-bm": "Python", "p4lang@@p4-hlir": "Python", "Azure@@sonic-dbsyncd": "Python", "Azure@@sonic-py-swsssdk": "Python", "Azure@@sonic-snmpagent": "Python", "p4lang@@ptf": "Python", "Azure@@sonic-utilities": "Python", "aristanetworks@@sonic": "Python", "Azure@@sonic-platform-common": "Python", "Azure@@sonic-platform-daemons": "Python", "Azure@@sonic-platform-pdk-pde": "Python", "Azure@@sonic-frr": "C", "Mellanox@@SAI-P4-BM": "C++", "Mellanox@@hw-mgmt": "Shell", "p@@redis-dump-load": "Python", "Mellanox@@SAI-Implementation": "C", "Azure@@sonic-mgmt-framework": "Python", "Azure@@sonic-telemetry": "Go", "Mellanox@@Switch-SDK-drivers": "C", "Azure@@sonic-ztp": "Python", "Azure@@sonic-restapi": "Python", "Azure@@sonic-mgmt-common": "Go", "Azure@@sonic-wpa-supplicant": "C", "Azure@@saibcm-modules": null, "nokia@@sonic-platform": "Python", "Azure@@sonic-linkmgrd": "C++", "Azure@@sonic-pins": "C++", "sheredom@@ubench.h": "C", "open-source-parsers@@jsoncpp": "C++", "pbek@@qmarkdowntextedit": "C++", "pbek@@qt-piwik-tracker": "C++", "pbek@@qkeysequencewidget": "C++", "pbek@@Qt-Toolbar-Editor": "C++", "pbek@@qtcsv": "C++", "qownnotes@@md4c": "C", "qownnotes@@QHotkey": "C++", "LibVNC@@libvncserver": "C", "Matroska-Org@@libebml": "C++", "Matroska-Org@@libmatroska": "C++", "MaddTheSane@@a52codec": "C", "MaddTheSane@@FFmpeg": "C", "zeek@@zeek-aux": "C", "zeek@@binpac": "C++", "zeek@@zeekctl": "Python", "zeek@@btest": "Python", "zeek@@cmake": "CMake", "zeek@@zeek-3rdparty": "C", "zeek@@broker": "C++", "zeek@@zeek-netcontrol": "Python", "zeek@@bifcl": "Yacc", "zeek@@zeek-docs": "Zeek", "zeek@@paraglob": "C", "zeek@@rapidjson": "C++", "zeek@@libkqueue": "C", "zeek@@highwayhash": "C++", "zeek@@zeek-archiver": "C++", "zeek@@package-manager": "Python", "zeek@@zeek-client": "Python", "RegrowthStudios@@SoAGameData": "GLSL", "RegrowthStudios@@Vorb": "C++", "xen-project@@xen": "C", "tklengyel@@libvmi": "C", "tklengyel@@volatility3": "Python", "tklengyel@@dwarf2json": "Go", "rdp@@screen-capture-recorder-to-video-windows-free": "C++", "google@@zopfli": "C++", "nxpfrankli@@libusb": "C", "tiny-dnn@@tiny-dnn": "C++", "practicalarduino@@SHT1x": "C++", "sfml@@sfml": "C++", "arsv@@riscv-qemu-tests": "Assembly", "michaeljclark@@asmjit": "C++", "michaeljclark@@riscv-meta": null, "binaryage@@sparkle": "Swift", "binaryage@@uninstaller": "Objective-C++", "OpenSourceRisk@@QuantLib": null, "b00f@@qautostart": "C++", "alex-spataru@@QSimpleUpdater": "C++", "radare@@radare2": "C", "Kode@@kraffiti_bin": null, "Kode@@krafix_bin": null, "Kode@@kincmake": "TypeScript", "jncronin@@dtc": "C", "dmlc@@dmlc-core": "C++", "dmlc@@ps-lite": "C++", "dmlc@@nnvm": "C++", "dmlc@@dlpack": "Python", "dmlc@@cub": "<PERSON><PERSON>", "Tasssadar@@kexec-tools": "C", "Tasssadar@@multirom_adbd": "C", "zynaddsubfx@@instruments": "Shell", "fundamental@@rtosc": "C++", "DISTRHO@@DPF": "C++", "apache@@incubator-pagespeed-mod": "C++", "FRiCKLE@@ngx_cache_purge": "C", "nginx@@nginx": "C", "openresty@@set-misc-nginx-module": "C", "simpl@@ngx_devel_kit": "C", "openresty@@headers-more-nginx-module": "C", "martinellimarco@@scream-android": "Java", "Provenance-Emu@@GLideN64": "C", "Provenance-Emu@@mupen64plus-core": "C", "mupen64plus@@mupen64plus-rsp-hle": "C", "Provenance-Emu@@mupen64plus-rsp-cxd4": "C", "Provenance-Emu@@mupen64plus-video-rice": "C++", "Provenance-Emu@@LzmaSDKObjC": "C++", "Provenance-Emu@@Core-VirtualJaguar": "Objective-C", "Provenance-Emu@@reicast-emulator": "C", "libretro@@PokeMini": "C", "libretro@@tgbdual-libretro": "C", "OatmealDome@@dolphin": "C++", "Provenance-Emu@@Play-": "C++", "Provenance-Emu@@ppsspp": "C++", "stenzek@@duckstation": "C++", "libretro@@desmume2015": "C++", "Provenance-Emu@@O2EM-Core": "C", "Provenance-Emu@@VecXGL-Core": null, "Provenance-Emu@@CrabEmu-Core": null, "Provenance-Emu@@Bliss-Core": null, "Provenance-Emu@@4DO-Core": null, "msteveb@@jimtcl": "C", "mapnik@@test-data": "PLpgSQL", "mapnik@@test-data-visual": "Python", "mapbox@@variant": "C++", "mapbox@@polylabel": "C++", "rikusalminen@@glxw": "Python", "Groovounet@@glm": "C++", "dschmidt@@libcrashreporter-qt": "C++", "ekg@@smithwaterman": "C++", "ekg@@multichoose": "C++", "ekg@@fastahack": "C++", "ekg@@intervaltree": "C++", "ekg@@fsom": "C", "ekg@@filevercmp": "C", "edawson@@libVCFH": "C++", "simd-everywhere@@simde-no-tests": "C", "litespeedtech@@lsquic": "C", "samsung@@veles.znicz": "Python", "libarchive@@libarchive": "C", "mdaines@@viz.js": "JavaScript", "yyuu@@pyenv": "<PERSON><PERSON>", "samsung@@veles.mastodon": "Java", "Samsung@@veles.simd": "C++", "HoShiMin@@HookLib": "C", "wjakob@@nanogui": "C++", "opencv@@opencv": "C++", "opencv@@opencv_contrib": "C++", "fribidi@@fribidi": "C", "HOST-Oman@@libraqm": "C", "ariya@@FastLZ": "C", "asimonov-im@@bzip2": "C", "robottwo@@quicklz": "C++", "ConorStokes@@LZSSE": "C++", "inikep@@lizard": "C", "01org@@isa-l": "C", "SecureAuthCorp@@pysap": "Python", "conor42@@fast-lzma2": "C", "sisong@@lzma": "C++", "emmanuel-marty@@lzsa": "C", "powturbo@@Turbo-Range-Coder": "C", "powturbo@@Turbo-Run-Length-Encoding": "C", "facebook@@zstd": "C", "justinfrankel@@WDL": "C", "taglib@@taglib": "C++", "justinfrankel@@reaper-sdk": "C++", "alanxz@@rabbitmq-c": "C", "UW-Hydro@@VIC_sample_data": "HTML", "DBCTRADO@@LibISDB": "C++", "JordanMilne@@gumbo-parser": "HTML", "Microsoft@@checkedc-clang": null, "C0deH4cker@@SimpleGameEngine": "C", "vlc-qt@@libvlc-headers": "C", "vlc-qt@@packaging": "<PERSON><PERSON><PERSON>", "Open-Transactions@@opentxs-cmake": "CMake", "Open-Transactions@@lucre": "Java", "aappleby@@smhasher": "C++", "983@@SHA1": "C++", "sipa@@bech32": "JavaScript", "martinus@@robin-hood-hashing": "C++", "cjdelisle@@packetcrypt_rs": "Rust", "Open-Transactions@@secp256k1-abc": "C", "vincentriemer@@yoga-dom": "C++", "Macaulay2@@memtailor": "C++", "Macaulay2@@mathic": "C++", "Macaulay2@@mathicgb": "C++", "Macaulay2@@bdwgc": "C", "Macaulay2@@libatomic_ops": null, "Macaulay2@@mpir": null, "Macaulay2@@flint2": "C", "Macaulay2@@frobby": "C++", "Macaulay2@@fflas-ffpack": "C++", "Macaulay2@@givaro": "C++", "Macaulay2@@M2-emacs": "Emacs <PERSON>", "ARM-software@@CMSIS": "C", "PaulStoffregen@@cores": "C", "ulfalizer@@Kconfiglib": "Python", "sipeed@@kendryte-standalone-sdk": "C", "sipeed@@kflash.py": "Python", "micropython@@micropython": "C", "littlevgl@@lv_binding_micropython": "C", "pellepl@@spiffs": "C", "igrr@@mkspiffs": "C++", "Neutree@@micropython-ulab": "C", "raburton@@esptool2": "C", "mikee47@@FlashString": "C++", "nodejs@@http-parser": "C", "mikee47@@IFS": "C++", "jacketizer@@libyuarel": "C", "slaff@@mqtt-codec": "C", "mikee47@@rboot": "C", "charliesome@@ws_parser": "C", "igrr@@axtls-8266": "C", "earlephilhower@@bearssl-esp8266": "C", "pfalcon@@esp-open-lwip": "C", "mikee47@@esp82xx-nonos-linklayer": "C", "rhempel@@umm_malloc": "C", "espressif@@ESP8266_NONOS_SDK": "C", "mikee47@@picotool": null, "raspberrypi@@pico-sdk": "C", "lwip-tcpip@@lwip": "C", "imabot2@@serialib": "C++", "adafruit@@Adafruit_BME280_Library": "C++", "adafruit@@Adafruit_BusIO": "C++", "adafruit@@Adafruit_Sensor": "C++", "adafruit@@Adafruit_SSD1306": "C++", "adafruit@@Adafruit-ST7735-Library": "C++", "adafruit@@Adafruit_VL53L0X": "C++", "kosme@@arduinoFFT": "C++", "slaff@@Arduino_TensorFlowLite": "C++", "xxzl0130@@CS5460": "C++", "slaff@@Sming-DIAL": "C++", "DFRobot@@DFRobotDFPlayerMini": "C++", "beegee-tokyo@@DHTesp": "C++", "slaff@@Sming-GoogleCast": "C++", "mikee47@@Sming-Graphics": "C", "mikee47@@HardwareSPI": "C++", "mikee47@@HueEmulator": "C++", "slaff@@Sming-jerryscript": "JavaScript", "markszabo@@IRremoteESP8266": "C++", "itead@@ITEADLIB_Arduino_Nextion": "HTML", "mikee47@@Sming-LittleFS": "C++", "mikee47@@Sming-MDNS": "C++", "mikee47@@Sming-MHZ19": "C++", "kmihaylov@@modbusino": null, "nomis@@ModbusMaster": "C++", "coryjfowler@@MCP_CAN_lib": "C++", "iafonov@@multipart-parser-c": "C", "mikee47@@Sming-RapidXML": "C++", "slaff@@Sming-rbpf": "C", "wizard97@@Embedded_RingBuf_CPP": "C++", "mikee47@@RingTone": "C++", "mikee47@@SignalGenerator": "C++", "jfjlaros@@simpleRPC": "C++", "mikee47@@SmingTest": "C++", "mikee47@@SolarCalculator": "C++", "mikee47@@Sming-SSDP": "C++", "mikee47@@TFT_S1D13781": "C++", "mikee47@@Timezone": "C++", "mikee47@@ToneGenerator": "C++", "avishorp@@TM1637": "C++", "mikee47@@Sming-UPnP": "C++", "mikee47@@UPnP-Schema": "XSLT", "mikee47@@VT100": "C++", "Vidvox@@hap": "C", "zeromq@@cppzmq": "C++", "x42@@libltc": "C", "zeromq@@libzmq": "C++", "brendangregg@@FlameGraph": "<PERSON><PERSON>", "CoolProp@@ExcelAddinInstaller": "<PERSON>", "sakra@@FindMathematica": "CMake", "CoolProp@@IF97": "C++", "CoolProp@@REFPROP-headers": "C++", "CE-Programming@@zdis": "C", "adriweb@@tivars_lib_cpp": "C++", "kkos@@oniguruma": "C", "greenplum-db@@pythonsrc-ext": null, "olikraus@@U8g2_Arduino": "C", "olikraus@@Ucglib_Arduino": "C", "weiss@@c99-snprintf": "C", "Tencent@@ncnn": "C++", "google@@protobuf": "C++", "akirill@@wtl": "C", "ethereum@@evmc": "C", "ethereum@@cable": "CMake", "scummvm@@game-translations": null, "abolz@@CmdLine": "C++", "ddiakopoulos@@tinyply": "C++", "ingowald@@pbrt-parser": "C++", "rvaser@@bioparser": "C++", "rvaser@@spoa": "C++", "rvaser@@thread_pool": "C++", "martinsos@@edlib": "C++", "rvaser@@rampler": "C++", "clara-parabricks@@GenomeWorks": "<PERSON><PERSON>", "k-takata@@Onigmo": "C", "ranguba@@rroonga": "C", "matsumoto-r@@ngx_mruby": "C", "groonga@@groonga-log": "<PERSON>", "apache@@arrow": "C++", "jonmarimba@@opencv": "C++", "aseba-community@@dashel": "C++", "aseba-community@@aseba": "C++", "cyberbotics@@webots_ros": "C++", "mariano@@node-db": "C++", "tearshark@@modern_cb": "C++", "neurodroid@@android-platform-external-libfuse": "C", "neurodroid@@encfs": "C++", "MicrochipTech@@cryptoauthlib": "C", "tuanpmt@@espmqtt": "C", "marbl@@meryl-utility": "C", "marbl@@meryl": "C", "marbl@@seqrequester": "C", "kosua20@@PtahRenderer": "Swift", "nemomobile@@qtdbusextended": "C++", "wasm3@@wasm3": "C", "gflags@@gflags": "C++", "Cloudef@@wlc": "C", "Cloudef@@inihck": "C", "Cloudef@@chck": "C++", "lisitsyn@@tapkee_data": null, "niessner@@Opt": "Terra", "termbox@@termbox2": "C", "svgpp@@svgpp": "C++", "svgpp@@rapidxml_ns": "C++", "zeux@@pugixml": "C++", "jeaye@@jest": "C++", "catchorg@@Clara": "C++", "syoyo@@tinyobjloader": "C++", "chewing@@libchewing": "C", "EasyIME@@libIME": "C++", "thestk@@rtaudio": "C++", "fragglet@@lhasa": "C", "gdraheim@@zziplib": "Shell", "kyu-sz@@pyboostcvconverter": "C++", "dtschump@@CImg": "C++", "ermig1979@@Simd": "C++", "webmproject@@libwebp": "C", "hackedteam@@core-android-audiocapture": "C", "hackedteam@@core-android-market": "C++", "hackedteam@@core-android-native": "C", "hackedteam@@core-blackberry": "Java", "hackedteam@@core-ios": "Objective-C", "hackedteam@@core-linux": "C", "hackedteam@@core-macos": "C++", "hackedteam@@core-packer": "C", "hackedteam@@core-symbian": "C++", "hackedteam@@core-win32": "C++", "hackedteam@@core-win64": "Assembly", "hackedteam@@core-winmobile": "C++", "hackedteam@@core-winphone": "C++", "hackedteam@@driver-macos": "C", "hackedteam@@driver-win32": "C", "hackedteam@@driver-win64": "C", "hackedteam@@fuzzer-android": "C", "hackedteam@@fuzzer-windows": "Python", "hackedteam@@gitosis-admin": null, "hackedteam@@poc-x": "<PERSON>", "hackedteam@@rcs-anonymizer": "Shell", "hackedteam@@rcs-anonymizer-old": "C", "hackedteam@@rcs-backdoor": "<PERSON>", "hackedteam@@rcs-collector": "<PERSON>", "hackedteam@@rcs-common": "<PERSON>", "hackedteam@@rcs-console": "ActionScript", "hackedteam@@rcs-console-library": "ActionScript", "hackedteam@@rcs-console-mobile": null, "hackedteam@@rcs-db-ext": "Python", "hackedteam@@scout-win": "C++", "hackedteam@@soldier-win": "C", "hackedteam@@test-av2": "Python", "hackedteam@@test-av": "Python", "hackedteam@@vector-applet": "Java", "hackedteam@@vector-default": "C", "hackedteam@@vector-dropper": "C++", "hackedteam@@vector-edk": "C", "hackedteam@@vector-exploit": "HTML", "hackedteam@@vector-ipa": "C", "hackedteam@@vector-macos-root": "Objective-C", "hackedteam@@vector-ni": null, "hackedteam@@vector-offline2": "Python", "hackedteam@@vector-offline": "C++", "hackedteam@@vector-recover": "C++", "hackedteam@@vector-rmi": "C", "hackedteam@@vector-silent": "C", "lheric@@libgitlmvc": "C++", "omcfadde@@jpeg-8d": "C", "matricali@@UBoat-Panel": "PHP", "llvm-mirror@@llvm": "LLVM", "dcodeIO@@binaryen": "WebAssembly", "llvm-mirror@@compiler-rt": "C", "llvm-mirror@@clang": "C++", "llvm-mirror@@lld": "C++", "piface@@libmcp23s17": "C", "xaqq@@libpifacedigital": null, "zeromq@@zmqpp": "C++", "xaqq@@flagset": "C++", "hyrise-mp@@tpcds-result-reproduction": "<PERSON><PERSON><PERSON>", "mrks@@cpp-btree": "C++", "gmarcais@@compact_vector": "C++", "cpplint@@cpplint": "Python", "abseil@@googletest": "C++", "mrks@@dbgen.JCC-H": "C", "gregrahn@@join-order-benchmark": null, "jtv@@libpqxx": "C++", "Neargye@@magic_enum": "C++", "Tessil@@robin-map": "C++", "hyrise@@sql-parser": "C++", "hyrise-mp@@tpcds-kit": "C", "asmaloney@@libE57Format": "C++", "CloudCompare@@CCCoreLib": "C++", "cloudcompare@@PoissonRecon": "C++", "hvs-ait@@mplane-plugin": "C++", "CyberbuildLab@@masonry-cc": "C++", "davisking@@dlib": "C++", "hhvm@@hhvm-third-party": "C", "facebook@@fatal": "C++", "facebookincubator@@fb303": "C++", "facebook@@libafdt": "C", "intelxed@@mbuild": "Python", "intelxed@@xed": "Python", "bronsonp@@SlidingDFT": "C", "sikang@@motion_primitive_library": "C++", "sikang@@DecompROS": "C++", "jrl-umi3218@@jrl-cmakemodules": "CMake", "Gepetto@@example-robot-data": "Python", "isovic@@seqlib": "HTML", "isovic@@argumentparser": "C++", "isovic@@gindex": "C++", "ultravideo@@greatest": "C", "mate-desktop@@mate-submodules": "C", "thestk@@stk": "C++", "skystrife@@cpptoml": "C++", "hugoam@@nanovg": "C", "hugoam@@GENie": "C", "hugoam@@vg-renderer": "C", "Auburns@@FastNoise": "C#", "munificent@@wren": "C", "septag@@deboost.context": "Assembly", "jpcy@@xatlas": "C++", "zeux@@meshoptimizer": "C++", "hugoam@@MaskedOcclusionCulling": "C++", "Subsurface@@libdc": "C", "mxe@@mxe": "<PERSON><PERSON><PERSON>", "monkey@@monkey": "C", "grpc@@grpc": "C++", "apache@@zookeeper": "Java", "Kode@@glslang": "C++", "Kode@@krafix_tests": "GLSL", "Kode@@sourcemap.cpp": "C++", "Kode@@SPIRV-Cross": "GLSL", "dougbinks@@enkiTS": "C++", "greg7mdp@@sparsepp": "C++", "ThePhD@@sol2": "C++", "tapio@@rlutil": "C++", "mattconte@@tlsf": "C", "awslabs@@aws-crt-cpp": "C++", "scipr-lab@@libsnark": "C++", "google@@leveldb": "C++", "Tencent@@libco": "C++", "google@@glog": "C++", "gperftools@@gperftools": "C++", "p4lang@@p4c-behavioral": "C", "p4lang@@p4c-bm": "Python", "p4lang@@behavioral-model": "C++", "p4lang@@p4ofagent": "C", "floodlight@@oftest": "Python", "p4lang@@switch": "C", "p4lang@@p4-build": "C++", "wireapp@@generic-message-proto": "Scala", "wireapp@@cryptobox-c": "C", "creytiv@@rem": "C", "wireapp@@rew": "C", "wireapp@@re": "C", "LLNL@@ZFP": "C++", "kanflo@@uhej": "C", "grigorig@@stcgal": "Python", "ionescu007@@edk2": "C", "xbmc@@skin.touched": "Shell", "panda-re@@panda": "C", "glennrp@@libpng": "C", "liberationfonts@@liberation-fonts": "<PERSON><PERSON><PERSON>", "FNA-XNA@@FAudio": "C++", "HansKristian-Work@@vkd3d-proton": "C", "KhronosGroup@@OpenXR-SDK": "C++", "jp7677@@dxvk-nvapi": "C++", "yanyiwu@@cppjieba": "C++", "google@@breakpad": "C++", "nih-at@@libzip": "C", "fancycode@@MemoryModule": "C", "microsoft@@detours": "C++", "elishacloud@@dinputto8": "C++", "zydeco@@libmfs": "C", "zydeco@@libres": "C", "zydeco@@minivmac": "C", "elmindreda@@glfw": "C", "openMVG-thirdparty@@osi_clp": "C++", "openMVG-thirdparty@@cereal": "C++", "robojackets@@style-configs": null, "robojackets@@rrt": "C++", "RoboJackets@@grSim": "C++", "robojackets@@robocup-fshare": "C++", "kylestach@@SDL_GameControllerDB": "Python", "Reisyukaku@@NX_Sysmodules": "C++", "SuperTux@@tinygettext": "C++", "albertodemichelis@@squirrel": "C++", "SuperTux@@sexp-cpp": "Common Lisp", "SuperTux@@physfs": "C", "SuperTux@@SDL_ttf": "C", "LukasBanana@@SPIRV-Headers": "C++", "cocos2d@@cocos2d-console": "Python", "cocos2d@@bindings-generator": "Python", "dumganhar@@ccs-res": null, "vancegroup-mirrors@@eigen": "C++", "tesseract-ocr@@test": "Shell", "flathub@@org.freedesktop.fwupd": null, "memononen@@nanovg": "C", "ryancdotorg@@secp256k1": "C", "ryancdotorg@@scrypt-jane": "C", "TobKed@@label-when-approved-action": "TypeScript", "potiuk@@get-workflow-origin": "TypeScript", "DoozyX@@clang-format-lint-action": "Python", "UAVCAN@@libuavcan": "C", "tensorflow@@fold": "Python", "monostream@@heif": "C++", "barisione@@clang-format-hooks": "Python", "fnc12@@sqlite_orm": "C++", "oxen-io@@oxen-mq": "C++", "jagerman@@uvw": null, "whoshuu@@cpr": "C++", "ngtcp2@@ngtcp2": "C", "arximboldi@@sinusoidal-sphinx-theme": "HTML", "nddrylliog@@greg": "C", "fletcher@@MMD-Test-Suite": "HTML", "fletcher@@MMD-Support": "XML", "fletcher@@human-markdown-reference": "<PERSON><PERSON>", "fletcher@@MultiMarkdown-4": "C", "osquery@@third-party-bzip2": "C", "file@@file": "C", "systemd@@systemd": "C", "GNOME@@libxml2": "C", "arangodb@@linenoise-ng": "C++", "xz-mirror@@xz": "C", "sleuthkit@@sleuthkit": "C", "osquery@@third-party-sqlite": "C", "ssdeep-project@@ssdeep": "C", "apache@@thrift": "C++", "VirusTotal@@yara": "C", "hercules-team@@augeas": "Augeas", "osquery@@third-party-smartmontools": "C++", "rpm-software-management@@rpm": "C", "osquery@@third-party-popt": "Shell", "osquery@@third-party-libdpkg": "C", "linux-audit@@audit-userspace": "C", "aclements@@libelfin": "C++", "osquery@@third-party-libgcrypt": "C", "osquery@@third-party-libgpg-error": "C", "lvmteam@@lvm2": "C", "osquery@@third-party-iptables": "C", "edenhill@@librdkafka": "C", "vincentbernat@@lldpd": "C", "boostorg@@boost": "HTML", "osquery@@third-party-gnulib": "C", "trailofbits@@ebpfpub": "C++", "libexpat@@libexpat": "C", "awslabs@@aws-c-auth": "C", "awslabs@@aws-c-cal": "C", "awslabs@@aws-c-common": "C", "awslabs@@aws-c-compression": "C", "awslabs@@aws-c-event-stream": "C", "awslabs@@aws-c-http": "C", "awslabs@@aws-c-io": "C", "awslabs@@aws-c-mqtt": "C", "awslabs@@aws-c-s3": "C", "awslabs@@aws-checksums": "C", "awslabs@@s2n": "C", "aws@@aws-sdk-cpp": "C++", "DeaDBeeF-Player@@deadbeef-osx-deps": "C", "DeaDBeeF-Player@@mp4p": "C", "DeaDBeeF-Player@@apbuild": "<PERSON><PERSON>", "aerospike@@aerospike-common": "C", "aerospike@@aerospike-mod-lua": "C", "aerospike@@jansson": "C", "aerospike@@luajit": "C", "aerospike@@s2-geometry-library": "C++", "aerospike@@aerospike-telemetry-agent": "Python", "aerospike@@jemalloc": "C", "freedreno@@envytools": "C", "xcsoar@@ioio": "C", "XCSoar@@UsbSerial": null, "cloudius-systems@@musl": "C", "osvunikernel@@musl": "C", "libusual@@libusual": "C", "kframework@@k": "Java", "somesocks@@lua-lockbox": "<PERSON><PERSON>", "Steveice10@@buildtools": "Python", "networkprotocol@@netcode.io": "C", "networkprotocol@@reliable.io": "C", "eranpeer@@FakeIt": "C++", "rkollataj@@nodeeditor": "C++", "tuvok@@qtCannelloniCanBus": "C++", "GENIVI@@CANdb": "C++", "rumpkernel@@buildrump.sh": "C", "rumpkernel@@src-netbsd": null, "AlexDenisov@@LibEBC": "C++", "mull-project@@libirm": "C++", "DaanDeMeyer@@reproc": "C", "fibjs@@fibjs_vender": "C++", "stlab@@libraries": "C++", "stlab@@adobe_source_libraries": "C++", "stlab@@adobe_platform_libraries": "C++", "stlab@@double-conversion": "C++", "darktable-org@@rawspeed": "C++", "KhronosGroup@@OpenCL-Headers": "C", "houz@@libxcf": "C", "gpakosz@@whereami": "C", "darktable-org@@darktable-tests": "Shell", "LibRaw@@LibRaw": "C++", "unix-thrust@@unix-vagrant": "VimL", "libigl@@eigen": "C++", "martanne@@vis-test": "C", "myleott@@transformers": "Python", "ebassi@@mutest": "C", "mrtazz@@jekyll-layouts": "HTML", "OpMonTeam@@OpMon-Data": "GDScript", "bitcoin-core@@secp256k1": "C", "xant@@libut": "C", "skypjack@@entt": "C++", "ARM-software@@CMSIS_5": "C", "mendsley@@bsdiff": "C", "TsudaKageyu@@minhook": "C", "skaslev@@gl3w": "Python", "Microsoft@@vcpkg": "CMake", "Corvusoft@@restbed": "C++", "commonmark@@cmark": "C", "RetroShare@@OBS": "Shell", "truvorskameikin@@udp-discovery-cpp": "C++", "i2p@@libsam3": "C", "RetroShare@@jni.hpp": "C++", "krkrz@@baseclasses": "C++", "krkrz@@freetype": "C", "krkrz@@libjpeg-turbo": "C", "krkrz@@libpng": "C", "krkrz@@oniguruma": "C", "krkrz@@zlib": "C", "krkrz@@jxrlib": "C", "sardemff7@@libgwater": "C", "sardemff7@@libnkutils": "C", "Loki-Astari@@andvari-theme-documentation": "HTML", "Loki-Astari@@ThorMaker": "Shell", "Loki-Astari@@ThorsStorage": "C++", "mpromonet@@live555helper": "C++", "civetweb@@civetweb": "C", "mpromonet@@webrtc-streamer-html": "HTML", "kimgr@@getopt_port": "C++", "jupp0r@@prometheus-cpp": "C++", "mpromonet@@libv4l2cpp": "C++", "openucx@@xucg": "C", "analogdevicesinc@@no-OS": "C", "cntools@@rawdraw": "C", "VoodooI2C@@VoodooGPIO": "C++", "VoodooI2C@@VoodooI2CHID": "C++", "VoodooI2C@@VoodooI2CELAN": "C++", "VoodooI2C@@VoodooI2CSynaptics": "C++", "VoodooI2C@@VoodooI2CFTE": "C++", "VoodooI2C@@VoodooI2CAtmelMXT": "C++", "Mogara@@QSanDumpUploader": "C++", "libretro@@libretro-common": "C", "simple2d@@test_media": null, "simple2d@@deps": "C", "obilaniu@@libpfc": "C", "travisdowns@@nasm-utils": "Assembly", "nemequ@@portable-snippets": "C", "travisdowns@@pmu-tools": "Python", "log4cplus@@log4cplus": "C++", "apache@@pulsar": "Java", "seahorn@@sea-dsa": "C++", "vnotex@@vtextedit": "C++", "Cpasjuste@@libcross2d": "C++", "Cpasjuste@@pscrap": "C++", "klauspost@@rawspeed": "C++", "majn@@tgl": "C", "ebetica@@pytorch": "C++", "TorchCraft@@TorchCraft": "C++", "openbw@@openbw": "C++", "openbw@@bwapi": "C++", "rogersce@@cnpy": "C++", "jgehring@@cxx-prettyprint": "C++", "mgbellemare@@Arcade-Learning-Environment": "C++", "shon@@httpagentparser": "Python", "UltimateHackingKeyboard@@KSDK_2.0_MKL03Z8xxx4": "C", "UltimateHackingKeyboard@@KSDK_2.0_MK22FN512xxx12": "C", "UltimateHackingKeyboard@@bootloader": "C", "UltimateHackingKeyboard@@agent": "TypeScript", "UltimateHackingKeyboard@@SDK_2.8.0_MKL17Z32xxx4": "C", "appcelerator@@kroll": "C++", "mickem@@json-spirit": "C++", "mickem@@google-breakpad": "C++", "anura-engine@@imgui": "C++", "litespeedtech@@ls-qpack": "C", "litespeedtech@@ls-hpack": "C", "ousnius@@nifly": "C++", "smartdevicelink@@jsoncpp": null, "smartdevicelink@@rpc_spec": "Python", "mavlink@@mavlink": "CMake", "dronecan@@libuavcan": "C++", "PX4@@jMAVSim": "Java", "PX4@@PX4-SITL_gazebo": "C++", "PX4@@PX4-GPSDrivers": "C++", "PX4@@Micro-CDR": "C++", "PX4@@NuttX": "C", "PX4@@NuttX-apps": "C", "PX4@@PX4-FlightGear-Bridge": "C++", "PX4@@px4-jsbsim-bridge": "C++", "UAVCAN@@libcanard": "C++", "UAVCAN@@public_regulated_data_types": "Python", "PX4@@public_regulated_data_types": "Python", "PX4@@Monocypher": null, "PX4@@px4-simulation-ignition": "C++", "PX4@@libtomcrypt": "C", "PX4@@libtommath": null, "pytorch@@cpuinfo": "C", "gameknife@@gkEngine-thirdparty": "C", "gameknife@@gkEngine-mediapack-base": null, "gameknife@@gkEngine-mediapack-confroom": null, "google@@perf_data_converter": "C++", "lewes6369@@tensorRTWrapper": "C++", "pietern@@libcorrect": "C", "mayah@@tinytoml": "C++", "nanomsg@@nanomsg": "C", "GothenburgBitFactory@@libshared": "C++", "Wind4@@vlmcsd-debian": "Shell", "Wind4@@vlmcsd-docker": "Dockerfile", "liris@@websocket-client": "Python", "albertlauncher@@plugins": "C++", "seccomp@@libseccomp": "C", "sustrik@@libmill": "C", "mldbai@@baseimage-docker": "Python", "mldbai@@tensorflow": "C++", "mldbai@@re2": "C++", "mldbai@@JPeg9A": "C", "mldbai@@eigen": "C++", "mldbai@@edlib": "C++", "mldbai@@uap-cpp": "C++", "mldbai@@uap-core": "JavaScript", "mldbai@@protobuf": "C++", "mldbai@@farmhash": "C++", "mldbai@@highwayhash": "C++", "mldbai@@mongo-cxx-driver": "C++", "mldbai@@libbson": "C", "mldbai@@mongo-c-driver": "C", "mldbai@@mnmlstc": "C++", "mldbai@@v8-cross-build-output": "C++", "mldbai@@gmock-1.7.0": "C++", "mldbai@@giflib": "Shell", "mldbai@@pffft": "C", "mldbai@@JSONTestSuite": "C++", "mldbai@@zstd": "C", "mldbai@@mldb_sample_plugin": "C++", "mldbai@@easyexif": "C++", "mldbai@@libgit2": "C", "mldbai@@fastText": "C++", "mldbai@@cpprestsdk": "C++", "mldbai@@azure-storage-cpp": "C++", "datacratic@@pymldb": "Python", "mldbai@@libhubbub": "HTML", "mldbai@@concurrentqueue": "C++", "mldbai@@libarchive": "C", "mldbai@@s2geometry": "C++", "mldbai@@cryptopp": null, "simsong@@http-parser": "C", "tomato42@@tlsfuzzer": "Python", "warner@@python-ecdsa": "Python", "tomato42@@tlslite-ng": "Python", "dolphin-emu@@ext-win-qt": "C++", "mgba-emu@@mgba": "C", "nebgnahz@@ofxGrt": "C++", "damellis@@grt": "C++", "damellis@@Processing_SpaceInv": "JavaScript", "nebgnahz@@ofxDatGui": "C++", "braitsch@@ofxParagraph": "C++", "EOSIO@@musl": "C", "EOSIO@@libcxx": "C++", "learnforpractice@@binaryen": "Assembly", "learnforpractice@@berkeley-softfloat-3": "C", "EOSIO@@magic_get": "C++", "learnforpractice@@panicwrap": "Go", "kardianos@@osext": "Go", "learnforpractice@@chainbase": "C++", "learnforpractice@@appbase": "C++", "learnforpractice@@fc": "C++", "learnforpractice@@polly": "CMake", "learnforpractice@@micropython": "C", "learnforpractice@@cpython": "Python", "learnforpractice@@cpp-ethereum": "C++", "learnforpractice@@lua_sandbox": "C", "EOSIO@@wabt": "C++", "cnlohr@@cnfa": "C", "cnlohr@@rawdrawandroid": "C", "jnider@@nucleus": "C++", "wolkykim@@qlibc": "C", "Z3Prover@@z3": "C++", "creativetimofficial@@material-kit": "HTML", "Lora-net@@lr1110_driver": "C", "whoshuu@@curl": "C", "whoshuu@@googletest": "C++", "OpenSpace@@Spice": "C", "CelestiaProject@@CelestiaContent": "CMake", "irods@@irods_schema_messaging": null, "RedisGraph@@libcypher-parser": "C", "antirez@@rax": "C", "RediSearch@@RediSearch": "C", "RadeonOpenCompute@@ROCm-Device-Libs": "C", "RadeonOpenCompute@@llvm-project": null, "night-ghost@@SingleSerial": "C++", "ingydotnet@@test-simple-bash": "Shell", "illusori@@bash-tap": "Shell", "vcflib@@vcflib": "C++", "uNetworking@@uSockets": "C", "uNetworking@@libEpollFuzzer": "C", "rgiduthuri@@amdovx-modules": "C++", "andoma@@libav": "C", "andoma@@rtmpdump": "C", "andoma@@libntfs_ext": "C", "andoma@@freetype2-ios": "C", "andoma@@vmir": "C", "guardianproject@@badvpn": "C", "guardianproject@@jsocks": "Java", "qml-box2d@@qml-box2d": "C++", "bjorn@@tiled": "C++", "ucb-bar@@berkeley-softfloat-3": "C", "tianocore@@edk2-cmocka": null, "ingydotnet@@git-subrepo": "Shell", "intel@@tinycbor": "C", "kmackay@@micro-ecc": "C++", "kokke@@tiny-AES-c": "C", "solokeys@@cifra": "C", "ycrypto@@salty": "Rust", "davidhalter@@jedi": "Python", "nosami@@OmniSharpServer": "C#", "ross@@requests-futures": "Python", "kennethreitz@@requests": null, "Pylons@@waitress": "Python", "defnull@@bottle": "Python", "slezica@@python-frozendict": "Python", "duncanc@@Lua-for-AGS": "C++", "mongodb@@mongo-cxx-driver": "C++", "dlfcn-win32@@dlfcn-win32": "C", "kgabis@@parson": "C", "WinMerge@@frhed": "C++", "WinMerge@@winimerge": "C++", "WinMerge@@patch": "C", "WinMerge@@sevenzip": "C++", "WinMerge@@freeimage": "C", "htacg@@tidy-html5": "C", "stedolan@@jq": "C", "microsoft@@wil": "C++", "AimTuxOfficial@@imgui": "C", "abcminiuser@@lufa": "C", "sensepost@@esp-vnc": "C", "pfalcon@@esp-open-sdk": "<PERSON><PERSON><PERSON>", "AprilBrother@@esptool": "Python", "RoganDawes@@vncdotool": "Python", "meta-toolkit@@meta-libsvm": "C++", "julp@@FindICU.cmake": "C", "meta-toolkit@@meta-stlsoft": "C++", "joakimkarlsson@@bandit": "C++", "meta-toolkit@@meta-cmake": "CMake", "wichtounet@@make-utils": "<PERSON><PERSON><PERSON>", "wichtounet@@etl": "C++", "wichtounet@@mnist": "C++", "wichtounet@@nice_svm": "C++", "wichtounet@@cifar-10": "C++", "dreamworksanimation@@openvdb": "C++", "Tessil@@ordered-map": "C++", "google@@double-conversion": "C++", "curv3d@@libfive": "C++", "libgit2@@libgit2": "C", "basho@@leveldb_ee": "C++", "P-H-C@@phc-winner-argon2": "C", "walaj@@fermi-lite": "C", "walaj@@bwa": "C", "eosio@@berkeley-softfloat-3": "C", "Yubico@@yubihsm-shell": "C", "eosio@@eos-vm": "C++", "EOSIO@@eosio-wasm-spec-tests": "C++", "EOSIO@@abieos": "C++", "CopernicaMarketingSoftware@@AMQP-CPP": "C++", "eosio@@fc": "C++", "eosio@@chainbase": "C++", "eosio@@appbase": "C++", "frida@@frida-qml": "C++", "radareorg@@radare2": "C", "hjiang@@jsonxx": "C++", "ImageOptim@@libimagequant": "Rust", "dcdillon@@cpuaff": "C++", "vishnubob@@wait-for-it": "Python", "simongog@@libdivsufsort": "C", "ned14@@llfio": "C++", "ned14@@ntkernel-error-category": "C++", "mpromonet@@v4l2wrapper": "C++", "video-dev@@hls.js": "TypeScript", "wang-bin@@QtAV": "C++", "kazuho@@picotemplate": "<PERSON><PERSON>", "kazuho@@p5-Server-Starter": "<PERSON><PERSON>", "shibukawa@@oktavia": "JavaScript", "h2o@@cache-digest.js": "JavaScript", "deweerdt@@h2get": "C", "silentbicycle@@theft": "C", "h2o@@picotls": "C", "attractivechaos@@klib": "C", "hls-fpga-machine-learning@@example-models": "Python", "hughperkins@@clew": "C", "ardagnir@@vimbed": "Vim script", "dusty-nv@@jetson-utils": "C++", "dusty-nv@@camera-capture": "C++", "dusty-nv@@pytorch-classification": "Python", "dusty-nv@@pytorch-detection": null, "dusty-nv@@pytorch-segmentation": "Python", "dusty-nv@@trt_pose": "Python", "OpenBangla@@riti": "Rust", "PCSX2@@xz": "C", "rtissera@@libchdr": "C", "mozilla@@cubeb": "C++", "DS-Homebrew@@DLDI": "C", "mongodb@@specifications": "Python", "ziutek@@matrix": "Go", "redeclipse@@acerspyro": null, "redeclipse@@actors": null, "redeclipse@@appleflap": null, "redeclipse@@blendbrush": null, "redeclipse@@caustics": null, "redeclipse@@crosshairs": null, "redeclipse@@dc": null, "redeclipse@@decals": null, "redeclipse@@dziq": null, "redeclipse@@elyvisions": null, "redeclipse@@fonts": null, "redeclipse@@freezurbern": null, "redeclipse@@john": null, "redeclipse@@jojo": null, "redeclipse@@jwin": null, "redeclipse@@luckystrike": null, "redeclipse@@maps": null, "redeclipse@@mayhem": null, "redeclipse@@mikeplus64": null, "redeclipse@@misc": null, "redeclipse@@molexted": null, "redeclipse@@nieb": null, "redeclipse@@nobiax": null, "redeclipse@@particles": null, "redeclipse@@philipk": null, "redeclipse@@projectiles": null, "redeclipse@@props": null, "redeclipse@@q009": null, "redeclipse@@skyboxes": null, "redeclipse@@snipergoth": null, "redeclipse@@sounds": null, "redeclipse@@textures": null, "redeclipse@@torley": null, "redeclipse@@trak": null, "redeclipse@@ulukai": null, "redeclipse@@unnamed": null, "redeclipse@@vanities": null, "redeclipse@@vegetation": null, "redeclipse@@weapons": null, "TartanLlama@@libelfin": "C++", "antirez@@linenoise": "C", "CMU-Perceptual-Computing-Lab@@caffe": "C++", "logcabin@@logcabin": "C++", "rizinorg@@rizin": "C", "rizinorg@@cutter-translations": null, "hasherezade@@pe-sieve": "C++", "hasherezade@@paramkit": "C++", "mfragkoulis@@coreutils": "C", "mfragkoulis@@grep": "C", "mfragkoulis@@bash": "C", "lmb-freiburg@@lmbspecialops": "C++", "Esri@@lerc": "C++", "espressif@@ESP8266_RTOS_SDK": "C", "zhaojh329@@buffer": "C", "zhaojh329@@ssl": "C", "zhaojh329@@log": "C", "dynup@@kpatch-unit-test-objs": "Shell", "vovoid@@cal3d": "C++", "vovoid@@LZMA-SDK": "C++", "vovoid@@lzham_codec": "C++", "vovoid@@ftgl": "C++", "vovoid@@vsxu-dependencies": "C", "vovoid@@lodepng": "C++", "vovoid@@freetype2": "C", "zfsonlinux@@zfs-images": null, "cr-marcstevens@@sha1collisiondetection": "C", "UPBGE@@blender-addons": "Python", "acaudwell@@Core": "C++", "Maschell@@controller_patcher": "C++", "Maschell@@dynamic_libs": "C", "huggle@@mass-delivery": "C++", "huggle@@enwiki": "C++", "huggle@@extension-thanks": "C++", "huggle@@extension-splitter-helper": "C++", "huggle@@extension-mass-delete": "C++", "huggle@@extension-scoring": "C++", "grumpy-irc@@libirc": "C++", "huggle@@extension-flow": "C++", "openxc@@isotp-c": "C", "rurban@@smhasher": "C++", "leo-yuriev@@t1ha": "C", "google@@highwayhash": "C++", "wangyi-fudan@@wyhash": "C", "jbapple@@HalftimeHash": "C++", "flaviotordini@@idle": "C++", "flaviotordini@@media": "C++", "flaviotordini@@http": "C++", "flaviotordini@@updater": "C++", "flaviotordini@@js": "C++", "flaviotordini@@promises": "C++", "lhmouse@@asteria": "C++", "stp@@OutputCheck": "Python", "stp@@googletest": "C++", "apache@@incubator-weex-playground": "Java", "eclipse@@paho.mqtt.embedded-c": "C", "analogdevicesinc@@libtinyiiod": "C", "novnc@@noVNC": "JavaScript", "bjornbytes@@msdfgen": "C++", "bjornbytes@@ode": "C++", "WohlSoft@@LuaJIT": "C", "khronosgroup@@openxr-sdk": "C++", "boostorg@@regex": "C++", "microsoft@@GSL": "C++", "ethereum@@tests": "JavaScript", "ethereum@@dopple": "Python", "NVIDIAGameWorks@@Falcor": "C++", "ned14@@nedtries": "C++", "Taywee@@args": "C++", "mitsuba-renderer@@openexr": "C", "Tom94@@zlib": "C", "c42f@@tinyformat": "C++", "Tom94@@filesystem": "C++", "Tom94@@utfcpp": "C++", "Tom94@@tinylogger": "C++", "Tom94@@clip": null, "microsoft@@DirectXTex": "C++", "Tom94@@nanogui-1": null, "meganz@@sdk": "C++", "qian256@@HoloLensCamCalib": "C", "BalazsJako@@ImGuiColorTextEdit": "C++", "eidheim@@tiny-process-library": "C++", "github@@cmark-gfm": "C", "wjakob@@openexr": "C++", "mitsuba-renderer@@zlib": "C", "wdas@@ptex": "C", "carlonluca@@LightLogger": "C++", "pmj@@genccont": "C", "protocolbuffers@@protobuf": "C++", "c-ares@@c-ares": "C++", "google@@bloaty": "C++", "envoyproxy@@data-plane-api": "Starlark", "googleapis@@googleapis": "Starlark", "envoyproxy@@protoc-gen-validate": "Go", "census-instrumentation@@opencensus-proto": "Python", "open-telemetry@@opentelemetry-proto": "<PERSON><PERSON><PERSON>", "cncf@@xds": "Starlark", "redis@@hiredis": "C", "WebAssembly@@binaryen": "WebAssembly", "webassembly@@testsuite": "WebAssembly", "jpd002@@Play-Dependencies": "C", "jpd002@@Play--Framework": "C++", "jpd002@@Play--CodeGen": "C++", "jpd002@@Nuanceur": "C++", "jpd002@@libchdr": null, "jpd002@@AltKit": "Swift", "rapidsai@@gputreeshap": "C++", "golang@@sys": "Go", "robfig@@cron": "Go", "fsnotify@@fsnotify": "Go", "cihub@@seelog": "Go", "golang@@snappy": "Go", "syndtr@@goleveldb": "Go", "labstack@@echo": "Go", "dgrijalva@@jwt-go": "Go", "labstack@@gommon": "Go", "mattn@@go-colorable": "Go", "mattn@@go-isatty": "Go", "valyala@@fasttemplate": "Go", "golang@@crypto": "Go", "golang@@net": "Go", "ugorji@@go": "Go", "chigraph@@chigraph-tests": "C", "ngageoint@@hootenanny-ui": "JavaScript", "MailCore@@mailcore2": "C++", "dinhviethoa@@kvdb": "C", "dinhviethoa@@Sparkle": "Objective-C", "dinhviethoa@@PXSourceList": "Objective-C", "dinhviethoa@@Google-Analytics-for-OS-X": "Objective-C", "bitstadium@@HockeySDK-Mac": "Objective-C", "potionfactory@@LetsMove": "Objective-C", "Abizern@@NPReachability": "Objective-C", "facebook@@KVOController": "Objective-C", "ErwinJanssen@@graphviz-build-utilities": "M4", "Snaipe@@Criterion": "C", "ErwinJanssen@@graphviz-windows-dependencies": "C", "advancedfx@@advancedfx-prop": "C++", "advancedfx@@easywsclient": "C++", "advancedfx@@Detours": "C++", "advancedfx@@halflife": "C++", "advancedfx@@contrib": null, "advancedfx@@l10n": "Python", "advancedfx@@wxl-po-tools": null, "advancedfx@@rapidxml": "C++", "advancedfx@@injector": "C#", "texane@@stlink": "C", "pbatard@@libwdi": "C", "Softmotions@@EJDB2Swift": "Swift", "psi-im@@jdns": "C", "BradyBrenot@@screen_capture_lite": "C++", "RipcordSoftware@@libstringintern": "C++", "hsluv@@hsluv-c": "C", "thecherno@@yaml-cpp": "C++", "thecherno@@imguizmo": "C++", "cppit@@libclangmm": "C++", "signal11@@hidapi": "C", "zelmon64@@PS3EYEDriver": "C++", "boostorg@@type_index": "C++", "retf@@Boost.Application": "C++", "spurious@@SDL-mirror": "C", "mherb@@kalman": "C++", "cboulay@@libstem_gamepad": "C", "qmk@@ChibiOS": "C", "qmk@@ChibiOS-Contrib": "C", "qmk@@googletest": "C++", "qmk@@lufa": "C", "qmk@@v-usb": "C", "qmk@@printf": null, "sass@@libsass": "C++", "WasmVM@@wabt": "C++", "WasmVM@@testsuite-cases": "WebAssembly", "fasiondog@@hikyuu_extern_libs": "C", "haileysome@@twostroke": "JavaScript", "daedric@@commonpp": "C++", "vusec@@ramses": "C", "vysheng@@tgl": "C", "coderespawn@@prefabricator-samples-ue4": null, "umurmur@@umurmur-monitor": "C", "umurmur@@numurmon": "C", "kapilratnani@@rapidjson": "C++", "moonlight-stream@@moonlight-common-c": "C", "cgutman@@qmdnsengine": "C++", "gabomdq@@SDL_GameControllerDB": "Python", "cgutman@@libsoundio": "C", "aizvorski@@h264bitstream": "C", "cgutman@@moonlight-qt-prebuilts": "C", "Obijuan@@ArduSnake": "<PERSON><PERSON><PERSON><PERSON>", "hasherezade@@libpeconv": "C++", "MelvinGr@@LibCredentials": "C#", "libusb@@hidapi": "C", "vrpn@@jsoncpp": "C++", "psi-im@@iris": "C++", "psi-im@@libpsi": "C", "psi-im@@qhttp": "C++", "Ri0n@@qite": "C++", "phisko@@putils": "C", "h2o@@picohttpparser": "C", "Universal-Team@@Universal-Core": "C++", "dmbryson@@apto": "C++", "TrampolineRTOS@@ArduinoCore-avr": "C", "Instrumented-Pulpino@@pulpino": "C", "hpcc-systems@@xstyle": "JavaScript", "hpcc-systems@@put-selector": "JavaScript", "hpcc-systems@@dgrid": "JavaScript", "hpcc-systems@@libuv": "C", "hpcc-systems@@cpp-driver": "C++", "hpcc-systems@@librdkafka": "C", "hpcc-systems@@lz4": "C", "hpcc-systems@@libcouchbase-cxx": "C++", "hpcc-systems@@libcouchbase": "C++", "hpcc-systems@@hiredis": "C", "hpcc-systems@@tbb": "C++", "hpcc-systems@@aws-sdk-cpp": "C++", "hpcc-systems@@rapidjson": "C++", "hpcc-systems@@h3": "C", "hpcc-systems@@libantlr3c": "C", "hpcc-systems@@aeron": "Java", "hpcc-systems@@libyaml": "C", "hpcc-systems@@azure-storage-cpplite": "C++", "hpcc-systems@@spark-hadoop": "Python", "hpcc-systems@@spark-plugin-java-packages": null, "hpcc-systems@@jwt-cpp": null, "hpcc-systems@@nlp-engine": "C++", "hpcc-systems@@elasticlient": null, "ampl@@jacop": null, "Gecode@@gecode": "C++", "ampl@@asl": "C", "yifanlu@@substitute": "C", "DaveeFTW@@taihen-parser": "C", "wjakob@@nanovg": "C", "wjakob@@glfw": "C", "nigels-com@@glew": "C", "aliafshar@@esp32-wrover-kicad": null, "xenia-project@@xbyak": "C++", "benvanik@@binutils-ppc-cygwin": "Shell", "xenia-project@@SPIRV-Tools": "C++", "xenia-project@@premake-core": "C", "xenia-project@@snappy": "C++", "xenia-project@@premake-export-compile-commands": "<PERSON><PERSON>", "discordapp@@discord-rpc": "C++", "openluopworld@@aes_128": "C", "xenia-project@@capstone": "C", "xenia-project@@utfcpp": "C++", "xenia-project@@disruptorplus": "C++", "microsoft@@DirectXShaderCompiler": "C++", "Enhex@@premake-cmake": "<PERSON><PERSON>", "xenia-project@@FFmpeg": "C", "Triang3l@@premake-androidndk": "<PERSON><PERSON>", "bitshares@@bitshares-fc": "C++", "hall-lab@@svtyper": "Python", "GregoryFaust@@samblaster": "C++", "cc2qe@@vawk": "Python", "samtools@@tabix": "C", "ekg@@freebayes": "C++", "lh3@@bwa": "C", "cc2qe@@bamkit": "Python", "hall-lab@@lumpy-sv": "C", "abyzovlab@@CNVnator": "C++", "roc-streaming@@vendor": null, "numpy@@numpydoc": "Python", "davidhalter@@parso": "Python", "gorakhargosh@@watchdog": "Python", "ClickHouse-Extras@@poco": "C", "ClickHouse-Extras@@librdkafka": "C", "ClickHouse-Extras@@cctz": "C++", "ClickHouse-Extras@@zlib-ng": "C", "capnproto@@capnproto": "C++", "ClickHouse-Extras@@llvm": "C++", "ClickHouse-Extras@@mariadb-connector-c": "C", "ClickHouse-Extras@@jemalloc": null, "ClickHouse-Extras@@UnixODBC": "C", "ClickHouse-Extras@@protobuf": "C++", "ClickHouse-Extras@@boost": "C++", "ClickHouse-Extras@@Turbo-Base64": "C", "ClickHouse-Extras@@arrow": "C++", "ClickHouse-Extras@@libhdfs3": "C++", "ClickHouse-Extras@@libgsasl": "C", "ClickHouse-Extras@@libcxx": "C++", "ClickHouse-Extras@@libcxxabi": "C++", "ClickHouse-Extras@@snappy": "C++", "mfontanini@@cppkafka": "C++", "ClickHouse-Extras@@h3": "C", "ClickHouse-Extras@@hyperscan": "C++", "ClickHouse-Extras@@libunwind": "C++", "simdjson@@simdjson": "C++", "ClickHouse-Extras@@rapidjson": "C++", "ClickHouse-Extras@@fastops": "C++", "ClickHouse-Extras@@orc": null, "sparsehash@@sparsehash-c11": "C++", "ClickHouse-Extras@@grpc": "C++", "ClickHouse-Extras@@aws-sdk-cpp": "C++", "ClickHouse-Extras@@aws-c-event-stream": null, "ClickHouse-Extras@@aws-c-common": null, "ClickHouse-Extras@@aws-checksums": null, "ClickHouse-Extras@@icudata": "Assembly", "unicode-org@@icu": "C++", "ClickHouse-Extras@@flatbuffers": null, "ClickHouse-Extras@@replxx": "C++", "ClickHouse-Extras@@avro": "Java", "ClickHouse-Extras@@libcpuid": "C", "ClickHouse-Extras@@openldap": null, "ClickHouse-Extras@@AMQP-CPP": "C++", "ClickHouse-Extras@@cpp-driver": "C++", "ClickHouse-Extras@@libuv": null, "ClickHouse-Extras@@sentry-native": "C", "ClickHouse-Extras@@krb5": "C", "ClickHouse-Extras@@cyrus-sasl": null, "RoaringBitmap@@CRoaring": "C", "danlark1@@miniselect": "C++", "ClickHouse-Extras@@rocksdb": "C++", "ClickHouse-Extras@@abseil-cpp": "C++", "ClickHouse-Extras@@dragonbox": "C++", "fastfloat@@fast_float": "C++", "ClickHouse-Extras@@libpq": "C", "ClickHouse-Extras@@boringssl": "C", "ClickHouse-Extras@@NuRaft": "C++", "ClickHouse-Extras@@nanodbc": "C++", "ClickHouse-Extras@@datasketches-cpp": "C++", "ClickHouse-Extras@@yaml-cpp": "C++", "ClickHouse-Extras@@libstemmer_c": null, "ClickHouse-Extras@@wordnet-blast": null, "ClickHouse-Extras@@lemmagen-c": null, "ClickHouse-Extras@@libpqxx": "C++", "azadkuh@@sqlite-amalgamation": "C", "ClickHouse-Extras@@s2geometry": "C++", "ClickHouse-Extras@@bzip2": "C", "google@@libprotobuf-mutator": "C++", "ClickHouse-Extras@@sysroot": "C", "ClickHouse-Extras@@azure-sdk-for-cpp": "C++", "vysheng@@tl-parser": "C", "cuberite@@Core": "<PERSON><PERSON>", "cuberite@@ProtectionAreas": "<PERSON><PERSON>", "cuberite@@ChatLog": "<PERSON><PERSON>", "cuberite@@polarssl": "C", "cuberite@@SQLiteCpp": "C", "cuberite@@libevent": "C", "cuberite@@TCLAP": "C++", "cuberite@@cmake-coverage": "CMake", "cuberite@@expat": "C", "cuberite@@lua": "C++", "cuberite@@luaexpat": "C++", "cuberite@@luaproxy": "CMake", "cuberite@@sqlite": "C++", "cuberite@@toluapp": "<PERSON><PERSON>", "grafi-tt@@lunajson": "<PERSON><PERSON>", "cuberite@@libdeflate": "C", "nicklockwood@@GZIP": "Objective-C", "AFNetworking@@AFNetworking": "Objective-C", "clowwindy@@OpenSSL-for-iPhone": "C", "selsta@@hlsdl": "C", "coslyk@@danmaku2ass_cpp": "C++", "osmcode@@osm-testdata": "<PERSON>", "citizenfx@@jitasm": "C++", "citizenfx@@breakpad": "C++", "citizenfx@@udis86": null, "citizenfx@@libssh": "C++", "citizenfx@@curl": "C", "citizenfx@@leveldb": "C++", "citizenfx@@minhook": null, "citizenfx@@libuv": null, "nghttp2@@nghttp2": "C++", "freeminer@@enet": "C", "citizenfx@@imgui": "C++", "01org@@tbb": "C++", "Microsoft@@cpprestsdk": "C++", "citizenfx@@discord-rpc": null, "nanomsg@@nng": "C", "citizenfx@@node": "JavaScript", "dpirch@@libfvad": "C", "citizenfx@@webrtc-audio-processing": "C", "nmoinvaz@@minizip": "C", "electronicarts@@EASTL": "C++", "citizenfx@@yojimbo": "C", "citizenfx@@netcode.io": "C", "citizenfx@@reliable.io": "C", "citizenfx@@websocketpp": "C++", "SLikeSoft@@SLikeNet": "HTML", "blattersturm@@replxx": "C++", "inkooboo@@thread-pool-cpp": "C++", "citizenfx@@Ben.Demystifier": "C#", "cpp-netlib@@url": "C++", "citizenfx@@uvw": null, "uNetworking@@uWebSockets": "C++", "stbrumme@@toojpeg": "C++", "citizenfx@@bgfx": "C++", "bkaradzic@@bx": "C++", "bkaradzic@@bimg": "C++", "citizenfx@@nngpp": null, "blattersturm@@librw": null, "aap@@librwgta": "POV-Ray SDL", "LabSound@@LabSound": "C++", "citizenfx@@txAdmin": "JavaScript", "facebook@@folly": "C++", "citizenfx@@native-doc-tooling": "JavaScript", "cameron314@@concurrentqueue": "C++", "citizenfx@@xenium": null, "ngtcp2@@nghttp3": "C", "citizenfx@@lua-cmsgpack": null, "citizenfx@@lua-rapidjson": null, "citizenfx@@lua": null, "syoyo@@tinygltf": "C++", "electronicarts@@EABase": "C++", "emeryberger@@Hoard": "C++", "emeryberger@@Heap-Layers": "C++", "blattersturm@@jexl-rs": "Rust", "thorwe@@DSPFilters": "C++", "sisong@@HDiffPatch": "C", "xiph@@speexdsp": "C", "microsoft@@FX11": "C++", "john-chapman@@im3d": "C++", "citizenfx@@lmprof": null, "citizenfx@@rpmalloc": null, "Thalhammer@@jwt-cpp": "C++", "citizenfx@@fxcode": "TypeScript", "corellium@@corellium-android-unpacking": "Go", "osqp@@qdldl": "C", "mossmann@@libopencm3": "C", "greatscottgadgets@@gsg-kicad-lib": null, "rafael-santiago@@cutest": "C", "Andersbakken@@rct": "C++", "KDAB@@perfparser": "C++", "koenpoppe@@PrefixTickLabels": "C++", "vgteam@@fastahack": "C++", "vgteam@@gssw": "C", "vgteam@@cpp_progress_bar": "C++", "ekg@@lru_cache": "C++", "vog@@sha1": "C++", "jltsiren@@gcsa2": "C++", "vgteam@@sdsl-lite": "C++", "sparsehash@@sparsehash": "C++", "vgteam@@gfakluge": "C++", "vgteam@@DYNAMIC": "C++", "vgteam@@raptor": "C", "vgteam@@Complete-Striped-Smith-Waterman-Library": "C", "benedictpaten@@pinchesAndCacti": "C", "benedictpaten@@sonLib": "C", "vgteam@@fermi-lite": "C", "jltsiren@@gbwt": "C++", "yoheirosen@@sublinear-Li-Stephens": "C++", "adamnovak@@backward-cpp": "C++", "vgteam@@structures": "C++", "ocxtal@@dozeu": "C", "vgteam@@libhandlegraph": "C++", "vgteam@@libvgio": "C++", "vgteam@@libbdsg": "C++", "vgteam@@xg": "C++", "jltsiren@@gbwtgraph": "C++", "vgteam@@ips4o": "C++", "ekg@@mmmultimap": "C++", "vgteam@@BBHash": "C++", "nemequ@@simde-no-tests": "C", "mandreyel@@mio": "C++", "max0x7ba@@atomic_queue": "C++", "ekg@@tabixpp": "C++", "mapbox@@mason": "Python", "Kingcom@@filesystem": null, "diffblue@@java-models-library": "Java", "xmichelo@@XMiLib": "C++", "muan@@emojilib": "JavaScript", "BeamMW@@trezor-cpp-client": "C++", "Chatterino@@libcommuni": "C++", "jiakuan@@qBreakpad": "C++", "mohabouje@@WinToast": "C++", "pajlada@@settings": "C++", "pajlada@@signals": "C++", "pajlada@@serialize": "C++", "Chatterino@@qtkeychain": "C++", "gpac@@testsuite": "Shell", "bagder@@c-ares": "C++", "psas@@elderberry": "C", "scrosby@@OSM-binary": "Java", "tronkko@@dirent": "C", "osmcode@@libosmium": "C++", "rxi@@microtar": "C", "RPGillespie6@@fastcov": "Python", "vthiery@@cpp-statsd-client": "CMake", "fpoussin@@libusb": "C", "pmwkaa@@sophia": "C", "c-util@@c-rbtree": "C", "c-util@@c-list": "C", "c-util@@c-dvar": "C", "c-util@@c-utf8": "C", "c-util@@c-shquote": "C", "c-util@@c-ini": "C", "c-util@@c-stdaux": "C", "warmspringwinds@@ATen": "C", "citra-emu@@ext-boost": "C++", "neobrain@@nihstro": "C++", "citra-emu@@dynarmic": "C++", "lsalzman@@enet": "C", "arun11299@@cpp-jwt": "C++", "wwylele@@teakra": "C++", "lvandeve@@lodepng": "C++", "lemenkov@@libyuv": "C++", "nemequ@@simde": "C", "phusion@@passenger_rpm_automation": "HTML", "phusion@@passenger_apt_automation": "C", "phusion@@cxxcodebuilder": "<PERSON>", "phusion@@passenger_binary_build_automation": "Shell", "phusion@@cxx_hinted_parser": "<PERSON>", "phusion@@passenger_homebrew_automation": "<PERSON>", "katef@@kmkf": "<PERSON><PERSON><PERSON>", "BOINC@@boinc": "PHP", "mortbopet@@VSRTL": "C++", "serge1@@ELFIO": "C++", "mortbopet@@libelfin": "C++", "adrienverge@@openfortivpn": "<PERSON><PERSON>", "VcDevel@@vc-testdata": "C++", "mattkretz@@virtest": "C++", "bitcoin@@libblkmaker": "C", "KnCMiner@@knc-asic": "C", "tailhook@@coyaml": "C", "tailhook@@libwebsite": "C", "mitsuba-renderer@@nanogui": "C++", "vgmtrans@@vgmtrans-ext-win": "C++", "adium@@MMTabBarView": "Objective-C", "wangyu-@@UDPspeeder": "C++", "Nils-TUD@@GIMMIX": "Assembly", "Nils-TUD@@ECO32": "C", "valenok@@mongoose": "C", "jdarpinian@@LibOVR": "C++", "h2o@@picotest": "C", "XadillaX@@xmempool": "C++", "simonask@@libevhtp": "C", "simonask@@synth": "C++", "Xaymar@@cmake-clang": "CMake", "Xaymar@@cmake-codesign": "CMake", "Xaymar@@msvc-redist-helper": "C++", "NVIDIA@@MAXINE-AR-SDK": "C", "NVIDIA@@MAXINE-VFX-SDK": "C", "paulcbetts@@NuGet": "C#", "beehive-lab@@pie": "<PERSON>", "caolan@@nodeunit": "JavaScript", "mishoo@@UglifyJS": "JavaScript", "ddnet@@ddnet-libs": "C", "JoakimSoderberg@@coveralls-cmake": "CMake", "onnx@@onnx-tensorrt": "C++", "taisei-project@@SDL_GameControllerDB": "Python", "taisei-project@@koishi": "Assembly", "taisei-project@@basis_universal": "C++", "taisei-project@@python-zipfile-zstd": "Python", "teeworlds@@teeworlds-translation": null, "teeworlds@@teeworlds-maps": null, "DISTRHO@@pugl": "C", "zyantific@@zycore-c": "C", "TorchCraft@@libzmq": "C++", "antze-k@@miso": "C++", "kuba--@@zip": "C", "virinext@@QHexView": "C++", "DynamoRIO@@dynamorio": "C", "DynamoRIO@@googletest": "C++", "s1lentq@@CSSDK": "C++", "topjohnwu@@selinux": "C", "topjohnwu@@ndk-busybox": "C", "dgibson@@dtc": "C", "topjohnwu@@mincrypt": "C", "iqiyi@@xHook": "C", "topjohnwu@@libcxx": "C++", "greg7mdp@@parallel-hashmap": "C++", "termux@@termux-elf-cleaner": "C", "HeliumProject@@Bullet": "C++", "HeliumProject@@FreeType": "C", "HeliumProject@@glfw": "C", "HeliumProject@@glew": "C", "HeliumProject@@googletest": "C++", "HeliumProject@@libpng": "C", "HeliumProject@@NvidiaTextureTools": "C++", "HeliumProject@@mongo-c": "C", "HeliumProject@@OIS": "C++", "HeliumProject@@premake-core": "C", "HeliumProject@@P4API": "C++", "HeliumProject@@rapidjson": "C++", "HeliumProject@@wxWidgets": "C++", "HeliumProject@@zlib": "C", "HeliumProject@@Core": "C++", "acama@@libxdisasm": "C", "switchbrew@@libnx": "C", "vmtk@@vmtk-test-data": null, "colobot@@colobot-data": "Python", "percona@@galera": "C++", "percona@@wsrep-lib": "C++", "Percona-Lab@@coredumper": "C", "backtrace-labs@@umash": "Python", "mikrosimage@@concurrent_utils": "C++", "gchatelet@@light_sequence_parser": "C++", "ricardoquesada@@libfreetype": "C", "polybar@@i3ipcpp": "C++", "polybar@@xpp": "C++", "aspnet@@FileSystem": "C#", "tectonic-typesetting@@tectonic-staging": "C", "harfbuzz@@harfbuzz": "C++", "vhelin@@wla-dx": "C", "tatfook@@dxEffects2glEffects": "C", "tatfook@@glEffects": "C++", "xianyi@@OpenBLAS": "Fortran", "JuliaLang@@openlibm": "C", "ElunaLuaEngine@@Eluna": "C++", "google@@google-drive-proxy": "C#", "andryblack@@fontbuilder": "C++", "abcminiuser@@dmbs": "<PERSON><PERSON><PERSON>", "google@@glslang": null, "google@@shaderc": "C++", "KhronosGroup@@SPIRV-Tools": "C++", "SuperHouse@@esp-open-rtos": "C", "HomeACcessoryKid@@esp-wifi-config": "C", "greatwolf@@sqlite3": "C", "VCVRack@@nanovg": "C", "memononen@@nanosvg": "C", "AndrewBelt@@osdialog": "C", "VCVRack@@oui-blendish": "C", "VCVRack@@rtaudio": "C++", "VCVRack@@glfw": "C", "nickgillian@@grt": "C++", "kazuho@@picotest": "C", "google@@cpu_features": "C++", "szagoruyko@@cunnproduction": "C++", "euroelessar@@qutim-translations": "CMake", "euroelessar@@k8json": "C++", "euroelessar@@jreen": "C++", "qutIM@@artwork": null, "gorthauer@@vreen": "C++", "gorthauer@@QtDockTile": "CSS", "qutIM@@qbs-labs": "JavaScript", "gorthauer@@Controls.Experimental": "JavaScript", "Ralim@@usb-pd": "C++", "vslavik@@Sparkle": "Objective-C", "vslavik@@wxWidgets": "C++", "vslavik@@LucenePlusPlus": "C++", "GroundControl-Solutions@@LetsMove": "Objective-C++", "CLD2Owners@@cld2": "C++", "mity@@mctrl": "C", "wisk@@samples": null, "pipeacosta@@traci4matlab": "MATLAB", "DLR-TS@@homebrew-sumo": "<PERSON>", "lcodeca@@SUMOActivityGen": "Python", "esnme@@ujson4c": "C", "jwmatthys@@rtcmix-in-pd": "C++", "pd-l2ork@@cwiid": "C", "pd-projects@@lyonpotpourri": "Pure Data", "ericlyon@@fftease3.0-32bit": "C", "pd-l2ork@@miXed": "C", "JaredCJR@@cmsis": "C", "yuvadm@@stellaris": "C", "tandasat@@HyperPlatform": "C++", "alex85k@@wingetopt": "C", "google@@tracing-framework": "JavaScript", "dcnieho@@FreeGLUT": "C", "google@@omaha": "C++", "google@@image-compression": "C++", "google@@fonts": "HTML", "away3d@@away3d-examples-broomstick": "ActionScript", "googlei18n@@noto-cjk": "Shell", "davejrichardson@@OpenCTM": "C", "datenwolf@@linmath.h": "C", "aantron@@better-enums": "C++", "Vita3K@@ext-boost": "C++", "KorewaWatchful@@crypto-algorithms": "C", "Vita3K@@dlmalloc": "C++", "Vita3K@@ffmpeg-core": "C", "ocornut@@imgui_club": "C", "Vita3K@@libfat16": "C++", "jonasmr@@microprofile": "C", "Vita3K@@nativefiledialog-cmake": "C", "Vita3K@@printf": "C++", "Vita3K@@sdl": "C", "tcbrindle@@sdl2-cmake-scripts": "CMake", "Vita3K@@unicorn": "C", "vitasdk@@vita-toolchain": "C", "Vita3K@@psvpfstools": "CMake", "Vita3K@@dynarmic": "C++", "Thealexbarney@@LibAtrac9": "C#", "linux-test-project@@ltp": "C", "nfrechette@@sjson-cpp": "C++", "nfrechette@@rtm": "C++", "pmp-library@@pmp-data": null, "google@@cctz": "C++", "ondrejbudai@@libhidx": "C++", "radiotap@@radiotap-library": "C", "ChrislS@@SConsChecks": "Python", "thestinger@@util": "C++", "chenxiaolong@@Android-Terminal-Emulator": "Java", "chenxiaolong@@minizip": "C", "chenxiaolong@@RootShell": "Java", "chenxiaolong@@googletest": "C++", "chenxiaolong@@outcome": "Assembly", "tihmstar@@jssy": "C", "skvadrik@@REgen": "Java", "nanocurrency@@lmdb": "C", "nanocurrency@@phc-winner-argon2": "C", "cryptocode@@cpptoml": "C++", "nanocurrency@@nano-pow-server": "C++", "nanocurrency@@rocksdb": null, "nanocurrency@@diskhash": "C++", "mongodb@@mongo-c-driver": "C", "anonimal@@cpp-netlib": "C++", "anonimal@@cryptopp": "C++", "monero-project@@kovri-docs": null, "monero-project@@miniupnp": "C", "itdaniher@@kissfft": "C", "npedotnet@@TGAReader": "Java", "moai@@gvr-ios-sdk": "C++", "artoolkit@@artoolkit5": "C++", "memononen@@libtess2": "C", "zorgnax@@libtap": "C", "maxmind@@MaxMind-DB": "<PERSON><PERSON>", "hzeller@@rpi-rgb-led-matrix": "C++", "hzeller@@spixels": "C++", "WeAreROLI@@JUCE": "C++", "GerHobbelt@@pthread-win32": "C", "pierreguillot@@pure-data": "C", "medooze@@media-server": "C++", "medooze@@mp4v2": "C++", "cisco@@libsrtp": "C", "an-tao@@trantor": "C++", "mmatyas@@pegasus-frontend-translations": null, "mmatyas@@pegasus-theme-grid": "QML", "mmatyas@@SortFilterProxyModel": "C++", "bear101@@tinyxml": "C++", "BearWare@@testdata": null, "hiikezoe@@libdiagexploit": "C", "android-rooting-tools@@libfb_mem_exploit": "C", "android-rooting-tools@@libkallsyms": "C", "android-rooting-tools@@libdiagexploit": "C", "android-rooting-tools@@libfj_hdcp_exploit": "C", "fi01@@libget_user_exploit": "C", "fi01@@libmsm_acdb_exploit": "C", "fi01@@libmsm_cameraconfig_exploit": "C", "android-rooting-tools@@libperf_event_exploit": "C", "fi01@@libput_user_exploit": "C", "android-rooting-tools@@libfutex_exploit": "C", "android-rooting-tools@@libexploit_utils": "C", "trezor@@trezor-crypto": "C", "trezor@@trezor-common": "Python", "trezor@@trezor-storage": "C", "CanonicalLtd@@grpc": "C++", "canonical@@yaml-cpp": "C++", "CanonicalLtd@@libssh": "C", "CanonicalLtd@@semver": "C++", "ricab@@scope_guard": "C++", "pocoproject@@poco": "C", "pfalcon@@axtls": "C", "atgreen@@libffi": "C", "pfalcon@@berkeley-db-1.xx": "C", "micropython@@stm32lib": "C", "rime@@librime": "C++", "rime@@plum": "Shell", "eagleas@@mongo-c-driver": "C", "xyzz@@acquisition-redist": null, "ericsium@@boost-header-only": "C++", "BLAKE2@@BLAKE2": "C", "SoftEtherVPN@@libhamcore": "C", "medooze@@libdatachannels": "C++", "DaveGamble@@cJSON": "C", "Azure@@azure-c-shared-utility": "C", "andrewrk@@libsoundio": "C", "microsoft@@libuvc": "C", "gdnsd@@gdnsd-geoip-testdata-v3": null, "d0k3@@BrahmaLoader": "C", "mid-kid@@CakesROP": "C", "dukesrg@@CakeHax": "C", "b1l1s@@2xrsa": "C", "infinitdotio@@couchdb-python": "Python", "mefyl@@bottle": "Python", "infinit@@sendwithus_python": "Python", "infinit@@grpc": "C", "infinit@@prometheus-cpp": "C++", "ms-iot@@serial-wiring": "C++", "hkalodner@@bitcoin-api-cpp": "C++", "wjakob@@dset": "C++", "mpark@@variant": "C++", "blockchain@@Blockchain-Known-Pools": null, "muellan@@clipp": "C++", "hkalodner@@mio": "C++", "hkalodner@@filesystem": "C++", "steinwurf@@endian": "C++", "citp@@testchain-generator": "Python", "bittorrent@@libbtdht": "C++", "RigsOfRods@@content": "1C Enterprise", "skypjack@@uvw": "C++", "Naios@@function2": "C++", "apache@@apr-util": null, "LibreOffice@@dictionaries": "Python", "editorconfig@@editorconfig-core-c": "C", "PhilipHazel@@pcre2": "C", "ogdf@@ogdf": "C++", "teragonaudio@@AudioTestData": null, "teragonaudio@@audiofile": "C++", "teragonaudio@@flac": "C", "mpreisler@@mingw-bundledlls": "Python", "naev@@naev-artwork-production": "Shell", "kritzikratzi@@ofxMightyUI": "C++", "kritzikratzi@@ofxAvCodec": "C", "kritzikratzi@@ofxFontAwesome": "C++", "kritzikratzi@@ofxFontStash2": "C++", "kritzikratzi@@ofxNative": "C++", "elliotwoods@@ofxSpout": "C++", "astellato@@ofxSyphon": "Objective-C", "kritzikratzi@@ofxLibsamplerate": "C", "kritzikratzi@@ofxLiblaserdock": "C", "rpm-software-management@@libdnf": "C++", "bagder@@curl": "C", "pwn20wndstuff@@Injector": "C", "sbingner@@snappy": "Objective-C", "pwn20wndstuff@@patchfinder64": "C", "sbingner@@offset-cache": "C", "sbingner@@kerneldec": "C++", "cjdoucette@@dpdk": "C", "cjdoucette@@bird": "C", "uglide@@qredisclient": "C++", "uglide@@pyotherside": "C++", "zeux@@volk": "C", "thisistherk@@fast_obj": "C++", "troydhanson@@uthash": "C", "adsr@@pcre": "C", "nekromant@@auraclient": "C", "osbean@@esp8266-json": "C", "Caerbannog@@esphttpclient": "C", "eadf@@esp8266_ping": "C", "zeromq@@zeromq4-x": "C++", "zeromq@@czmq": "C", "krb5@@krb5": "C", "gost-engine@@engine": "C", "mchck@@programmer": "<PERSON>", "domoticz@@jsoncpp": "C++", "eclipse@@mosquitto": "C", "domoticz@@minizip": "C", "RedisBloom@@t-digest-c": "C", "livecode@@livecode-ide": "<PERSON><PERSON><PERSON>", "livecode@@livecode-thirdparty": "C", "python@@cpython": "Python", "dotnet@@coreclr": null, "chaoticbob@@glfw": "C", "kayru@@rastafont": "C++", "mogemimi@@pomdog-third-party": "C", "WebAssembly@@testsuite": "WebAssembly", "dabeaz@@ply": "Python", "WebAssembly@@wasm-c-api": "C++", "nodejs@@uvwasi": "C", "KDAB@@android_openssl": "C", "hermitcore@@LwIP": "C", "hermitcore@@hermit-caves": "C", "univrsal@@libgamepad": "C++", "univrsal@@netlib": "C", "kwhat@@libuiohook": "C", "azure@@azure-uamqp-c": "C", "azure@@umock-c": "C", "azure@@azure-ctest": "C", "azure@@azure-c-testrunnerswitcher": "CMake", "azure@@azure-umqtt-c": "C", "Azure@@azure-iot-sdk-c": "C", "aseprite@@pixman": "C", "aseprite@@simpleini": "C++", "aseprite@@libwebp": "C", "aseprite@@flic": "C++", "aseprite@@freetype2": "C", "aseprite@@zlib": "C", "aseprite@@libpng": "C", "aseprite@@clip": "C++", "aseprite@@observable": "C++", "aseprite@@undo": "C++", "aseprite@@laf": "C++", "aseprite@@cmark": "C", "aseprite@@harfbuzz": "C++", "aseprite@@libarchive": "C", "aseprite@@json11": "C++", "aseprite@@benchmark": "C++", "aseprite@@giflib": "C", "aseprite@@fmt": "C++", "aseprite@@tinyexpr": "C", "aseprite@@lua": "C", "aseprite@@tga": "C++", "aseprite@@curl": "C", "machinezone@@IXWebSocket": "C++", "oceancx@@glew-cmake": "C", "oceancx@@freetype": "C", "oceancx@@glfw": "C", "oceancx@@lua-cmake": "C", "oceancx@@kbase-cmake": "C++", "oceancx@@ezio-cmake": "C++", "oceancx@@cxlua": "CMake", "FWGS@@mainui_cpp": "C++", "vincentlaucsb@@csv-data": "Python", "boostorg@@assert": "C++", "boostorg@@bind": "C++", "boostorg@@concept_check": "C++", "boostorg@@config": "C++", "boostorg@@core": "C++", "boostorg@@heap": "C++", "boostorg@@iterator": "C++", "boostorg@@static_assert": "C++", "boostorg@@throw_exception": "C++", "boostorg@@type_traits": "C++", "boostorg@@preprocessor": "C++", "boostorg@@mpl": "C++", "boostorg@@detail": "C++", "boostorg@@parameter": "C++", "boostorg@@mp11": "C++", "boostorg@@utility": "C++", "c-util@@c-siphash": "C", "stephenegriffin@@MAPIStubLibrary": "C", "jimloco@@Csocket": "C++", "znc@@znc-docker": "Dockerfile", "jpbruyere@@vkhelpers": "C++", "joyent@@node": null, "oleavr@@readline": "C", "Samsung@@libtuv": "C", "cgutman@@X1Kit": "Swift", "comex@@data": "C", "comex@@datautils0": "C++", "comex@@white": "C", "Gwion@@Gwion-plug": "C", "Gwion@@gwion-util": "C", "Gwion@@gwion-ast": "C", "euppal@@libcmdapp": "C", "Gwion@@gwion-fmt": "C", "neopenx@@DragonBoard": "JavaScript", "fastogt@@cmake": "CMake", "FreeRTOS@@coreMQTT": "C", "FreeRTOS@@coreHTTP": "C", "FreeRTOS@@coreJSON": "C", "aws@@device-shadow-for-aws-iot-embedded-sdk": "C", "aws@@jobs-for-aws-iot-embedded-sdk": "C", "aws@@device-defender-for-aws-iot-embedded-sdk": "C", "FreeRTOS@@corePKCS11": "C", "FreeRTOS@@backoffAlgorithm": "C", "aws@@ota-for-aws-iot-embedded-sdk": "C", "aws@@Fleet-Provisioning-for-AWS-IoT-embedded-sdk": "C", "aws@@SigV4-for-AWS-IoT-embedded-sdk": "C", "NVIDIA@@flownet2-pytorch": "Python", "shadowsocks@@ipset": "C", "shadowsocks@@libbloom": "C", "espressif@@esp32-camera": "C", "espressif@@esp-dl": "C++", "espressif@@esp-sr": "C", "KhronosGroup@@glTF-Sample-Models": "Mathematica", "decaf-emu@@libbinrec": "C", "exjam@@ovsocket": "C++", "decaf-emu@@gsl-lite": "C++", "decaf-emu@@addrlib": "C++", "exjam@@excmd": "C++", "decaf-emu@@cnl": null, "decaf-emu@@cpptoml": "C++", "yhirose@@cpp-peglib": "C++", "githubuser0xFFFF@@Qt-Advanced-Docking-System": "C++", "microsoft@@onnxruntime-tvm": "Python", "google@@nsync": "C", "tensorflow@@tensorboard": "TypeScript", "microsoft@@mimalloc": "C", "dcleblanc@@SafeInt": "C++", "apple@@coremltools": "Python", "emscripten-core@@emsdk": "Python", "microsoft@@onnxruntime-extensions": "C++", "apache@@tvm": "Python", "micropython@@axtls": "C", "NordicSemiconductor@@nrfx": "C", "adafruit@@asf4": "C", "hathach@@tinyusb": "C", "micropython@@mynewt-nimble": null, "bluekitchen@@btstack": "C", "hathach@@nxp_driver": "C", "jedisct1@@libhydrogen": "C", "kicad@@kicad-symbols": "CMake", "mateidavid@@fast5": "C++", "lh3@@minimap2": "C", "hasindu2008@@slow5lib": "C", "TartanLlama@@tl-cmake": "CMake", "cloudwu@@skynet": "C", "VioletGiraffe@@qtutils": "C++", "VioletGiraffe@@text-encoding-detector": "C++", "VioletGiraffe@@cpputils": "C++", "VioletGiraffe@@github-releases-autoupdater": "C++", "VioletGiraffe@@cpp-template-utils": "C++", "VioletGiraffe@@image-processing": "C++", "TarsCloud@@TarsFramework": "C++", "TarsCloud@@TarsCpp": "C++", "TarsCloud@@TarsJava": "Java", "tars-node@@Tars.js": null, "TarsPHP@@TarsPHP": "PHP", "TarsCloud@@TarsTup": "Objective-C", "TarsCloud@@TarsWeb": "JavaScript", "TarsCloud@@TarsGo": "Go", "TarsCloud@@TarsDocs": null, "TarsCloud@@TarsDocker": "Shell", "TarsCloud@@TarsDocs_en": null, "martinrotter@@transka": "Shell", "martinrotter@@7za": null, "martinrotter@@nsis": "NSIS", "Qfusion@@libRocket": "C++", "Qfusion@@angelscript": "HTML", "Qfusion@@qfusion-libsrcs": "C", "Qfusion@@nanosvg": "C", "Qfusion@@miniz": null, "carhartl@@jquery-cookie": "JavaScript", "drudru@@ansi_up": "JavaScript", "chncwang@@N3LDG-1": "C++", "khizmax@@libcds": "C++", "transmission@@arc4": "C", "transmission@@dht": "C", "transmission@@libb64": null, "transmission@@libevent": "C", "transmission@@libnatpmp": "C", "transmission@@libutp": "C++", "transmission@@miniupnpc": "C", "transmission@@utfcpp": null, "nomacs@@exiv2": "C++", "nomacs@@expat": "C", "nomacs@@quazip": "C++", "nomacs@@LibRaw": "C++", "nomacs@@imageformats": "CMake", "nomacs@@nomacs-plugins": "C++", "udoprog@@unc": "C++", "sasam@@M0517_flash_tools": "C", "wolftype@@gfx": "C++", "dusty-nv@@a3c_continuous": "Python", "JenniferBuehler@@gazebo-pkgs": "C++", "bats-core@@bats-core": "Shell", "ned14@@boost-trunk": "C++", "Unidata@@netcdf-c": "C", "shogun-toolbox@@shogun-data": "Objective-C", "shogun-toolbox@@shogun-debian": "Shell", "shogun-toolbox@@shogun-gpl": "C++", "tihmstar@@tsschecker": "C", "tihmstar@@idevicerestore": "C", "lewissbaker@@cake": "Python", "SuperV1234@@SSVMenuSystem": "C++", "SuperV1234@@SSVUtils": "C++", "SuperV1234@@vrm_pp": "C++", "SuperV1234@@SSVStart": "C++", "eliasdaler@@imgui-sfml": "C++", "averrin@@libmapgen": "C++", "agauniyal@@rang": "C++", "xroche@@coucal": "C", "buytenh@@ivykis": "C", "REDasmOrg@@REDasm-Library": "C++", "REDasmOrg@@REDasm-Plugins": "C++", "REDasmOrg@@REDasm-Loaders": "C++", "REDasmOrg@@REDasm-Assemblers": "C", "REDasmOrg@@REDasm-Database": "CMake", "KDAB@@KDDockWidgets": "C++", "ambrop72@@aipstack": "C++", "vlm@@asn1c": "C", "peters@@winpty": "C++", "strasdat@@Sophus": "C++", "banditcpp@@snowhouse": "C++", "Eyescale@@CMake": "CMake", "MariaDB@@mariadb-connector-c": "C", "codership@@wsrep-lib": "C++", "wolfSSL@@wolfssl": "C", "mariadb-corporation@@libmarias3": "C", "mariadb-corporation@@mariadb-columnstore-engine": "C++", "tree-sitter@@tree-sitter": "Rust", "tree-sitter@@tree-sitter-cpp": "JavaScript", "MrKepzie@@google-test": null, "MrKepzie@@google-mock": "C++", "MrKepzie@@SequenceParsing": "C++", "devernay@@openfx": "C++", "apache@@parquet-testing": null, "apache@@arrow-testing": "Shell", "ADLINK-IST@@MPC_ROOT": "<PERSON><PERSON>", "krzyzanowskim@@OpenSSL": "C", "zeromq@@zeromq3-x": "C++", "mediaelch@@mediaelch-doc": "Shell", "stachenov@@quazip": "C++", "d0k3@@3DS-Extended-Homebrew-Starter-Pack": "<PERSON><PERSON>", "d0k3@@ZIP3DSFX": "C", "Librevault@@libnatpmp": "C", "Librevault@@dht": "C", "AndrewGaspar@@corrosion": "CMake", "visualboyadvance-m@@dependencies": "C++", "espressif@@esp-adf-libs": "C", "Zeex@@subhook": "C", "x1nixmzeng@@cs_x86": "C#", "Cxbx-Reloaded@@XbSymbolDatabase": "C", "libtom@@libtommath": "C", "libtom@@libtomcrypt": "C", "tekknolagi@@libtap": "C", "arkhipenko@@TaskScheduler": "C++", "antirez@@redis": "C", "viaduck@@mariadbpp": "C++", "ddarriba@@pll-modules": "C", "amkozlov@@terraphast-one": "C++", "Aegisub@@wxWidgets": "C++", "svn2github@@googletest": "C++", "FFMS@@ffms2": "C++", "svn2github@@icu4c": "C++", "Aegisub@@fftw3": "C", "BYVoid@@uchardet": "C++", "ourairquality@@lwip": "C", "zserge@@jsmn": "C", "raburton@@rboot": "C", "sheinz@@fs-test": "C", "Zaltora@@crc_generic_lib": "C", "nochkin@@libesphttpd": "C", "nochkin@@multipwm": "C", "littlevgl@@lvgl": "C", "littlevgl@@lv_drivers": "C", "littlevgl@@lv_examples": "C", "nullworks@@source-sdk-2013-headers": "C++", "nullworks@@libglez": "C", "nullworks@@libxoverlay": "C", "nullworks@@simple-ipc": "C++", "nullworks@@MicroPather": "C++", "nullworks@@TF2_NavFile_Reader": "C++", "nullworks@@clip": "C++", "nullworks@@libnullnexus": "C++", "Orphis@@boost-cmake": "CMake", "DiligentGraphics@@DiligentCore": "C++", "DiligentGraphics@@DiligentTools": "C++", "DiligentGraphics@@DiligentSamples": "C++", "DiligentGraphics@@DiligentFX": "C", "PowerDNS@@pdns-builder": "Shell", "ceres-solver@@ceres-solver": "C++", "RainerKuemmerle@@g2o": "C++", "rmsalinas@@DBow3": "C++", "s-u@@REngine": "Java", "Sarcasm@@run-clang-format": "Python", "EQEmu@@recastnavigation": "C++", "MacPython@@terryfy": "Python", "ceph@@ceph-object-corpus": "Shell", "ceph@@jerasure": "C", "ceph@@gf-complete": "C", "ceph@@rocksdb": "C++", "ceph@@ceph-erasure-code-corpus": "Shell", "ceph@@googletest": "C++", "ceph@@spdk": "C", "ceph@@xxHash": "C", "ceph@@isa-l": "C", "01org@@isa-l_crypto": "Assembly", "ceph@@blkin": "C++", "ceph@@rapidjson": "C++", "ceph@@dmclock": "C++", "ceph@@seastar": "C++", "ceph@@fmt": "C++", "ceph@@c-ares": "C++", "ceph@@spawn": "C++", "ceph@@rook-client-python": "Python", "ceph@@s3select": "C++", "ceph@@libkmip": "C", "eliboa@@biskeydump": "C", "BVLC@@caffe": "C++", "provider-corner@@libprov": "C", "Zeex@@cmake-modules": "CMake", "Zeex@@samp-plugin-sdk": "C", "billyquith@@ponder": "C++", "atframework@@cmake-toolset": "CMake", "alicevision@@osi_clp": "C++", "alicevision@@MeshSDFilter": "C++", "alicevision@@nanoflann": "C++", "xrootd@@xrdcl-http": "C++", "xrootd@@xrootd-ceph": "C++", "p4lang@@p4runtime": "Python", "libbpf@@libbpf": "C", "RSATom@@ya-libvlc-wrapper": "C++", "jamplus@@jamplus": "C", "lltcggie@@caffe": "C++", "m6w6@@pecl-ci": "PHP", "fletcher@@MultiMarkdown-5": "C", "fireeye@@rvmi-qemu": "C", "fireeye@@rvmi-rekall": "Python", "fireeye@@rvmi-kvm": "C", "alsa-project@@alsa-lib": "C", "onetrueawk@@awk": "C", "videolan@@dav1d": "Assembly", "dosfstools@@dosfstools": "C", "rhinstaller@@efibootmgr": "C", "rhinstaller@@efivar": "C", "elftoolchain@@elftoolchain": "C", "oasislinux@@fspec-sync": "C", "libfuse@@libfuse": "C", "git@@git": "C", "oasislinux@@hotplugd": "C", "PJK@@libcbor": "C", "libffi@@libffi": "C", "oasislinux@@libfido2": "C", "oasislinux@@libinput": "C", "emersion@@libliftoff": "C", "thom311@@libnl": "C", "oasislinux@@libutp": "C", "xkbcommon@@libxkbcommon": "C", "leahneukirchen@@mblaze": "C", "oridb@@mc": "C", "mpv-player@@mpv": "C", "marlam@@msmtp-mirror": "C", "oasislinux@@mupdf": "C", "vapier@@ncompress": "C", "oasislinux@@netbsd-curses": "C", "oasislinux@@netsurf": "C", "ibara@@oksh": "C", "oasislinux@@openssh": "C", "madler@@pigz": "C", "tytso@@pwgen": "C", "benavento@@rc": "C", "michaelforney@@samurai": "C", "michaelforney@@sbase": "C", "michaelforney@@sdhcp": "C", "libretro@@snes9x2010": "C", "AgentD@@squashfs-tools-ng": "C", "michaelforney@@swc": "C", "oasislinux@@syslogd": "C", "ggreer@@the_silver_searcher": "C", "tinyalsa@@tinyalsa": "C", "eggert@@tz": "C", "michaelforney@@ubase": "C", "NLnetlabs@@unbound": "C", "usbids@@usbids": null, "JuliaLang@@utf8proc": "C", "michaelforney@@velox": "C", "martanne@@vis": "C", "michaelforney@@wld": "C", "gnif@@LGMP": "C", "gnif@@PureSpice": "C", "cimgui@@cimgui": "<PERSON><PERSON>", "hydrogen-music@@documentation": "HTML", "boostorg@@compute": "C++", "ColinH@@PEGTL": "C++", "eerimoq@@simba-esp32": "C", "eerimoq@@mbedtls": "C", "eerimoq@@atto": "C", "eerimoq@@xvisor": "C", "google@@effcee": "C++", "yasio@@ftp_server": "C++", "yasio@@thirdparty": "C++", "libgd@@libgd": "C", "altseed@@OpenSoundMixer": "C++", "altseed@@LLGI": "C++", "altseed@@AltseedRHI": "C++", "effekseer@@TestData": "Python", "effekseer@@ResourceData": null, "yse@@easy_profiler": "C++", "effekseer@@nativefiledialog": null, "effekseer@@imgui": null, "effekseer@@imgui-node-editor": "C++", "atheme@@libmowgli-2": "C", "atheme@@atheme-contrib-modules": "C", "arvidn@@libsimulator": "C++", "arvidn@@try_signal": "C++", "paullouisageneau@@boost-asio-gnutls": "C++", "petroules@@solar-cmake": "Python", "paulsapps@@googletest": "C++", "paulsapps@@libdeflate": "C", "paulsapps@@nativefiledialog": "C++", "paulsapps@@Detours": "C++", "paulsapps@@boost_1_63_mini": "C++", "paulsapps@@squirrel": "C++", "paulsapps@@sqrat": "C++", "memononen@@fontstash": "C", "paulsapps@@soxr-0.1.2": "C", "ericniebler@@meta": "C++", "pichenettes@@avril-firmware_tools": "Python", "Aethelflaed@@kompex-sqlite-wrapper": "C", "mousebird@@wg-resources-new": "Scheme", "clangen@@musikcube-bin": "Shell", "reagent@@buffer": "C", "chocolate-doom@@quickcheck": "<PERSON><PERSON><PERSON>", "nixers-projects@@urnnputs": "<PERSON><PERSON>", "ms-iot@@optee_os": "C", "ms-iot@@optee_client": "C", "openenclave@@oeedger8r-cpp": "C++", "openenclave@@openenclave-mbedtls": "C", "intel@@intel-sgx-ssl": "C", "openenclave@@openenclave-musl": "C", "hughperkins@@EasyCL": "C++", "hughperkins@@clBLAS": "C", "hughperkins@@boost-headers-lite": "C++", "hughsie@@PackageKit-Qt": "C++", "FreeRTOS@@FreeRTOS-Kernel": "C", "amazon-freertos@@pkcs11": null, "espressif@@esp-afr-sdk": "C", "Linaro@@freertos-pkcs11-psa": "C", "FreeRTOS@@FreeRTOS-Plus-TCP": "C", "FreeRTOS@@coreMQTT-Agent": "C", "awslabs@@aws-build-accumulator": "Python", "cypresssemiconductorco@@serial-flash": "C", "cypresssemiconductorco@@whd-bsp-integration": "C", "cypresssemiconductorco@@rgb-led": "C", "cypresssemiconductorco@@abstraction-rtos": "C", "cypresssemiconductorco@@psoc6cm0p": "C", "cypresssemiconductorco@@capsense": "C", "cypresssemiconductorco@@connectivity-utilities": "C", "cypresssemiconductorco@@wifi-host-driver": "C", "cypresssemiconductorco@@clib-support": "C", "cypresssemiconductorco@@psoc6make": "<PERSON><PERSON><PERSON>", "cypresssemiconductorco@@udb-sdio-whd": "C", "cypresssemiconductorco@@lpa": "C", "cypresssemiconductorco@@kv-store": "C", "Linaro@@freertos-ota-pal-psa": "C", "ed7coyne@@arduino-mock": "C++", "Yangqing@@ios-cmake": "CMake", "solrex@@android-cmake": "CMake", "freeminer@@default": "<PERSON><PERSON>", "kaadmy@@pixture": "Logos", "bji@@libs3": "C", "stxxl@@foxxll": "C++", "XorTroll@@Plutonium": "C++", "DarkMatterCore@@libusbhsfs": "C", "apiaryio@@drafter": "C++", "jcelerier@@QProgressIndicator": "C++", "jcelerier@@Qt-Color-Widgets": "C++", "Velron@@doxygen-bootstrapped": "JavaScript", "jcelerier@@phantomstyle": "C++", "jcelerier@@zipdownloader": "C++", "jcelerier@@magicitems": "C++", "jcelerier@@QCodeEditor": "C++", "jcelerier@@vst3_public_sdk": null, "jcelerier@@vst3_pluginterfaces": "C++", "steinbergmedia@@vst3_base": "C++", "jcelerier@@vst3_cmake": "CMake", "ossia@@libossia": "Max", "jcelerier@@libpd": "Objective-C", "jcelerier@@snappy": "C++", "jcelerier@@libsndfile": "C", "jcelerier@@Gist": "C++", "shinh@@8cc": "C", "koturn@@Whitespace": "C", "mirror@@tinycc": "C", "justinmeza@@lci": "C", "oneoo@@alilua-coevent-module": "C", "openresty@@luajit2": "C", "haka-security@@iniparser": "C", "haka-security@@lua": "C", "haka-security@@luaunit": "<PERSON><PERSON>", "haka-security@@breathe": "Python", "haka-security@@capstone": "C", "hjk41@@asio": "C++", "hughperkins@@coriander": "LLVM", "niessner@@mLib": "C++", "openstf@@android-libjpeg-turbo": "<PERSON><PERSON><PERSON>", "stratux@@dump1090": "C", "cyoung@@goflying": "Go", "sparkle-project@@Sparkle": "Objective-C", "tmk@@USB_Host_Shield_2.0": "C++", "DMattoon@@TSS.C": "C", "opentrack@@PS3EYEDriver": "C++", "jacobly0@@gif-h": "C", "kmammou@@v-hacd-data": "Batchfile", "01org@@linux-sgx-driver": "C", "01org@@linux-sgx": "C++", "morristech@@android-ifaddrs": "C", "BoostGSoC14@@boost.http": "C++", "Maratyszcza@@NNPACK": "C", "facebookincubator@@gloo": "C++", "Maratyszcza@@pthreadpool": "C++", "Maratyszcza@@FXdiv": "C++", "Maratyszcza@@FP16": "C++", "Maratyszcza@@psimd": "C++", "PeachPy@@enum34": "Python", "Maratyszcza@@PeachPy": "Python", "benjaminp@@six": "Python", "shibatch@@sleef": "C", "intel@@ideep": "C++", "NVIDIA@@nccl": "C++", "google@@gemmlowp": "C++", "pytorch@@QNNPACK": "C", "intel@@ARM_NEON_2_x86_SSE": "C", "pytorch@@fbgemm": "C++", "houseroad@@foxi": "C", "facebookincubator@@fbjni": "C++", "google@@XNNPACK": "C", "pytorch@@tensorpipe": "C++", "NVIDIA@@cudnn-frontend": "C++", "pytorch@@kineto": "HTML", "mreineck@@pocketfft": "C++", "driazati@@breakpad": "C++", "chrisvana@@common": "C++", "chrisvana@@third_party": "Python", "massar@@rfc6234": "C", "micro-os-plus@@sifive-templates-xpack": "Liquid", "micro-os-plus@@diag-trace-xpack": "C++", "micro-os-plus@@startup-xpack": "C++", "micro-os-plus@@c-libs-xpack": "C", "micro-os-plus@@cpp-libs-xpack": "C++", "micro-os-plus@@riscv-arch-xpack": "C++", "micro-os-plus@@sifive-devices-xpack": "C", "micro-os-plus@@sifive-arty-boards-xpack": "C++", "micro-os-plus@@sifive-hifive1-board-xpack": "C++", "micro-os-plus@@semihosting-xpack": "C++", "eldar@@deepcut": "Matlab", "pdinc-oss@@cpptest": "C++", "pdinc-oss@@gcovr": "Python", "FlagBrew@@sharkive": "Python", "ianlancetaylor@@libbacktrace": "C", "ElementsProject@@libwally-core": "C", "valyala@@gheap": "C++", "rustyrussell@@lnprototest": "Python", "NVIDIA@@cub": "<PERSON><PERSON>", "chikuzen@@ResizeHalf": "C++", "sekrit-twc@@zimg": "C++", "kanryu@@quazip": "C++", "kanryu@@qfullscreenframe": "C++", "kanryu@@qlanguageselector": "QML", "kanryu@@qnamedpipe": "C++", "mayanklahiri@@easyexif": "C++", "kanryu@@qactionmanager": "C++", "Exiv2@@exiv2": "C++", "kanryu@@luminor": "C++", "kanryu@@lib7zip": "C++", "grame-cncm@@faustlibraries": "OpenSCAD", "google@@oboe": "C++", "microsoft@@uf2": "JavaScript", "sonydevworld@@spresense-exported-sdk": "C", "hathach@@ti_driver": "C", "hathach@@microchip_driver": "C", "majbthrd@@nuc_driver": "C", "STMicroelectronics@@cmsis_device_f4": "C", "STMicroelectronics@@stm32f4xx_hal_driver": "C", "STMicroelectronics@@cmsis_device_f0": "C", "STMicroelectronics@@stm32f0xx_hal_driver": "C", "STMicroelectronics@@cmsis_device_f1": "C", "STMicroelectronics@@stm32f1xx_hal_driver": "C", "STMicroelectronics@@cmsis_device_f2": "C", "STMicroelectronics@@stm32f2xx_hal_driver": "C", "STMicroelectronics@@cmsis_device_f3": "C", "STMicroelectronics@@stm32f3xx_hal_driver": "C", "STMicroelectronics@@cmsis_device_f7": "C", "STMicroelectronics@@stm32f7xx_hal_driver": "C", "STMicroelectronics@@cmsis_device_h7": "C", "STMicroelectronics@@stm32h7xx_hal_driver": "C", "STMicroelectronics@@cmsis_device_l0": "C", "STMicroelectronics@@stm32l0xx_hal_driver": "C", "STMicroelectronics@@cmsis_device_l1": "C", "STMicroelectronics@@stm32l1xx_hal_driver": "C", "STMicroelectronics@@cmsis_device_l4": "C", "STMicroelectronics@@stm32l4xx_hal_driver": "C", "STMicroelectronics@@cmsis_device_g0": "C", "STMicroelectronics@@stm32g0xx_hal_driver": "C", "STMicroelectronics@@cmsis_device_g4": "C", "STMicroelectronics@@stm32g4xx_hal_driver": "C", "STMicroelectronics@@cmsis_device_l5": "C++", "STMicroelectronics@@stm32l5xx_hal_driver": "C", "gsteiert@@sct_neopixel": "C", "cmsis-packs@@cmsis-dfp-efm32gg12b": "C", "kkitayam@@rx_device": "C", "hathach@@nxp_lpcopen": "C", "NXPmicro@@mcux-sdk": "C", "hathach@@nxp_sdk": "C++", "Nuclei-Software@@nuclei-sdk": "C", "hathach@@mm32sdk": "C", "adafruit@@broadcom-peripherals": "<PERSON><PERSON>", "Infineon@@mtb-xmclib-cat3": "C++", "Mokosha@@FasTC-MSVCLibs": "C", "Palakis@@obs-ndi": "C", "Palakis@@obs-websocket": "C++", "shineframe@@shine_serial": "C++", "zhaozg@@lua-auxiliar": "C", "throwtheswitch@@unity": "C", "etnaviv@@galcore_headers": "C++", "flatpak@@xdg-dbus-proxy": "C", "msune@@libcdada": "C", "higepon@@mosh": "Scheme", "openscad@@MCAD": "OpenSCAD", "swagger-api@@swagger-codegen": "Mustache", "AtomicGameEngine@@CEF3Binaries": "C++", "AtomicGameEngine@@AtomicExamples": "JavaScript", "zer0fl4g@@Nanomite-Updater": "C++", "merces@@libpe": "C", "sylefeb@@LibSL-small": "C++", "openframeworks@@projectGenerator": "CSS", "openframeworks@@apothecary": "Shell", "aliceatlas@@buildstatic": "Shell", "imzhenyu@@rDSN.tools.explorer": "C++", "imzhenyu@@rDSN.tools.hpc": "C++", "imzhenyu@@rDSN.dist.service": "C++", "imzhenyu@@rDSN.dist.deployment": "C++", "imzhenyu@@rDSN.tools.log.monitor": "C++", "opentoonz@@opentoonz_plugin_utility": "C++", "ccp-project@@libccp": "C", "adafruit@@Adafruit_nRFCrypto": "C", "adafruit@@Adafruit_TinyUSB_Arduino": "C", "danielinux@@picotcp": "C", "insane-adding-machines@@frosted-userland": "C", "insane-adding-machines@@frosted-headers": "C", "insane-adding-machines@@unicore-mx": "C", "StanfordSNR@@libwebm": "C++", "StanfordSNR@@dist-for-puffer": "JavaScript", "StanfordSNR@@pensieve": "JavaScript", "sagar-pa@@abr_rl_test": "Python", "oatpp@@oatpp-lib": "C++", "oatpp@@oatpp-libressl": "C++", "oatpp@@oatpp-consul": "C++", "oatpp@@oatpp-kafka": "C++", "oatpp@@oatpp-swagger": "C++", "oatpp@@oatpp-curl": "C++", "tzutalin@@miniglog": "C++", "tzutalin@@dlib-android-app": "Java", "Blizzard@@civetweb": "C", "Blizzard@@s2client-proto": "Python", "jrepp@@ipv6-parse": "C", "jakogut@@mlibc": "C", "tevador@@RandomX": "C++", "monero-project@@supercop": "Assembly", "joyent@@eng": "JavaScript", "kcat@@openal-soft": "C++", "enginmanap@@nodeGraph": "C++", "TheOfficialFloW@@VitaShell": "C", "firebreath@@firebreath-boost": "C++", "taocpp@@PEGTL": "C++", "stan-dev@@stan": "C++", "stan-dev@@math": "C++", "OpenMS@@contrib": "CMake", "OpenMS@@THIRDPARTY": "XSLT", "OpenMS@@pyopenms-extra": "Python", "wxFormBuilder@@ticpp": "C++", "yixuan@@LBFGSpp": "C++", "boostorg@@lockfree": "C++", "boostorg@@predef": "C", "elnormous@@HTTPRequest": "C++", "lifting-bits@@lifting-tools-ci": "Python", "joakimkarlsson@@snowhouse": "C++", "ftylitak@@qzxing-test-resources": "Python", "wg-perception@@PartsBasedDetectorModels": null, "surge-synthesizer@@tuning-library": "C++", "simd-everywhere@@simde": "C", "surge-synthesizer@@eurorack": null, "ODDSound@@MTS-ESP": "C++", "surge-synthesizer@@JUCE": null, "OpenSIPS@@wolfssl": null, "fuzzylite@@fuzzylite": "C++", "PREF@@PrefLib": "C", "Dax89@@QHexEdit": "C++", "s-yata@@marisa-trie": "C++", "BYVoid@@OpenCC": "C++", "lotem@@capnproto": "C++", "ned14@@uthash": "C", "rusefi@@ChibiOS": "C", "ChibiOS@@ChibiOS-Contrib": "C", "rusefi@@kicad-libraries": "Python", "openscopeproject@@InteractiveHtmlBom": "Python", "posborne@@cmsis-svd": "Python", "mck1117@@wideband": "C", "pfalcon@@uzlib": "C", "rusefi@@lua": null, "rusefi@@luaaa": "C++", "rusefi@@openblt": "C", "rusefi@@hex2dfu": "C", "rusefi@@luaformatter": "Java", "openxc@@nxpUSBlib": "C", "openxc@@cJSON": "C", "openxc@@nxp-cdl": "C", "openxc@@nxp-bsp": "C", "peplin@@Arduino-Makefile": "<PERSON><PERSON><PERSON><PERSON>", "openxc@@emqueue": "C", "openxc@@AT-commander": "C", "openxc@@openxc-message-format": "C", "openxc@@uds-c": "C", "openxc@@STBTLE": "C", "openxc@@MLA": "C", "upx@@upx-lzma-sdk": "C++", "rain-1@@linenoise-mob": "C", "google@@corgi": "C++", "eric612@@Yolo-Model-Zoo": null, "awslabs@@aws-templates-for-cbmc-proofs": "<PERSON><PERSON><PERSON>", "awslabs@@aws-verification-model-for-libcrypto": "C", "moneymanagerex@@LuaGlue": "C++", "moneymanagerex@@html-template": "C++", "FVANCOP@@ChartNew.js": "JavaScript", "moneymanagerex@@database": "Shell", "utelle@@wxsqlite3": "C", "apexcharts@@apexcharts.js": "JavaScript", "sammycage@@lunasvg": "C++", "moneymanagerex@@themes": "CSS", "moneymanagerex@@general-reports": "<PERSON><PERSON>", "gdabah@@distorm": "C", "GlPortal@@glportal-data": "GLSL", "GlPortal@@RadixEngine": "C++", "GlPortal@@documentation": "CSS", "rsta2@@hostap": "C", "lvgl@@lvgl": "C", "lvgl@@lv_examples": "C", "pelya@@fakechroot": "C", "cedric-vincent@@PRoot": "C", "termux@@proot": "C", "CypherpunkArmory@@proot": "C", "mm2@@Little-CMS": "C", "Cyan4973@@FiniteStateEntropy": "C", "dfranx@@ImGuiColorTextEdit": "C++", "dfranx@@imgui": "C++", "dfranx@@assimp": "C++", "dfranx@@SPIRV-VM": "C", "dfranx@@ShaderExpressionParser": "C++", "dfranx@@SpvGenTwo": "C++", "ajweeks@@glm": "C++", "ajweeks@@bullet3": "C++", "Tencent@@phxpaxos": "C++", "Tencent@@phxrpc": "C++", "funkaster@@cxx-generator": "Python", "rapid7@@ReflectiveDLLInjection": "C", "rapid7@@meterpreter-deps": "C", "rapid7@@mimikatz": "C", "boostorg@@nowide": "C++", "JehanneOS@@devtools": "Go", "istarc@@mbed": "C", "istarc@@freertos": "C", "istarc@@cpputest": null, "istarc@@googletest": "C++", "MRPT@@nanogui": "C++", "OpenKinect@@libfreenect": "C", "MRPT@@rplidar_sdk": "C++", "jlblancoc@@nanoflann": "C++", "pantoniou@@libfyaml": "C", "akrzemi1@@Optional": "C++", "dropbox@@djinni": "C++", "libmx3@@json11": "C++", "mattstevens@@xcode-googletest": "Objective-C++", "moneroexamples@@xmregcore": "C++", "wangwenx190@@framelesshelper": "C++", "ztombol@@bats-support": "Shell", "ztombol@@bats-assert": "Shell", "mbrucher@@pybind11": "C++", "mbrucher@@GSL": "C++", "mbrucher@@eigen-git-mirror": "C++", "Fluorohydride@@ygopro-core": "C++", "Fluorohydride@@ygopro-scripts": "<PERSON><PERSON>", "apiaryio@@sundown": "C", "alliedmodders@@sourcepawn": "C++", "AndreRH@@wine": "C", "AndreRH@@qemu": "C", "opennetworklinux@@buildroot-mirror": "<PERSON><PERSON><PERSON>", "opennetworklinux@@linux-3.9.6": "C", "floodlight@@infra": "C", "floodlight@@bigcode": "C", "opennetworklinux@@linux-3.8.13": "C", "opennetworklinux@@build-artifacts": null, "area9innovation@@asmjit": "C++", "w-shackleton@@arp-scan": "C", "w-shackleton@@custom-android-iptables": "C", "audiofilter@@spuce": "C++", "beltoforion@@muparserx": "C++", "pothosware@@PothosPlotters": "C++", "pothosware@@PothosWidgets": "C++", "pothosware@@PothosFlow": "C++", "pothosware@@PothosSoapy": "C++", "pothosware@@PothosBlocks": "C++", "pothosware@@PothosPython": "C++", "pothosware@@PothosAudio": "C++", "pothosware@@PothosComms": "C++", "p12tic@@libsimdpp": "C++", "schoebel@@football": "Shell", "DNS-OARC@@pcap-thread": "C", "scipy@@scipy-mathjax": "JavaScript", "numpy@@SVML": "Assembly", "unittest-cpp@@unittest-cpp": "C++", "Achain-Dev@@lib": null, "marian-nmt@@marian-examples": "Shell", "marian-nmt@@marian-regression-tests": "Shell", "marian-nmt@@sentencepiece": "C++", "marian-nmt@@nccl": "C++", "marian-nmt@@FBGEMM": "C++", "marian-nmt@@intgemm": "C++", "marian-nmt@@Simple-WebSocket-Server": "C++", "degenerated1123@@bgfx-cmake": "CMake", "degenerated1123@@ZenLib": "C++", "REGoth-project@@CAB-Installer-Extractor": "C", "frabert@@libdmusic": "C", "TartanLlama@@optional": "C++", "samtools@@htscodecs": "C", "excamera@@alfalfa_test_vectors": null, "Tencent@@sqlcipher": "C", "barrywhitehat@@baby_jubjub_ecc": "C++", "kobigurk@@sha256_ethereum": "C++", "michalsrb@@android-ifaddrs": "C", "eProsima@@Fast-CDR": "C++", "reswitched@@unicorn": "C", "reswitched@@SwIPC": "Python", "Microsoft@@Delayed-Compensation-Asynchronous-Stochastic-Gradient-Descent-for-Multiverso": "C++", "ps4eye@@libusb": "C", "cnr-isti-vclab@@vcglib": "C++", "cnr-isti-vclab@@nexus": "C", "bkaradzic@@bgfx": "C++", "rttrorg@@rttr": "C++", "tandasat@@capstone": "POV-Ray SDL", "pimoroni@@rpi_ws281x": "C", "coronalabs@@submodule-box2d": "C++", "coronalabs@@submodule-CryptoPP": "C++", "coronalabs@@submodule-freetype": "C", "coronalabs@@submodule-live-libs": "C", "coronalabs@@submodule-lfs": "C", "coronalabs@@submodule-luasocket": "HTML", "coronalabs@@submodule-openal-soft_apportable": "C", "coronalabs@@submodule-plugins-build": null, "coronalabs@@submodule-plugins-build-core": null, "coronalabs@@submodule-plugins-gameNetwork": "<PERSON><PERSON>", "coronalabs@@submodule-plugins-licensing": "Shell", "coronalabs@@submodule-plugins-network": "C++", "coronalabs@@submodule-welcomescreen": "<PERSON><PERSON>", "coronalabs@@submodule-native": "<PERSON><PERSON><PERSON>", "coronalabs@@framework-easing": "<PERSON><PERSON>", "coronalabs@@framework-transition": "<PERSON><PERSON>", "coronalabs@@framework-widget": "<PERSON><PERSON>", "coronalabs@@framework-timer": "<PERSON><PERSON>", "coronalabs@@framework-composer": "<PERSON><PERSON>", "coronalabs@@openal-soft": "C++", "coronalabs@@metalangle": "C++", "eosio@@musl": "C", "eosio@@libcxx": "C++", "eosio@@llvm": "LLVM", "EOSIO@@berkeley-softfloat-3": "C", "effolkronium@@random": "C++", "bfgroup@@Lyra": "C++", "mikrosimage@@sequenceparser": "C++", "avTranscoder@@avTranscoder": "C++", "AVnu@@igb_avb": "C", "Aquantia@@atl_avb": "C", "klesh@@qt-phash": "C++", "FIX94@@fixNES": "C", "d-a-v@@esp82xx-nonos-linklayer": "C", "plerup@@espsoftwareserial": "C++", "ARMmbed@@littlefs": "C", "earlephilhower@@ESP8266SdFat": "C++", "pyserial@@pyserial": "Python", "arduino-libraries@@Ethernet": "C++", "igrr@@esp32-http-server": "C", "k0kubun@@ruby": "<PERSON>", "jcubic@@jquery.terminal": "JavaScript", "baotiao@@slash": "C++", "Qihoo360@@pink": "C++", "jasonsandlin@@DirectXTK": "C++", "dyne@@zuper": "Shell", "warmcat@@libwebsockets": "C", "dyne@@dnscrypt-proxy": "C", "dyne@@domain-list": "Shell", "firehol@@netdata": "C", "WayfireWM@@wf-config": "C++", "WayfireWM@@wf-utils": "C++", "WayfireWM@@wf-touch": "C++", "StanfordPL@@x64asm": "C++", "StanfordPL@@cpputil": "C++", "samdmarshall@@Core-Lib": "C", "martinmoene@@expected-lite": "C++", "martinmoene@@variant-lite": "C++", "martinmoene@@optional-lite": "C++", "martinmoene@@string-view-lite": "C++", "kennytm@@EcaFretni": "Python", "makerdiary@@nRF5-SDK-for-Mesh": "C", "qtumproject@@cpp-eth-qtum": "C++", "zoidrr@@scal-data": null, "rdp@@ruby_simple_gui_creator": "<PERSON>", "sifive@@freedom-metal": "C", "sifive@@elf2hex": "Shell", "sifive@@freedom-e-sdk-docs": "JavaScript", "sifive@@freedom-devicetree-tools": "C++", "sifive@@FreeRTOS-metal": "C", "sifive@@devicetree-overlay-generator": "Python", "sifive@@ldscript-generator": "Python", "sifive@@cmsis-svd-generator": "Python", "sifive@@openocdcfg-generator": "Python", "sifive@@esdk-settings-generator": "Python", "sifive@@scl-metal": "C", "wjakob@@pcg32": "C++", "wjakob@@tbb": "C++", "wjakob@@pss": "C++", "microsoft@@gsl": "C++", "throwtheswitch@@cmock": "C", "bitcraze@@libdw1000": "C", "rurban@@libuv": "C", "yoyow-org@@berkeley-softfloat-3": "C", "yoyow-org@@binaryen": "Assembly", "yoyow-org@@magic_get": "C++", "yoyow-org@@libcxx": "C++", "yoyow-org@@musl": "C", "yoyow-org@@wabt": "C++", "winlibs@@libjpeg": "C", "Armada651@@OpenAutomate": "C++", "Armada651@@glew": "C", "stinb@@libgit2": "C", "libssh2@@libssh2": "C", "apache@@incubator-tvm": "Python", "kpu@@intgemm": "C++", "oneapi-src@@oneDNN": "C++", "concurrencykit@@ck": "C", "FlagBrew@@PKSM-Scripts": "C", "FlagBrew@@PKSM-Core": "C++", "FlagBrew@@picoc": "C", "igraph@@igraph": "C", "ex3ndr@@telegram-api": "Java", "ex3ndr@@telegram-mt": "Java", "ex3ndr@@telegram-tl-core": "Java", "ex3ndr@@telegram-actors": "Java", "gtcasl@@hydrazine": "C++", "jackaudio@@example-clients": "C", "jackaudio@@headers": "C", "jackaudio@@tools": "C", "throwtheswitch@@cexception": "C", "portworx@@gperftools": "C++", "underhood@@mqtt_websockets": "C", "netdata@@aclk-schemas": "<PERSON><PERSON><PERSON>", "leggedrobotics@@darknet": "C", "ebruneton@@dimensional_types": "C++", "ebruneton@@progress_bar": "C++", "jrmuizel@@minpng": "C", "kmkolasinski@@QtnProperty": "C++", "neutrinolabs@@librfxcodec": "C", "neutrinolabs@@libpainter": "C", "ckaiser@@UGlobalHotkey": "C++", "ckaiser@@SingleApplication": "C++", "devinacker@@bsnes-plus-ext-qt": "C++", "LaurentGomila@@qt-android-cmake": "CMake", "Maximus5@@minhook": "C", "Maximus5@@cygwin-connector": "C++", "Maximus5@@json-parser": "C", "Maximus5@@RapidXml": "C++", "Maximus5@@googletest": "C++", "orlp@@ed25519": "C", "Yubico@@ykneo-openpgp": "Java", "Yubico@@ykneo-oath": "Java", "philipWendland@@IsoApplet": "Java", "licel@@jcardsim": "Java", "vletoux@@GidsApplet": "Java", "frankmorgner@@PivApplet": "Java", "frankmorgner@@vJCRE": "Java", "OpenSC@@OpenSC": "C", "nickrussler@@DevMsi": "HTML", "kanflo@@uhej-python": "Python", "nidium@@libapenetwork": "C", "asarium@@cmake-modules": "CMake", "asarium@@libRocket": "C++", "facebookincubator@@fizz": "C++", "facebook@@wangle": "C++", "facebook@@fbthrift": "C++", "firmata@@arduino": "C++", "maddinat0r@@samp-plugin-sdk": "C", "Zeex@@amx_assembly": "Pawn", "Seeed-Studio@@Grove_Drivers_for_Wio": "C++", "jonathan-beard@@cmdargs": "C++", "RaftLib@@shm": "C++", "RaftLib@@affinity": "C++", "RaftLib@@demangle": "C++", "jratcliff63367@@sse2neon": "C++", "juribeparada@@STM32F10X_Lib": "C", "rad1o@@libopencm3": "C", "rad1o@@hackrf": "KiCad", "mossmann@@hackrf": "C", "TheOfficialFloW@@taiHEN": "C", "jmcnamara@@libxlsxwriter": "C", "mumble-voip@@speex": "C", "mumble-voip@@celt-0.7.0": "C", "mumble-voip@@minhook": "C", "mumble-voip@@mach_override": "C", "mumble-voip@@speexdsp": "C", "mumble-voip@@rnnoise": "C", "Krzmbrzl@@FindPythonInterpreter": "CMake", "SuperV1234@@vrm_core": "C++", "soomla@@android-store": "Java", "soomla@@ios-store": "Objective-C", "soomla@@android-store-google-play": "Java", "soomla@@android-store-amazon": "Java", "nemtrif@@ftest": "C++", "notaz@@libpicofe": "C", "notaz@@warm": "C", "ANSSI-FR@@OVALI": "JavaScript", "ANSSI-FR@@ADCP-DirectoryCrawler": "C", "ANSSI-FR@@ADCP-libdev": "C", "zyantific@@zyan-disassembler-engine": "C", "ShoreTel-Inc@@erld_erlang_app": "Erl<PERSON>", "JohannesTaelman@@elfloader": "C", "Qucs@@qucs-test": "OpenEdge ABL", "Qucs@@qucsator": "C++", "acmol@@toft": "C++", "TrenchBroom@@vecmath": "C++", "ubawurinna@@freetype-windows-binaries": "C", "TrenchBroom@@BinaryLibs": "C", "zephyrproject-rtos@@zephyr": "C", "arkku@@ihex": "C", "iotivity@@iotivity-constrained": "C", "azonenberg@@logtools": "C++", "azonenberg@@xptools": "C++", "rdiankov@@collada_robots": null, "hcatlin@@libsass": "C++", "davidfstr@@discount": "C", "ips4o@@ips4o": "C++", "ekg@@paryfor": "C++", "Tessil@@hopscotch-map": "C++", "PipeWire@@pipewire": "C", "ndyer@@libusbdroid": "C", "mozilla@@gecko-dev": null, "blynn@@pbc": "C", "thegenemyers@@DALIGNER": "C", "Eureka22@@DAZZ_DB": "C", "thegenemyers@@DEXTRACTOR": "C", "thegenemyers@@DASCRUBBER": "C", "thestk@@rtmidi": "C++", "eteran@@qhexview": "C++", "10110111@@gdtoa-desktop": "C", "webmproject@@sjpeg": "C++", "google@@highway": "C++", "google@@libnop": "C++", "x42@@weakjack": "C", "jothepro@@doxygen-awesome-css": "CSS", "oriansj@@M2libc": "C", "3MFConsortium@@forks-libressl-distribution": "C", "flightlessmango@@minhook": "C", "bats-core@@bats-assert": "Shell", "bats-core@@bats-support": "Shell", "tralston@@bats-file": "Shell", "publicsuffix@@list": "Go", "rerrahkr@@emu2149": null, "pure-data@@pd-lib-builder": "<PERSON><PERSON><PERSON>", "flatsurf@@conda-snippets": "Shell", "coin3d@@cpack.d": "CMake", "coin3d@@soanydata": "<PERSON><PERSON><PERSON>", "coin3d@@sogui": "C++", "coin3d@@generalmsvcgeneration": "Shell", "kanedo@@gzstream": "C", "Yamakuzure@@pwx-elogind-migration-tools": "<PERSON><PERSON>", "mapnik@@geometry-test-data": "JavaScript", "x42@@robtk": "C", "adobe-type-tools@@psautohint-testdata": "PostScript", "taocpp@@config": "C++", "taocpp@@json": "C++", "orlp@@pdqsort": "C++", "ToruNiina@@toml11": "C++", "HDFGroup@@hdf5": "C", "krrishnarraj@@libopencl-stub": "C++", "GATB@@gatb-core": "C++", "YosysHQ@@nextpnr-tests": "Verilog", "SymbiFlow@@fpga-interchange-schema": "Cap'n Proto", "tenzir@@actor-framework": null, "corvusoft@@asio-dependency": "C++", "corvusoft@@catch-dependency": "C++", "corvusoft@@openssl-dependency": "C", "mustache@@spec": "<PERSON>", "no1msd@@headerize": "C++", "WebAssembly@@WASI": "Rust", "containers@@libocispec": "Python", "AFLplusplus@@unicornafl": "Rust", "AFLplusplus@@Grammar-Mutator": "Python", "AFLplusplus@@qemuafl": "C", "FlorentAvellaneda@@EvalMaxSAT": "C++", "NixOS@@patchelf": "C", "RICSecLab@@coresight-trace": "C", "nyx-fuzz@@libnyx": "Rust", "nyx-fuzz@@qemu-nyx": "C", "nyx-fuzz@@packer": "C", "WhisperSystems@@libsignal-protocol-c": "C", "hatukanezumi@@sombok-test-data": null, "samtools@@samtools": "C", "scottt@@debugbreak": "Python", "jowi24@@libfritzpp": "C++", "jowi24@@liblogpp": "C++", "jowi24@@libconvpp": "C++", "jowi24@@libnetpp": "C++", "pierreguillot@@pd.build": "CMake", "nfs-ganesha@@ntirpc": "C", "lancos@@qhexedit2": "C++", "Nitrokey@@hidapi": "C", "dimkr@@libwaive": "C", "agateau@@qpropgen": "Python", "shugo@@PDCurses": "C", "flatsurf@@antic": "C", "wbhart@@flint2": "C", "flatsurf@@unique-factory": "M4", "astropy@@astropy-helpers": "Python", "garybernhardt@@readygo": "<PERSON>", "hillbig@@esaxx": "C++", "frankmorgner@@OpenSC": "C", "apache@@mynewt-nimble": "C", "librestack@@BLAKE3": "Assembly", "NatronGitHub@@google-test": "C++", "NatronGitHub@@google-mock": "C++", "NatronGitHub@@SequenceParsing": "C++", "NatronGitHub@@google-breakpad": "C++", "cloose@@discount": "C", "cloose@@hunspell-mingw": "C++", "cloose@@hoedown": "C", "tlsfuzzer@@tlsfuzzer": "Python", "tlsfuzzer@@python-ecdsa": "Python", "tlsfuzzer@@tlslite-ng": "Python", "bazelbuild@@rules_cc": "Starlark", "bazelbuild@@rules_proto": "Starlark", "chromium@@mini_chromium": "C++", "solvespace@@libdxfrw": "C++", "solvespace@@pixman": "C", "solvespace@@cairo": "C", "solvespace@@angle": "C++", "codeplaysoftware@@computecpp-sdk": "C", "codeplaysoftware@@py_gen": "Python", "WuBingzheng@@libwuya": "C", "Nate711@@ChRt": "C", "greiman@@SdFat": "C++", "Nate711@@SparkFun_BNO080_Arduino_Library": "C++", "mattreecebentley@@plf_colony": "C++", "hrydgard@@pspautotests": "C", "hrydgard@@ppsspp-ffmpeg": "C", "hrydgard@@minidx9": "C++", "Kingcom@@armips": "C++", "hrydgard@@glslang": "C++", "hrydgard@@ppsspp-freetype": "C", "hrydgard@@miniupnp": "C", "hrydgard@@ppsspp-mac-sdl": "C", "unknownbrackets@@ppsspp-debugger": "JavaScript", "janbar@@openssl-cmake": "C", "TeamHypersomnia@@rectpack2D": "C++", "hunter-packages@@disabled-mode": "CMake", "FWGS@@vgui-dev": "C++", "ColinDuquesnoy@@QDarkStyleSheet": "Python", "libav@@libav": "C", "kenmcmil@@z3": "C++", "valinet@@libvalinet": "C", "kubo@@funchook": "C", "microsoft@@Detours": "C++", "valinet@@sws": "C", "valinet@@ep_dwm": "C", "hexops@@dawn": "C++", "iwatake2222@@InferenceHelper": "C++", "kotatogram@@lib_ui": "C++", "kotatogram@@cmake_helpers": "CMake", "KDE@@extra-cmake-modules": "CMake", "KDE@@plasma-wayland-protocols": "CMake", "gitlab-freedesktop-mirrors@@wayland-protocols": "<PERSON><PERSON>", "hmatuschek@@eigen3-nnls": "C", "kyamagu@@mexplus": "C++", "heiher@@hev-task-system": "C", "heiher@@yaml": "C", "heiher@@hev-socks5-core": "C", "syoyo@@tinyexr": "C++", "Qv2ray@@QvPlugin-Interface": "C++", "Qv2ray@@QJsonStruct": "C++", "kepka-app@@libtgvoip": "C++", "kepka-app@@rlottie": "C++", "kepka-app@@lib_crl": "C++", "kepka-app@@lib_rpl": "C++", "kepka-app@@lib_base": "C++", "kepka-app@@codegen": "C++", "kepka-app@@lib_ui": "C++", "kepka-app@@lib_lottie": "C++", "kepka-app@@lib_tl": "Python", "kepka-app@@lib_spellcheck": "C++", "kepka-app@@lib_storage": "C++", "kepka-app@@cmake_helpers": "CMake", "kepka-app@@lib_qr": null, "kepka-app@@libdbusmenu-qt": null, "kepka-app@@materialdecoration": null, "kepka-app@@qt5ct": null, "lxqt@@lxqt-qtplugin": "C++", "lxqt@@libqtxdg": "C++", "NativeScript@@common-runtime-tests-modules": "JavaScript", "NativeScript@@common-runtime-tests-app": "JavaScript", "NativeScript@@android-dts-generator": "Java", "juce-framework@@JUCE": "C++", "malus-security@@sandblaster": "Python", "planetbeing@@xpwn": "C", "sofacoustics@@API_Cpp": "C++", "moderngpu@@moderngpu": "C++", "NVIDIA@@thrust": "C++", "Jack-McDowell@@pe-sieve": "C++", "Jack-McDowell@@rules": null, "Jack-McDowell@@signature-base": null, "Jack-McDowell@@tinyxml2": null, "RWTH-HPC@@CMake-codecov": "CMake", "amallia@@QMX": "C++", "intel@@parallelstl": "C++", "pisa-engine@@Porter2": "C++", "pisa-engine@@warcpp": "C++", "pisa-engine@@trecpp": "C++", "pisa-engine@@KrovetzStemmer": "C++", "pisa-engine@@wapopp": "C++", "emil-e@@rapidcheck": "C++", "pisa-engine@@taily": "C++", "grit-engine@@grit-bullet": "C++", "grit-engine@@grit-lua": "C", "grit-engine@@grit-util": "C++", "grit-engine@@grit-ogre": "C++", "grit-engine@@grit-freeimage": "C", "grit-engine@@grit-windows-prebuilt-dependencies": "C", "caelan@@motion-planners": "Python", "dmlc@@HalideIR": "C++", "SirAnthony@@mhook": "C", "oci-labs@@googletest": null, "ayamir@@nvimdots": "<PERSON><PERSON>", "Atmosphere-NX@@Atmosphere-libs": "C++", "ffmpeginteropx@@zlib": "C", "ffmpeginteropx@@bzip2": "C", "ffmpeginteropx@@libiconv": "C", "ffmpeginteropx@@libxml2": null, "ffmpeginteropx@@liblzma": "C", "ffmpeginteropx@@PkgConfigFake": "C#", "videolan@@x265": "Assembly", "webmproject@@libvpx": "C", "FFmpeg@@gas-preprocessor": "<PERSON><PERSON>", "shiffthq@@buffer": "C", "shadowsocks@@libev": "Shell", "shiffthq@@logger": "C", "ortclib@@udns": "C", "MomoDeve@@EASTL": "C++", "MomoDeve@@assimp": "C++", "MomoDeve@@imgui": "C++", "MomoDeve@@glew-cmake": "C", "samhocevar@@portable-file-dialogs": "C++", "RuntimeCompiledCPlusPlus@@RuntimeCompiledCPlusPlus": "C++", "mariusbancila@@stduuid": "C++", "MomoDeve@@ImGuizmo": "C++", "MomoDeve@@rttr": "C++", "MomoDeve@@Allocators": "C++", "luncliff@@latch": "C++", "luncliff@@ssf": "C++", "riscv-collab@@riscv-binutils-gdb": "C", "riscv-collab@@riscv-gcc": null, "riscv-collab@@riscv-dejagnu": "TeX", "kode54@@mgba": "C", "adplug@@adplug": "C++", "adplug@@libbinio": "C++", "adplug@@database": "<PERSON><PERSON><PERSON>", "shpakovski@@MASShortcut": "Objective-C", "kode54@@libsidplayfp": "C++", "linuxdeploy@@linuxdeploy-desktopfile": "C++", "linuxdeploy@@cmake-scripts": "CMake", "UAVCAN@@dsdl": "Python", "n8vm@@generator": "C++", "owl-project@@owl": "C++", "n8vm@@assimp": "C++", "q2vr@@vrgamex86": "C", "q2vr@@mpgamex86": "C", "AliveTeam@@fluidsynth-lite": "C", "bw2012@@UnrealSandboxTerrain": "C++", "bw2012@@UnrealSandboxToolkit": "C++", "btzy@@nativefiledialog-extended": "C++", "capstone-engine@@capstone": "C", "WerWolv@@libromfs": "C++", "r-barnes@@DGGRID": "C++", "Xilinx@@Vitis_Libraries": "C++", "lukier@@camera_drivers": "C++", "dorian3d@@DBoW2": "C++", "jczarnowski@@vision_core": "C++", "borglab@@gtsam": "C++", "jczarnowski@@Pangolin": "C++", "laurentkneip@@opengv": "C++", "stevenlovegrove@@Sophus": "C++", "imazen@@freeimage": "C", "matus-chochlik@@oglplus": "C++", "SRombauts@@cpplint": "Python", "cosmoscout@@libtiff": "C", "cosmoscout@@opensg-1.8": "C++", "cosmoscout@@c-ares": "C++", "cosmoscout@@curl": "C", "cosmoscout@@curlpp": "C++", "cosmoscout@@freeglut": "C", "cosmoscout@@gli": "C++", "cosmoscout@@glm": "C++", "cosmoscout@@tinygltf": "C++", "cosmoscout@@vista": "C++", "cosmoscout@@git-archive-all.sh": "Shell", "cosmoscout@@spdlog": "C++", "cosmoscout@@civetweb": null, "xLAva@@OculusSDK": "C", "SpiderLabs@@secrules-language-tests": "<PERSON><PERSON>", "libinjection@@libinjection": "C", "SpiderLabs@@ModSecurity-Python-bindings": "Python", "ObKo@@stm32-cmake": "C", "google@@s2geometry": "C++", "analogdevicesinc@@linux": "C", "chemfiles@@lzma": "C", "honkstar1@@xdelta": "C", "hoffstadt@@DearPyGui_Ext": "Python", "ElementsProject@@secp256k1-zkp": "C", "brightray@@brightray": "C++", "google@@crc32c": "C++", "ton-blockchain@@libRaptorQ": "C++", "TKJElectronics@@KalmanFilter": "C++", "felis@@USB_Host_Shield_2.0": "C++", "Lauszus@@Arduino_Makefile_master": null, "Optiroc@@libSFX": "<PERSON><PERSON>", "mupfelofen-de@@SNESterm": "Assembly", "pulp-platform@@axi_mem_if": "SystemVerilog", "pulp-platform@@fpga-support": "SystemVerilog", "pulp-platform@@common_cells": "SystemVerilog", "pulp-platform@@axi": "SystemVerilog", "pulp-platform@@register_interface": "SystemVerilog", "pulp-platform@@apb_uart": "VHDL", "pulp-platform@@apb_node": "SystemVerilog", "pulp-platform@@axi2apb": "SystemVerilog", "pulp-platform@@axi_slice": "SystemVerilog", "pulp-platform@@tech_cells_generic": "SystemVerilog", "pulp-platform@@fpnew": "SystemVerilog", "lowRISC@@ariane-ethernet": "SystemVerilog", "pulp-platform@@axi_riscv_atomics": "SystemVerilog", "pulp-platform@@riscv-dbg": "SystemVerilog", "pulp-platform@@rv_plic": "SystemVerilog", "pulp-platform@@apb_timer": "SystemVerilog", "kabylkas@@dromajo": "C++", "pulp-platform@@common_verification": "SystemVerilog", "Leandros@@metareflect": "C++", "f0rki@@mapping-high-level-constructs-to-llvm-ir": "LLVM", "prh@@rules": "JavaScript", "fltk@@fltk": "C++", "OtherCrashOverride@@odroid-go-firmware": "C", "IlyaMZP@@emulator-launcher-odroid-go": "C", "OtherCrashOverride@@prosystem-odroid-go": "C", "OtherCrashOverride@@stella-odroid-go": "C", "OtherCrashOverride@@frodo-go": "C", "OtherCrashOverride@@go-play": "C", "pelle7@@odroid-go-pcengine-huexpress": "C", "mattkj@@super-go-play": "C", "pitpo@@go-play": "C", "pelle7@@odroid-go-spectrum-emulator": "C", "pelle7@@odroid-go-handy": "C", "ducalex@@retro-go": "C++", "apolukhin@@magic_get": "C++", "Panzerschrek@@panzer_ogl_lib": "C", "OpenHMD@@OpenHMD": "C", "termux@@termux-packages": "Shell", "sqlg@@sqlite-amalgamation": "C", "niftools@@nifdocsys": "Python", "qhull@@qhull": "C", "Cincinesh@@mon": "C++", "horsicq@@QHexView": "C++", "horsicq@@XPDF": "C++", "pdschubert@@WALi-OpenNWA": "C++", "pboettch@@json-schema-validator": "C++", "notscimmy@@libelevate": "C++", "cair@@deep-rts": "C++", "dimkr@@lolibc": "C", "google@@pienoon": "C++", "ot@@emphf": "C++", "jermp@@essentials": "C++", "jermp@@cmd_line_parser": "C++", "ipasimulator@@objc4": "Objective-C++", "ipasimulator@@apple-headers": "C", "ipasimulator@@libclosure": "C", "ipasimulator@@WinObjC": "Objective-C++", "ipasimulator@@llvm": "LLVM", "ipasimulator@@clang": "C++", "ipasimulator@@lld": "C++", "ipasimulator@@LIEF": "C++", "ipasimulator@@tapi": "C++", "ipasimulator@@lldb": "C++", "ipasimulator@@unicorn": "C", "ipasimulator@@libdispatch": "C", "ipasimulator@@Libffi": "C", "dryman@@spookyhash-c": "C", "dryman@@farmhash-c": "C", "mkj@@dropbear": "C", "kraj@@uClibc": "C", "mirror@@busybox": "C", "rofl0r@@proxychains-ng": "C", "jedisct1@@pure-ftpd": "C", "KoynovStas@@wsdd": "C", "roleoroleo@@mqttv4": "C", "GLVis@@data": null, "XboxDev@@extract-xiso": "C", "XboxDev@@nxdk-sdl": "C", "XboxDev@@nxdk-pdclib": "C", "XboxDev@@nxdk-libcxx": "C++", "libsdl-org@@SDL_ttf": "C", "libsdl-org@@SDL_image": "C", "XboxDev@@libusbohci": null, "AsahiLinux@@artwork": "<PERSON><PERSON><PERSON>", "marzer@@tomlplusplus": "C++", "HookedBehemoth@@Plutonium": "C++", "openzfs@@zfs-images": null, "monero-project@@monero": "C++", "dlbeer@@quirc": "C", "epezent@@implot": "C++", "etlegacy@@etlegacy-libs": "C", "macmade@@PIMPL": "C++", "macmade@@makelib": "<PERSON><PERSON><PERSON>", "macmade@@xcconfig": "Shell", "macmade@@XSTest": "C++", "2ndQuadrant@@pglogical_dump": "C", "earlephilhower@@ArduinoCore-API": "C++", "littlefs-project@@littlefs": "C", "raspberrypi@@pico-extras": "C", "earlephilhower@@Keyboard": "C++", "earlephilhower@@Mouse": "C++", "jspahrsummers@@xcconfigs": null, "Carthage@@workflows": "Shell", "ngsolve@@pybind11": "C++", "AmrikSadhra@@g3log": "C++", "jarikomppa@@soloud": "C", "hav4ik@@tinyai": "HTML", "yapb@@crlib": "C", "contiki-ng@@cc26xxware": "C", "contiki-ng@@cc13xxware": "C", "contiki-ng@@cooja": "Java", "contiki-ng@@tinydtls": "C", "contiki-ng@@example-lwm2m-standalone": "C", "contiki-ng@@motelist": "Python", "contiki-ng@@coresdk_cc13xx_cc26xx": "C", "contiki-ng@@cc2640r2-sdk": "C", "contiki-ng@@nrf-sdk-contiki-ng": "C", "kbladin@@ElkEngine": "C++", "espressif@@aws-iot-device-sdk-embedded-C": "C", "edk2-porting@@edk2-sdm845-binary": null, "tianocore@@edk2-platforms": "C", "BigfootACA@@simple-init": "C", "BoschSensortec@@BME280_driver": "C", "diyruz@@zstack-lib": "C", "LedgerHQ@@nanos-nonsecure-firmware": "C", "LedgerHQ@@nanos-nonsecure-firmware-releases": null, "aperezdc@@signify": "C", "stream-labs@@lib-streamlabs-ipc": "C++", "SLIBIO@@SLibExternalBin_FacebookSDK": "Objective-C", "SLIBIO@@SLibExternalBin_OpenSSL": "C", "SLIBIO@@SLibExternalBin_FFmpeg": "Shell", "SLIBIO@@SLibExternalBin_libmysqlclient": null, "SLIBIO@@SLibExternalBin_libcef": null, "SLIBIO@@SLibExternalBin_XgPush": "Objective-C", "SLIBIO@@SLibExternalBin_WeChat": "Objective-C", "SLIBIO@@SLibExternalBin_Firebase": "Objective-C", "SLIBIO@@SLibExternalBin_Alipay": "Objective-C", "pure-data@@pure-data": "C", "krzhu@@IdeaPlaceEx": "C++", "jayl940712@@ConstGen": "C++", "baloneymath@@anaroute": "C++", "jayl940712@@device_generation": "Python", "Microsoft@@Detours": "C++", "nanomq@@NanoNNG": "C", "SergiusTheBest@@plog": "C++", "sctplab@@usrsctp": "C", "paullouisageneau@@libjuice": "C", "pl8787@@mshadow": "C++", "foonathan@@debug_assert": "C++", "keystone-enclave@@keystone-sdk": "C", "keystone-enclave@@linux-keystone-driver": "C", "buildroot@@buildroot": "<PERSON><PERSON><PERSON>", "torvalds@@linux": "C", "qemu@@qemu": "C", "keystone-enclave@@sm": "C", "zensim-dev@@zpc": "C++", "zenustech@@libigl": "C++", "zenustech@@opensim-core": "C++", "jrmadsen@@pybind11": "C++", "jrmadsen@@googletest": null, "jrmadsen@@Caliper": "C++", "jrmadsen@@GOTCHA": "C", "NERSC@@LLVM-openmp": null, "jrmadsen@@line_profiler": null, "jrmadsen@@nccl-tests": null, "jrmadsen@@PTL": "C++", "jrmadsen@@hatchet": "Python", "jrmadsen@@dyninst": "C", "emilk@@loguru": "C++", "patriciogonzalezvivo@@ada": "C++", "YaLTeR@@SPTLib": "C", "HLTAS@@hlstrafe": "C++", "HLTAS@@taslogger": "C++", "CraigChat@@translations": null, "PlatformLab@@NanoLog": "C++", "Lukas-W@@qt5-x11embed": "C++", "mjansson@@rpmalloc": "C", "lmms@@zynaddsubfx": "C++", "lmms@@veal": "C++", "ArashPartow@@exprtk": "C++", "swh@@ladspa": "C", "tomszilagyi@@tap-plugins": "C", "meganz@@mingw-std-threads": "C++", "JohannesLorenz@@ringbuffer": "C++", "falktx@@carla": "C++", "simonowen@@resid": "C++", "jackaudio@@jack2": "C++", "lmms@@cmt": "C++", "arangodb@@velocypack": "C++", "majek@@libtask": "C", "rikyoz@@7-Zip": "C++", "ovilab@@atomify-lammps-examples": "Python", "AlloSphere-Research-Group@@allolib": "C++", "erikd@@libsamplerate": "C", "AlloSphere-Research-Group@@al_ext": "C++", "mlabbe@@nativefiledialog": "<PERSON><PERSON><PERSON>", "emp-toolkit@@emp-tool": "C++", "LatticeX-Foundation@@Rosetta-IO": "C++", "LatticeX-Foundation@@Rosetta-Log": "C++", "emp-toolkit@@emp-zk": "C++", "emp-toolkit@@emp-ot": "C++", "petersteneteg@@warn": "Python", "petersteneteg@@benchmark": "C++", "inviwo@@googletest": "C++", "inviwo@@glfw": "C", "inviwo@@glm": "C++", "pantor@@inja": "C++", "inviwo@@openexr": "C", "inviwo@@assimp": "C++", "inviwo@@freetype2": "C", "inviwo@@span": null, "LLNL@@units": "C++", "BreakfastQuay@@Rubberband": "C++", "lameproject@@lame": "C", "timothytylee@@libgsm": "C", "gwsystems@@ps": "C", "gwsystems@@ck": "C", "HyperDbg@@zydis": null, "HyperDbg@@phnt": null, "ciaa@@firmware.modules.rtos": "C", "ciaa@@firmware.modules.modbus": "C", "KhronosGroup@@Vulkan-LoaderAndValidationLayers": "C++", "tambry@@ImGuiRenderers": "C++", "romanbsd@@statsd-c-client": "C", "makelinux@@lib": "Shell", "averne@@oss-nvjpg": "C++", "flutter@@flutter": "Dart", "lewissbaker@@cppcoro": "C++", "sctplab@@sctp-idata": "C", "OpenVPN@@openvpn3": "C++", "OpenZeppelin@@openzeppelin-solidity": "JavaScript", "maxmind@@libmaxminddb": "C", "cloudflare@@boringtun": "Rust", "bittorrent@@libutp": "C++", "google@@flutter-desktop-embedding": "C++", "microsoft@@eEVM": "C++", "OpenVPN@@tap-windows6": "C", "mackyle@@sqlite": "C", "spc476@@SPCDNS": "C", "gpg@@libgpg-error": "C", "gpg@@libgcrypt": "C", "GNOME@@glib": "C", "llvm-mirror@@libcxxabi": "C++", "llvm-mirror@@libcxx": "C++", "bellard@@quickjs": "C", "chfast@@intx": "C++", "brave-intl@@challenge-bypass-ristretto-ffi": "C++", "flutter@@plugins": "Dart", "brainhub@@SHA3IUF": "C", "freedesktop@@cairo": "C", "freedesktop@@pixman": "C", "ethereum-lists@@chains": "<PERSON><PERSON><PERSON>", "flutter@@engine": "C++", "google@@vpn-libraries": "C++", "hanickadot@@compile-time-regular-expressions": "C++", "versatica@@mediasoup": "C++", "libp2p@@cpp-libp2p": "C++", "xDimon@@soralog": "C++", "Tessil@@hat-trie": "C++", "boost-ext@@di": "C++", "SqliteModernCpp@@sqlite_modern_cpp": "C++", "boostorg@@outcome": "C++", "kasmtech@@noVNC": "JavaScript", "Tencent@@flare": "C++", "steinbergmedia@@vst3_cmake": "CMake", "SergiusTheBest@@FindWDK": "CMake", "steinbergmedia@@vst3sdk": "CMake", "aminosbh@@sdl2-cmake-modules": "CMake", "lemire@@simdjson": "C++", "teenydt@@teenydt.github.io": "C", "hathach@@st_driver": "C", "Chlumsky@@msdfgen": "C++", "Chlumsky@@artery-font-format": "C++", "falkTX@@Carla-Plugins": "C++", "spicyjpeg@@mkpsxiso": "C++", "recp@@cglm": "C", "intel@@safestringlib": "C", "apache@@rocketmq-client-cpp": "C++", "polymonster@@maths": "C++", "polymonster@@pmbuild": "Python", "shadowsocks@@libancillary": "C", "shadowsocks@@simple-obfs": "C", "sogou@@workflow": "C++", "xdaimon@@ffts": "C", "xdaimon@@SimpleFileWatcher": "C++", "stanfordsnr@@gg-toolchain": "Shell", "stanfordsnr@@gg-test-vectors": "Assembly", "dimkanovikov@@KITScenaristCore": "C++", "adafruit@@Adafruit-GFX-Library": "C", "btx000@@WiFiEsp": "C++", "littlevgl@@lv_maixduino": "C", "billziss-gh@@libfuse": "C", "billziss-gh@@winfsp": "C", "billziss-gh@@lxdk": "C", "olikraus@@u8g2": "C", "fiam@@idf_wmonitor_lib": "C", "fiam@@wldb": "C", "REGoth-project@@BsZenLib": "C++", "GameFoundry@@bsf": "C++", "bitluni@@ESP32Lib": "C", "schwabe@@openvpn": "C", "schwabe@@platform_external_openssl": "C", "schwabe@@openvpn3": "C++", "MicrosoftDocs@@sdk-api": null, "rosflight@@mavlink_c_library": "C", "rosflight@@BreezySTM32": "C", "rosflight@@airbourne_f4": "C", "couchbase@@forestdb": "C++", "wiredtiger@@wiredtiger": "C", "symisc@@vedis": "C", "Softmotions@@iowow": "C", "cruppstahl@@upscaledb": "C++", "symisc@@unqlite": "C", "chenyahui@@beats-annotated": "Go", "foo86@@dcadec-samples": null, "STMicroelectronics@@STMems_Machine_Learning_Core": "C", "STMicroelectronics@@STMems_Finite_State_Machine": "C", "STMicroelectronics@@a3g4250d": "C", "STMicroelectronics@@ais2dw12": "C", "STMicroelectronics@@ais2ih": "C", "STMicroelectronics@@ais328dq": "C", "STMicroelectronics@@ais3624dq": "C", "STMicroelectronics@@asm330lhh": "C", "STMicroelectronics@@h3lis100dl": "C", "STMicroelectronics@@h3lis331dl": "C", "STMicroelectronics@@hts221": "CSS", "STMicroelectronics@@i3g4250d": "C", "STMicroelectronics@@iis2dh": "C", "STMicroelectronics@@iis2dlpc": "C", "STMicroelectronics@@iis2iclx": "C", "STMicroelectronics@@iis2mdc": "CSS", "STMicroelectronics@@iis328dq": "C", "STMicroelectronics@@iis3dhhc": "C", "STMicroelectronics@@iis3dwb": "C", "STMicroelectronics@@ism303dac": "C", "STMicroelectronics@@ism330dhcx": "C", "STMicroelectronics@@ism330dlc": "C", "STMicroelectronics@@l20g20is": "CSS", "STMicroelectronics@@l3gd20h": "C", "STMicroelectronics@@lis25ba": "CSS", "STMicroelectronics@@lis2de12": "C", "STMicroelectronics@@lis2dh": "C", "STMicroelectronics@@lis2dh12": "C", "STMicroelectronics@@lis2ds12": "C", "STMicroelectronics@@lis2dtw12": "C", "STMicroelectronics@@lis2dw12": "C", "STMicroelectronics@@lis2hh12": "C", "STMicroelectronics@@lis2mdl": "CSS", "STMicroelectronics@@lis331dlh": "C", "STMicroelectronics@@lis3de": "C", "STMicroelectronics@@lis3dhh": "CSS", "STMicroelectronics@@lis3dh": "C", "STMicroelectronics@@lis3dsh": "CSS", "STMicroelectronics@@lis3mdl": "CSS", "STMicroelectronics@@lps22ch": "C", "STMicroelectronics@@lps22df": "CSS", "STMicroelectronics@@lps22hb": "C", "STMicroelectronics@@lps22hh": "C", "STMicroelectronics@@lps25hb": "C", "STMicroelectronics@@lps27hhtw": "C", "STMicroelectronics@@lps27hhw": "C", "STMicroelectronics@@lps28dfw": "CSS", "STMicroelectronics@@lps33hw": "C", "STMicroelectronics@@lps33k": "CSS", "STMicroelectronics@@lps33w": "C", "STMicroelectronics@@lsm303agr": "C", "STMicroelectronics@@lsm303ah": "C", "STMicroelectronics@@lsm6ds3tr-c": "C", "STMicroelectronics@@lsm6ds3": "C", "STMicroelectronics@@lsm6dsl": "C", "aros-translation-team@@info": "C", "qbs@@qbs": "C++", "malbou@@nanogui": "C++", "openexr@@openexr": "C", "couchbaselabs@@fleece": "C++", "couchbasedeps@@SQLiteCpp": "C", "couchbasedeps@@mbedtls": null, "couchbasedeps@@sockpp": "C++", "couchbasedeps@@zlib": "C", "couchbasedeps@@ios-cmake": null, "couchbasedeps@@sqlite3-unicodesn": null, "mitsuba-renderer@@libpng": "C", "mitsuba-renderer@@libjpeg": "C", "mitsuba-renderer@@tinyformat": "C++", "mitsuba-renderer@@pugixml": "C++", "mitsuba-renderer@@asmjit": "C++", "mitsuba-renderer@@enoki": "C++", "mitsuba-renderer@@mitsuba-data": "Python", "wjakob@@embree": "C++", "fvanroie@@freetype": "C", "CanalTP@@utils": "C++", "CanalTP@@libosmpbfreader": "C++", "canaltp@@SimpleAmqpClient": "C++", "CanalTP@@chaos-proto": "Rust", "CanalTP@@navitia-proto": null, "mariusmuja@@flann": "C++", "getnamo@@websocketpp": "C++", "getnamo@@rapidjson": "C++", "getnamo@@asio": "C++", "espressif@@json_parser": null, "espressif@@json_generator": null, "OpenImageDebugger@@eigen": "C++", "iconic@@open-iconic": "CSS", "Hanjun-Dai@@graphnn": "C++", "RISCSoftware@@cpacs_tigl_gen": "C++", "bigfug@@FugioTime": "C++", "The-Engine-Process@@brofiler": "C++", "horde3d@@Horde3D": "C++", "Isetta-Team@@yojimbo": "C", "Isetta-Team@@imgui": "C++", "flashrom@@flashrom": "C", "flingengine@@spdlog": "C++", "flingengine@@Catch2": "C++", "flingengine@@glm": "C++", "flingengine@@glfw": "C", "flingengine@@inih": "C++", "flingengine@@entt": null, "flingengine@@json": "C++", "flingengine@@stb": null, "flingengine@@tinyobjloader": null, "flingengine@@cereal": "C++", "flingengine@@imgui": "C++", "flingengine@@SPIRV-Cross": null, "flingengine@@imgui_entt_entity_editor": null, "Kistler-Group@@sdbus-cpp": "C++", "hydro-project@@common": "C++", "percona@@PerconaFT": "C++", "percona@@Percona-TokuBackup": "C++", "westerndigitalcorporation@@libzbd": "C", "steinbergmedia@@vstgui": "C++", "netwarm007@@ispc": "LLVM", "netwarm007@@zlib": "C", "netwarm007@@OpenGEX": "C++", "netwarm007@@crossguid": "C++", "netwarm007@@bullet3": "C++", "lammertb@@libcrc": "C", "netwarm007@@cmft": "C", "netwarm007@@SPIRV-Cross": "C++", "netwarm007@@cef": "C++", "netwarm007@@emsdk": "Python", "netwarm007@@SDL": "C", "netwarm007@@bison": "Shell", "neil3d@@UnrealAnimatedTexturePlugin": "C", "u1240976@@mess_note": "Python", "pi314@@pi314.notes": "Java", "intel@@SGXDataCenterAttestationPrimitives": "C++", "intel@@ipp-crypto": "C", "riscv@@opensbi": "C", "GPUOpen-LibrariesAndSDKs@@Cauldron": "C++", "KvrocksLabs@@lua": "C", "riscv@@riscv-clang": null, "Determinant@@salticidae": "C++", "Determinant@@minirun": "Shell", "HawkAaron@@mxnet-transducer": "C++", "imliubo@@A-Eye-Image2array-tool": "C", "etodd@@glew": "C", "etodd@@assimp": "C++", "etodd@@lodepng": "C++", "etodd@@bullet3": "C++", "etodd@@recastnavigation": "C++", "etodd@@SDL-mirror": "C", "etodd@@SDL_GameControllerDB": "C", "etodd@@cJSON": "C", "etodd@@mersenne-twister": "C++", "etodd@@FastLZ": "C", "etodd@@curl": "C", "etodd@@sqlite-amalgamation": "C", "etodd@@mongoose": "C", "etodd@@sha1": "C++", "etodd@@discord-rpc": "C++", "OpenHD@@mavlink": "CMake", "Consti10@@wifibroadcast": "C++", "JustasMasiulis@@xorstr": "C++", "open-ead@@sead": "C++", "open-ead@@nnheaders": "C++", "open-ead@@agl": "C++", "open-ead@@EventFlow": "C++", "open-ead@@nx-decomp-tools": "Rust", "open-ead@@botw-lib-musl": "C", "shlomif@@rinutils": "C", "libigl@@triangle": "C++", "rbrito@@lame": "C", "cornelius@@libkode": "C++", "bastibl@@Boost-for-Android": "Shell", "bastibl@@fftw-dist": "C", "bastibl@@volk": null, "bastibl@@gnuradio": "C++", "bastibl@@libgmp": null, "bastibl@@libusb": "C", "bastibl@@hackrf": null, "bastibl@@gr-osmosdr": null, "bastibl@@gr-grand": "C++", "bastibl@@uhd": "Verilog", "ghostop14@@gr-clenabled": "C++", "bastibl@@gr-sched": "C++", "bastibl@@rtl-sdr": null, "bastibl@@gr-ieee802-15-4": "C++", "harrism@@cpp11-range": "C++", "rsasaki0109@@ndt_omp_ros2": "C++", "ad-freiburg@@cppgtfs": "C++", "patrickbr@@pfxml": "C++", "yuki-koyama@@bigger": "C++", "alembic@@alembic": "C++", "yuki-koyama@@mathtoolbox": "C++", "d3cod3@@Mosaic_examples": "GLSL", "Daandelange@@ofxAddonTool": "Shell", "haskell@@containers": "<PERSON><PERSON>", "dsprenkels@@randombytes": "C", "system76@@ecsim": "Rust", "system76@@ecflash": "Rust", "system76@@ecspy": "Rust", "ArtifexSoftware@@mupdf": "C", "apple@@HomeKitADK": "C", "SaeruHikari@@marl": "C++", "BenzzzX@@Dots": "C++", "richzhang@@PerceptualSimilarity": "Python", "chenshuo@@muduo": "C++", "travisgoodspeed@@md380tools": "C", "sz3@@cimbar-samples": null, "chipsalliance@@UHDM": "C++", "alainmarcel@@antlr4": "Java", "pulp-platform@@axi_node": "SystemVerilog", "pulp-platform@@fpu_div_sqrt_mvp": "SystemVerilog", "bespoke-silicon-group@@ramulator": null, "bhesmans@@click": "C++", "pellegre@@libcrafter": "C++", "Flangvik@@SharpCollection": null, "lutzroeder@@Netron": "JavaScript", "microsoft@@winrt-rs": "Rust", "berndporr@@iir1": "C++", "boostorg@@vmd": "C++", "hyperlogic@@glyphblaster": "C", "hyperlogic@@abaci": "C++", "Kitware@@fletch": "C++", "Kitware@@kwiver": "C++", "VIAME@@scallop-tk": "C++", "Kitware@@vivia": "C++", "Kitware@@kwant": "C++", "Kitware@@burn-out": "C++", "VIAME@@darknet": "C", "Kitware@@SMQTK": "Terra", "pytorch@@pytorch": "C++", "pytorch@@vision": "Python", "viame@@mmdetection": "Python", "VIAME@@mmcv": "Python", "VIAME@@netharn": "Python", "VIAME@@pysot": "Python", "Kitware@@seal-tk": "C++", "InsightSoftwareConsortium@@ITKTrimmedPointSetRegistration": "C++", "Kitware@@keypointgui": "Python", "VIAME@@bioharn": "Python", "VIAME@@imgaug": null, "tensorflow@@models": "Python", "grimoire@@mmdetection-to-tensorrt": "Python", "NVIDIA-AI-IOT@@torch2trt": "Python", "cocos2d-html5@@cocos2d-x": "C", "cocos2d@@cocos2d-html5": "JavaScript", "svaarala@@duktape-releases": "Shell", "phimage@@CallbackURLKit": "Swift", "xmartlabs@@Eureka": "Swift", "ShadowsocksR-Live@@yaml": "HTML", "robbiehanson@@CocoaAsyncSocket": "Objective-C", "mutualmobile@@MMWormhole": "Objective-C", "robbiehanson@@KissXML": "Objective-C", "ShadowsocksR-Live@@libmaxminddb": "C", "ShadowsocksR-Live@@shadowsocksr-native": "C", "ThirteenAG@@IniReader": "C++", "ThomasMonkman@@filewatch": "C++", "CookiePLMonster@@ModUtils": "C++", "ThirteenAG@@pspsdk": "C++", "alecthomas@@entityx": "C++", "neuronsimulator@@iv": "C++", "BlueBrain@@CoreNeuron": "C++", "neuronsimulator@@rxdtestdata": null, "envoyproxy@@envoy": "C++", "linux-usb-gadgets@@libusbgx": "C", "HamletDuFromage@@zipper": "C", "HamletDuFromage@@borealis": "C++", "HamletDuFromage@@aiosu-rcm": "C", "lsds@@lkl": "C", "lsds@@musl": "C", "lsds@@sgx-lkl-musl": "C", "openenclave@@openenclave": "C", "lsds@@ltp": "C", "caiorss@@example-pybind11-vcpkg": "C++", "caiorss@@sample-cpp-plugin": "C++", "caiorss@@Rlang-RcppExamples": "C++", "caiorss@@example-project-pybind11": "C++", "bonzini@@qboot": "C", "09x09@@VBA-crash-course": null, "christopherpow@@nes-test-roms": "Assembly", "vlivashkin@@akvirtualcamera": null, "KVM-VMI@@kvm-vmi": "<PERSON><PERSON>", "0xf4b1@@libvmi": "C", "bitdefender@@libkvmi": "C", "0xf4b1@@syzkaller": "Go", "semlanik@@microjson": "C++", "Azure@@azure-sdk-for-c": "C", "arkenthera@@yume-freetype": "C", "arkenthera@@yume-boost": null, "arkenthera@@cef3d": "C++", "xtoolbox@@TeenyUSB_pc_tool": "<PERSON><PERSON>", "joyent@@illumos-libavl": "C", "Phil26AT@@HighFive": "C++", "iPower@@KasperskyHook": "C++", "google@@kafel": "C", "rdbo@@libmem": "C", "h5md@@h5xx": "C++", "shader-slang@@glslang": "C++", "shader-slang@@slang-binaries": "<PERSON><PERSON>", "FelixKratz@@libvim": "Vim script", "Jamesits@@Effy": "C", "pfalcon@@mbedtls": "C", "tcoppex@@ext-mikktspace": "C", "Illation@@vcpkg": "CMake", "richgel999@@bc7enc": "C++", "elasota@@ConvectionKernels": "C++", "MCMrARM@@Google-Play-API": "C++", "xdp-project@@libbpf": "C", "erikoest@@DNS-LDNS": "C", "NLnetLabs@@tpkg": "Shell", "pmodels@@izem": "C", "pmodels@@libfabric": "C", "pmodels@@hwloc": "C", "pmodels@@ucx": "C", "pmodels@@json-c": "C", "pmodels@@yaksa": "C", "wanduow@@libflowmanager": "C++", "LibtraceTeam@@libtrace": "C", "OpenRTX@@miosix-kernel": "C", "meshtastic@@Meshtastic-protobufs": null, "nrfconnect@@sdk-nrfxlib": "C", "meshtastic@@meshtastic-design": "Shell", "zaps166@@NFSIISE-ASM": "Assembly", "zaps166@@NFSIISE-CPP": "C++", "lab11@@nrf5x-base": "C", "lab11@@dw1000-driver": "C", "lab11@@stm32f0-base": "C", "ytsutano@@minizip": "C", "Lecrapouille@@minizip": "C", "Lecrapouille@@zlib": null, "realm@@realm-jsdoc": "JavaScript", "realm@@realm-core": "C++", "Valpineware@@ValpineBase": "C++", "picoruby@@picoruby": "<PERSON>", "WerWolv@@libtesla": "C", "edy555@@ChibiOS": "C", "chili-epfl@@chilitags-testdata": null, "WohlSoft@@PGE-File-Library-STL": "C++", "WohlSoft@@libFreeImage": "C++", "WohlSoft@@AudioCodecs": "C", "WohlSoft@@SDL-Mixer-X": "C", "WohlSoft@@Moondust-Devkit-Help": "JavaScript", "WohlSoft@@moondust-luabind": "C++", "facebookexperimental@@tvm": "Python", "noloader@@SHA-Intrinsics": "C", "jonmaiga@@mx3": "C++", "avaneev@@prvhash": "C", "gzm55@@hash-garage": "C", "avaneev@@komihash": "C", "xswang@@ps-lite": "C++", "tinyobjloader@@tinyobjloader": "C++", "nvpro-samples@@third_party_binaries": "C", "NVIDIA@@NVTX": "C", "BinomialLLC@@basis_universal": "C++", "MRtrix3@@test_data": "Python", "MRtrix3@@script_test_data": "Brainfuck", "skywind3000@@kcp": "C", "patriciogonzalezvivo@@mary": "C", "oceanbase@@oblogmsg": "C++", "homenc@@HElib": "C++", "stivale@@stivale": "C", "allanleal@@doxystrap": "JavaScript", "sciplot@@gnuplot-palettes": null, "awawa-dev@@libcec": "C++", "awawa-dev@@libjpeg-turbo": "C", "winlibs@@wineditline": "C", "ClickHouse-Extras@@cld2": null, "ClickHouse-Extras@@nlp-data": null, "ClickHouse-Extras@@hive-metastore": "Thrift", "zlib-ng@@minizip-ng": "C", "tesseract-ocr@@tesseract": "C++", "ashtons@@libtiff-ios": "<PERSON><PERSON><PERSON>", "intel@@gpgmm": "C++", "tomikaa87@@BlynkLibrary": "C#", "feelfreelinux@@bell": "C", "Microsoft@@IIS.Common": "C++", "a-n-t-h-o-n-y@@signals-light": "C++", "a-n-t-h-o-n-y@@Escape": "C++", "openmv@@micropython": "C", "v923z@@micropython-ulab": "C", "sccn@@liblsl": "C++", "mne-tools@@mne-cpp-test-data": null, "brainflow-dev@@brainflow": "C++", "Tessil@@sparse-map": "C++", "ktprime@@ktprime": "C++", "Dalzhim@@ArticleEnumClass-v2": "C++", "libb64@@libb64": "C", "bkloppenborg@@pathfind": "C++", "rsmmr@@justrx": "C", "simonfxr@@fiber": "C", "thelink2012@@any": "C++", "tekdemo@@MiniPID": "C++", "*********@@GTAVMenuBase": "C++", "*********@@GTAVDashHook": "C++", "afpd@@netaggregate": "C++", "dywisor@@ip-dedup": "C", "walaj@@SeqLib": "C++", "adafruit@@DHT-sensor-library": "C++", "munt@@munt": "C++", "FluidSynth@@fluidsynth": "C", "cpeditor@@QtFindReplaceDialog": "C++", "cpeditor@@lsp-cpp": "C++", "MikeMirzayanov@@testlib": "C++", "cpeditor@@qhttp": "C++", "tenacityteam@@vcpkg": "CMake", "DanBloomberg@@leptonica": "C", "DiligentGraphics@@nuklear": "C", "DiligentGraphics@@glfw": "C", "DLu@@navigation_layers": "C++", "BehaviorTree@@Groot": "C++", "tysik@@obstacle_detector": "C++", "Unity-Technologies@@ROS-TCP-Endpoint": "Python", "BehaviorTree@@BehaviorTree.CPP": "C++", "locusrobotics@@json_transport": "C++", "weserv@@docs": "<PERSON><PERSON>", "gongluck@@3rdparty": "C", "gongluck@@media": null, "gongluck@@tools": "Batchfile", "Azure@@azure-umqtt-c": "C", "Azure@@azure-uamqp-c": "C", "Azure@@azure-uhttp-c": "C", "Microsoft@@RIoT": "C", "Azure@@azure-utpm-c": "C", "Azure@@azure-macro-utils-c": "C", "Azure@@umock-c": "C", "Azure@@azure-c-testrunnerswitcher": "CMake", "Azure@@azure-ctest": "C", "scicompkl@@CoDiPack": "C++", "SciCompKL@@MeDiPack": "C++", "ninja-build@@ninja": "C++", "mesonbuild@@meson": "Python", "mutationpp@@Mutationpp": "C++", "SciCompKL@@OpDiLib": "C++", "pcarruscag@@MEL": "C++", "AcademySoftwareFoundation@@Imath": "C++", "Zuzu-Typ@@glm": "C++", "foxtacles@@iniparser": "C", "foxtacles@@y2038": "C", "foxtacles@@serialization": "C", "LLNL@@radiuss-ci": "CMake", "IUdalov@@u-test": "<PERSON><PERSON>", "jrayzero@@Halide": "C++", "k0dai@@cputime": "C", "k0dai@@spookyhash": "C", "norihiro@@libvisca-ip": "C", "norihiro@@dlib": null, "elonafoobar@@windows_deps": "C", "VCVRack@@Rack": "C++", "CardinalModules@@AudibleInstruments": "C++", "mhampton@@ZetaCarinaeModules": "C++", "dbgrande@@GrandeModular": "C++", "bogaudio@@BogaudioModules": "C++", "CardinalModules@@Bidoo": "C++", "VCVRack@@ESeries": "C++", "jeremywen@@JW-Modules": "C++", "CardinalModules@@rackwindows": "C++", "MarcBoule@@ImpromptuModular": "C++", "jhoar@@AmalgamatedHarmonics": "C++", "CardinalModules@@cf": "C++", "MarcBoule@@MindMeldModular": "C++", "ValleyAudio@@ValleyRackFree": "C++", "SVModular@@DrumKit": "C++", "falkTX@@Carla": "C++", "CardinalModules@@mscHack": "C++", "zezic@@ZZC": "C++", "wiqid@@repelzen": "C++", "CardinalModules@@AriaModules": "C++", "baconpaul@@BaconPlugs": "Jupyter Notebook", "RCameron93@@FehlerFabrik": "C++", "CardinalModules@@Mog-VCV": "C++", "jatinchowdhury18@@ChowDSP-VCV": "C++", "gluethegiant@@gtg-rack": "C++", "mhetrick@@hetrickcv": "C++", "VegaDeftwing@@LyraeModules": "C++", "mgunyho@@Little-Utils": "C++", "SteveRussell33@@Prism": "C++", "EaterOfSheep@@Extratone": "C++", "SteveRussell33@@LifeFormModular": null, "netboy3@@21kHz-rack-plugins": "C++", "DISTRHO@@PawPaw": "Shell", "LomasModules@@LomasModules": "C", "expertsleepersltd@@vcvrack-encoders": "C++", "CardinalModules@@ihtsyn": "C++", "JerrySievert@@QuickJS": "C", "NikolaiVChr@@Autinn": "C++", "dfranx@@BlueVM": "C", "dfranx@@aGen": "C++", "dfranx@@glsl-parser": "C++", "dfranx@@hlslparser": "C++", "electro-smith@@libDaisy": "C", "IsoaSFlus@@kde-theme-backup": null, "espressif@@esp-dsp": "C", "xiph@@flac": "C", "JanuszL@@ffts": "C", "mzient@@libcudacxx": null, "agency-library@@agency": "C++", "MoeMod@@mainui_cpp": "C++", "MoeMod@@nanogl": "C++", "MoeMod@@gl-wes-v2": "C", "MoeMod@@imgui": "C++", "MoeMod@@QindieGL": "C++", "ricmoo@@QRCode": "C++", "MoeMod@@luajit-cmake": "CMake", "petiaccja@@Inline-BaseLibrary": "C++", "NVIDIAGameWorks@@PhysX": "C++", "ParAlg@@parlaylib": "C++", "ldhulipala@@PAM": "C++", "Wohlstand@@libADLMIDI": "C++", "jpcima@@JUCE": "C++", "Wohlstand@@libOPNMIDI": "C", "jpcima@@simpleini": "C++", "0xcafed00d@@z8lua": "C", "0xcafed00d@@utf8-util": "C", "libnice@@libnice": "C", "ireader@@media-server": "C", "jeremycw@@httpserver.h": "C", "woodrush@@elvm": "C", "woodrush@@QFT-devkit": "Python", "halide@@Halide": "C++", "MegEngine@@MegRay": "C++", "MegEngine@@midout": "C++", "MegEngine@@cutlass": "C++", "MegEngine@@cpuinfo": null, "MegEngine@@gflags": "C++", "cpp-redis@@cpp_redis": "C++", "cpp-redis@@tacopie": "C++", "csantosbh@@eigen": "C++", "csantosbh@@pysigset": "Python", "FEX-Emu@@vixl": "C", "Sonicadvance1@@cpp-optparse": "C++", "Sonicadvance1@@imgui": "C++", "Sonicadvance1@@json-maker": "C", "Sonicadvance1@@tiny-json": "C", "FEX-Emu@@xbyak": "C++", "FEX-Emu@@fex-posixtest-bins": "C", "FEX-Emu@@fex-gvisor-tests-bins": null, "FEX-Emu@@fex-gcc-target-tests-bins": "Shell", "FEX-Emu@@jemalloc": null, "FEX-Emu@@drm-headers": "C", "FEX-Emu@@xxHash": null, "PlatONnetwork@@boost": "C++", "PlatONnetwork@@rapidjson": "C++", "ljfa-ag@@libnbtplusplus": "C++", "tsattler@@RansacLib": "C++", "vlarsson@@PoseLib": "C++", "wjakob@@nanovg_metal": "C", "borisbat@@dasGlfw": "C", "borisbat@@dasImgui": "C++", "borisbat@@dasBGFX": "C", "borisbat@@dasXbyak": "C++", "imp5imp5@@dasSound": "C++", "imp5imp5@@dasMinfft": "C", "borisbat@@dasHV": "C", "borisbat@@dasSFML": "C++", "pezmaster31@@bamtools": "C++", "egorodet@@MethaneExternals": "C++", "joncampbell123@@minx86dec": "C", "switch-iot@@n2n_meyerd": "C", "switch-iot@@n2n_ntop": "C", "ntop@@n2n": "C", "kbranigan@@Simple-OpenGL-Image-Library": "C", "kainjow@@Mustache": "C++", "andrejnau@@dxc": "C++", "andrejnau@@d3dx12": "C", "microsoft@@DirectX-Headers": "C", "PX4@@OpticalFlow": "C++", "unicorn-engine@@unicorn": "C", "keystone-engine@@keystone": "C++", "Firemoon777@@tg": "C", "xguerin@@tclap": "C++", "continental@@fineftp-server": "C++", "ikalnytskyi@@termcolor": "C++", "steinwurf@@recycle": "C++", "continental@@tcp_pubsub": "C++", "3Dickulus@@Fragmentarium_Examples_Folder": "GLSL", "moonlight-stream@@GS-IPv6-Forwarder": "C++", "LLNL@@camp": "C++", "SChernykh@@RandomX": "C++", "SChernykh@@rapidjson": "C++", "SChernykh@@cppzmq": "C++", "SChernykh@@libsodium": "C", "SChernykh@@libuv": "C", "SChernykh@@libzmq": "C++", "SChernykh@@robin-hood-hashing": "C++", "gcc-mirror@@gcc": "C", "stanfordsnr@@libgg": "C", "zinnschlag@@mangle": "C++", "korslund@@mangle": "C++", "ethz-asl@@maplab_test_data": "CMake", "ethz-asl@@linter": "Python", "citizenfx@@Win2D": "C++", "mpoeter@@xenium": "C++", "tatsuhiro-t@@openssl": "C", "tencent-wechat@@libco": "C++", "tencent-wechat@@phxrpc": "C++", "tencent-wechat@@phxpaxos": "C++", "lemire@@SIMDCompressionAndIntersection": "C++", "cmuparlay@@parlaylib": "C++", "taisei-project@@cglm": "C", "openvinotoolkit@@oneDNN": "C++", "opencv@@ade": "C++", "openvinotoolkit@@googletest": null, "KhronosGroup@@OpenCL-ICD-Loader": "C", "intel@@ittapi": "C", "Aleph-One-Marathon@@data-marathon": "<PERSON><PERSON>", "Aleph-One-Marathon@@data-marathon-2": "<PERSON><PERSON>", "rems-project@@sail-riscv": "Coq", "maximkulkin@@esp-cjson": "<PERSON><PERSON><PERSON>", "maximkulkin@@esp-wolfssl": "C", "maximkulkin@@esp-homekit": "C", "maximkulkin@@esp-http-parser": "<PERSON><PERSON><PERSON>", "Wren6991@@riscv-formal": "Verilog", "Wren6991@@libfpga": "Verilog", "Wren6991@@fpgascripts": "Python", "steinbergmedia@@vst3_pluginterfaces": "C++", "steinbergmedia@@vst3_public_sdk": "C++", "sfztools@@vstgui": "C++", "mackron@@dr_libs": "C", "sfztools@@stb_vorbis": "C", "sfztools@@libaiff": "C", "sfztools@@sfzt_auwrapper": "Objective-C++", "sfztools@@vst3_public_sdk": "C++", "sfztools@@vst3_pluginterfaces": "C++", "xicilion@@fibjs_docs": "Less", "intel@@mkl-dnn": "C++", "dmlc@@tvm": "Python", "BesLyric-for-X@@BesLyric-for-X_Linux_deploy-package": "Shell", "BesLyric-for-X@@BesLyric-for-X_macOS_deploy-package": "Shell", "erincatto@@box2d": "C++", "kushview@@kv-modules": "C++", "kushview@@JUCE": "C++", "lvtk@@jlv2": "Python", "DISTRHO@@JUCE": "C++", "WeAreRoli@@JUCE": "C++", "kushview@@element": "C++", "mfisher31@@libjuce": "Python", "FreeRTOS@@FreeRTOS-Kernel-Partner-Supported-Ports": "C", "simulatedsimian@@z8lua": "C", "mirror@@tclap": "C++", "bab2min@@cpp-btree": null, "nedrysoft@@componentsystem": "C++", "nedrysoft@@qt-ribbon": "C++", "nedrysoft@@SettingsDialog": "C++", "nedrysoft@@FontAwesomeForQt": "SCSS", "maxbachmann@@rapidfuzz-cpp": "C++", "nedrysoft@@ThemeSupport": "C++", "fizzyade@@componentsystem": "C++", "fizzyade@@qt-ribbon": "C++", "fizzyade@@SettingsDialog": "C++", "andreyvit@@create-dmg": "Shell", "vkaravir@@js-parsons": "JavaScript", "bnmnetp@@skulpt": "Python", "JustasMasiulis@@lazy_importer": "C++", "swaywm@@wlroots": "C", "ammen99@@wf-config": "C++", "libretro@@snes9x-next": "C", "nadavbh12@@nanoarch": "C", "whitfin@@siphash-cpp": "C++", "Open-Transactions@@trezor-firmware": "C", "libbitcoin@@secp256k1": "C", "Open-Transactions@@opentxs-proto": "C++", "libressl-portable@@portable": "C", "bitcoin@@secp256k1": "C", "Open-Transactions@@gtest": "C++", "zeromq@@zeromq4-1": "C++", "Open-Transactions@@bitcoin-core-base58": "C++", "Open-Transactions@@bitcoin-crypto": "C++", "Open-Transactions@@secp256k1": "C", "dorneanu@@ixkeylog": "C", "dorneanu@@netgrafio": "JavaScript", "dorneanu@@smalisca": "Python", "noptrix@@lulzbuster": "C", "noptrix@@nullscan": "Python", "noptrix@@httpgrep": "Python", "noptrix@@sshprank": "Python", "noptrix@@ipcountry": "Python", "noptrix@@dnsspider": "Python", "rmartinho@@wheels": "C++", "seemethere@@pybind11": null, "zdevito@@sleef": "C", "Maratyszcza@@cpuinfo": "C", "ARM-software@@ComputeLibrary": "C++", "bddppq@@onnx-tensorrt": "C++", "nvidia@@nccl": "C++", "nvidia@@cnmem": "C++", "taka-no-me@@android-cmake": "CMake", "system76@@edk2": "C", "system76@@coreboot": "C", "system76@@edk2-platforms": "C", "ptresearch@@unME12": "Python", "ptresearch@@unME11": "Python", "LongSoft@@UEFITool": "C++", "system76@@intel-spi": "Rust", "system76@@coreboot-collector": "Rust", "system76@@firmware-update": "Rust", "system76@@firmware-setup": "Rust", "platomav@@MEAnalyzer": "Python", "system76@@gop-policy": "Rust", "tianocore@@edk2-non-osi": "Assembly", "IntelFsp@@FSP": "C", "system76@@firmware-smmstore": "Rust", "ipxe@@ipxe": "C", "system76@@ec": "C", "system76@@smmstore": "Rust", "yuki-koyama@@optimization-test-functions": "C++", "stevengj@@nlopt": "C", "yuki-koyama@@nlopt-util": "C++", "Unarelith@@GameKit": "C++", "Unarelith@@zlib": "C", "Unarelith@@LuaJIT": "C", "OpenOrbis@@oni-framework": "C", "Xaymar@@obs-studio_amf-encoder-plugin": "C++", "jp9000@@libdshowcapture": "C++", "kc5nra@@obs-browser": "C++", "DDRBoxman@@obs-vst": "C++", "byu-magicc@@BreezySTM32": "C", "simondlevy@@BreezySTM32": "C", "mheily@@libpwq": "C", "g-oikonomou@@cc26xxware": "C", "g-oikonomou@@cc13xxware": "C", "axoloti@@axoloti-factory": "C++", "uriparser@@uriparser": "C", "google@@draco": "C++", "mapbox@@earcut.hpp": "C", "Amanieu@@asyncplusplus": "C++", "Duthomhas@@CSPRNG": "C++", "okdshin@@PicoSHA2": "C++", "bilke@@cmake-modules": "CMake", "KhronosGroup@@glTF": "HTML", "midjji@@lambdatwist-p3p": "C++", "LLNL@@blt": "C++", "llnl@@camp": "C++", "ROCmSoftwarePlatform@@rocPRIM": "C++", "LLNL@@radiuss-spack-configs": null, "LLNL@@uberenv": "Shell", "Microsoft@@DirectXMath": "C++", "troglobit@@libuev": "C", "vgteam@@tabixpp": "C++", "BurntSushi@@toml-test": "Go", "iarna@@toml-spec-tests": null, "marzer@@dox": "Python", "mosra@@m.css": "Python", "tidyjiang8@@esp32-onenet": "C", "CopenhagenGameCollective@@UniMove": "C#", "ms-iot@@UniversalMediaEngine": "C++", "inclavare-containers@@inclavare-containers.io": "SCSS", "inclavare-containers@@inclavared": "Rust", "inclavare-containers@@shelter": "C", "opengisch@@QGIS-Sampledata": "Game Maker Language", "folecr@@cocos2dx-autogen-bindings": "C++", "cocos2d@@cocos2d-js-tests": "JavaScript", "kripken@@emscripten": "C", "cocos2d-x@@plugin-x": "Objective-C", "tobozo@@ESP32-BLEBeaconSpam": "C++", "Jaysmito101@@glfw": "C", "Jaysmito101@@imgui": "C++", "Jaysmito101@@zip": "C", "atomic14@@PNGdec": "C", "martinberlin@@FT6X36-IDF": "C++", "Alia5@@SFML": "C++", "GiovanniDicanio@@WinReg": "C++", "TinyTinni@@ValveFileVDF": "C++", "ViGEm@@ViGEmClient": "C", "andoma@@xmp": "C", "Vogtinator@@nspire-io": "C", "vrpn@@hidapi": "C", "rpavlik@@hidapi": "C", "openxc@@bitfield-c": "C", "apiaryio@@cmdline": "C++", "leetal@@ios-cmake": "CMake", "nasa@@osal": "C", "nasa@@CF": "C", "nasa@@CS": "C", "nasa@@DS": "C", "nasa@@FM": "C", "nasa@@HK": "C", "nasa@@HS": "C", "nasa@@LC": "C", "nasa@@MD": "C", "nasa@@MM": "C", "nasa@@SBN": "C", "nasa@@SC": "C", "nasa@@SCH": "C", "nasa@@cfs_lib": "C", "nasa@@CFS_TO": "C", "nasa@@CFS_CI": "C", "dmlc@@nnvm-fusion": "C++", "qtproject@@qt-solutions": "C++", "Yubico@@yubikey-manager": "Python", "gvanas@@KeccakCodePackage": "C", "MizukiSonoko@@ed25519": "C", "MizukiSonoko@@Cappuccino": "C++", "SDL-mirror@@SDL_ttf": "C", "mborgerson@@extract-xiso": "C", "jaagr@@i3ipcpp": "C++", "fennecdjay@@Gwion-plug": "C", "fennecdjay@@gwion-util": "C", "fennecdjay@@gwion-jit": "C", "fennecdjay@@gwion-tools": "C", "fennecdjay@@gwion-mod": "C", "openenergymonitor@@emonlib": "C++", "jcw@@jeelib": "C++", "PaulStoffregen@@OneWire": "C++", "marcoschwartz@@LiquidCrystal_I2C": "C++", "kbengine@@kbengine_demos_assets": "Python", "hrydgard@@ppsspp-lang": null, "hrydgard@@native": "C", "hrydgard@@ppsspp-redist": null, "aff4@@ReferenceImages": null, "michalsc@@tiny-std": "C++", "syoyo@@tinyobjloader-c": "C", "NVIDIA@@cutlass": "C++", "cocodataset@@cocoapi": "Jupyter Notebook", "mzient@@rmm": null, "pypa@@manylinux": "Shell", "carla-simulator@@map": "C++", "DrBeef@@hlsdk-xash3d": "C++", "astrorigin@@swephelp": "C", "p4lang@@PI": "C++", "mfragkoulis@@diffutils": "C", "mfragkoulis@@parallel": "<PERSON><PERSON>", "ethereum@@evmjit": "C++", "WebKit@@webkit": null, "sticilface@@ESPdeviceFinder": "C++", "henrypp@@routine": "C", "aiekick@@ImGuiFileDialog": "C++", "cboulay@@hidapi": "C", "XKCP@@XKCP": "C", "doxygen@@doxygen": "C++", "logmich@@logmich": "C++", "tinygettext@@tinygettext": "C++", "Grumbel@@uitest": "C++", "lispparser@@sexp-cpp": "C++", "AppImage@@zsync2": "C", "TheAssassin@@fltk-1.3.4": "C", "TheAssassin@@libdesktopenvironments": "C++", "TheAssassin@@zsync2": "C", "rakhimov@@translators": "Python", "rakhimov@@qtango": "Python", "rakhimov@@googletest": "C++", "ziocleto@@websocketpp": "C++", "pajlada@@humanize": "C++", "hemirt@@libcommuni": "C++", "mbartling@@CMSIS_5": "C", "vmt@@udis86": "C", "aap@@librw": "C++", "maidsafe@@MaidSafe-RUDP": "C++", "maidsafe@@MaidSafe-Private": "C++", "definelicht@@hlslib": "C++", "q-gears@@luajit": "C", "paulsapps@@gmock-1.7.0": "C++", "myint@@perceptualdiff": "C++", "BlueBrain@@CMake": null, "BlueBrain@@pbrt-v2": "C++", "BlueBrain@@Brion": "C++", "BlueBrain@@pbrt-v3": "C++", "canokeys@@canokey-crypto": "C", "neosmart@@CppSQLite": "C++", "TortoiseGit@@apr": "C", "Pro@@mdnsd": "C", "ErwinJanssen@@gtk2-win32-development-libraries": "HTML", "ErwinJanssen@@expat-xml-parser-win32": "C", "ErwinJanssen@@libtool-win32": "Shell", "ErwinJanssen@@freeglut-win32": "C", "ErwinJanssen@@ANN-win32": "C++", "ErwinJanssen@@rxspencer-win32": "C", "CBATeam@@CBA_A3": "SQF", "ClickHouse-Extras@@ssl": "C", "nanodbc@@nanodbc": "C++", "rizinorg@@sdb": "C", "icculus@@mojoshader": "C", "FNA-XNA@@MojoShader": "C", "innative-sdk@@spec": "WebAssembly", "oscarlab@@graphene-sgx-driver": "C", "stream-labs@@dlib": "C++", "stream-labs@@freetype2": "C", "kamyarinfinity@@assimp": "C++", "lbcb-sci@@racon": "C++", "lbcb-sci@@ram": "C++", "rvaser@@logger": "C++", "johnezang@@pithy": "C", "richgel999@@lzham_codec": "C++", "svn2github@@lz4": "C", "nemequ@@lzmat": "C", "FrancescAlted@@blosc": "C", "svn2github@@wflz": "C", "mitchcurtis@@qt-undo": "C++", "alicevision@@geogram": "C++", "jdumas@@json": "C++", "PatWie@@CppNumericalSolvers": "C++", "yixuan@@spectra": "C++", "labatrockwell@@ofxOpenNI": "C", "kylemcdonald@@ofxCv": "C++", "labatrockwell@@ofxLibwebsockets": "C", "labatrockwell@@ofxTSPSReceiver": "C++", "Spacebrew@@ofxSpacebrew": "C++", "ofTheo@@ofxKinect": "C", "gameoverhack@@ofxOpenNI": "C", "bytedance@@terark-zip": "C++", "ned14@@outcome": "C++", "cycfi@@infra": "C++", "yugabyte@@yugabyte-bash-common": "Shell", "yugabyte@@yugabyte-installation": "Python", "pai-disc@@tensorflow": "C++", "ifratric@@winipt": "C", "dcrankshaw@@redox": "C++", "dcrankshaw@@rapidjson": "C++", "google@@yapf": "Python", "nico@@demumble": "Python", "smuehlst@@circle-stdlib": "C", "OtherCrashOverride@@esp-idf": "C", "eclipse@@paho.mqtt.c": "C", "DroneCAN@@libuavcan": "C++", "ArduPilot@@waf": "Python", "ArduPilot@@mavlink": "CMake", "ArduPilot@@googletest": "C++", "ArduPilot@@ChibiOS": "C", "ArduPilot@@gsoap": "HTML", "DroneCAN@@DSDL": "Python", "ardupilot@@CrashDebug": "C++", "DroneCAN@@pydronecan": "Python", "DroneCAN@@dronecan_dsdlc": "Python", "ArduPilot@@uavcan": "C++", "ArduPilot@@PX4Firmware": "C++", "ArduPilot@@PX4NuttX": "C", "diydrones@@PX4Firmware": "C++", "diydrones@@PX4NuttX": "C", "diydrones@@uavcan": "C++", "diydrones@@waf": "Python", "LaurentGomila@@SFML": "C++", "SuperV1234@@SSVLuaWrapper": "C++", "SuperV1234@@SSVEntitySystem": "C++", "SuperV1234@@SSVCMake": "CMake", "SuperV1234@@SSVUtilsJson": "C++", "c3d@@XL-programming-language": "C++", "c3d@@tao-help-viewer": "C++", "c3d@@tao-webui": "JavaScript", "c3d@@tao-charts": "C++", "c3d@@tao-chroma-key": "Shell", "c3d@@tao-database": "C++", "c3d@@tao-digital-clock": null, "c3d@@tao-film-strip": null, "c3d@@tao-filters": "C++", "c3d@@tao-lorem-ipsum": "C++", "c3d@@tao-mapping": "C++", "c3d@@tao-materials": "C++", "c3d@@tao-movie-credits": null, "c3d@@tao-network-access": "C++", "c3d@@tao-news-feed": null, "c3d@@tao-nodejs": "C++", "c3d@@tao-object-loader": "C++", "c3d@@tao-pan-and-zoom": null, "c3d@@tao-point-clouds": "C++", "c3d@@tao-prez-at": "JavaScript", "c3d@@tao-quiz": null, "c3d@@tao-remote-control": "C++", "c3d@@tao-revolving-texts": null, "c3d@@tao-shaders": "C", "c3d@@tao-shading": "C++", "c3d@@tao-slideshow-3d": null, "c3d@@tao-snowfall": null, "c3d@@tao-speech": "C++", "c3d@@tao-stereo-decoder": null, "c3d@@tao-synchro": "C++", "c3d@@tao-tester": "C++", "c3d@@tao-visuals": null, "c3d@@tao-transitions": "GLSL", "c3d@@tao-vlc-audio-video": "C++", "c3d@@tao-water-surface": "C++", "c3d@@tao-weather": "JavaScript", "c3d@@tao-web-remote": "HTML", "ARM-software@@astc-encoder": "C++", "ValveSoftware@@Fossilize": "C++", "Themaister@@muFFT": "C", "Themaister@@astc-encoder": "C++", "Themaister@@meshoptimizer": "C++", "Themaister@@Fossilize": "C", "itay-grudev@@SingleApplication": "C++", "Skycoder42@@QTaskbarControl": "C++", "crow-translate@@QOnlineTranslator": "C++", "crow-translate@@QGitTag": "C++", "Shatur95@@QOnlineTranslator": "C++", "boost-experimental@@sml": "C++", "Warchant@@sr25519-crust": "Rust", "msoos@@cnf-utils": "C++", "msoos@@sha1-sat": "C++", "danvk@@dygraphs": "JavaScript", "msoos@@licensecheck": "<PERSON><PERSON>", "msoos@@cryptominisat_build": "Shell", "msoos@@drat-trim": "C++", "msoos@@minisat": "C++", "msoos@@simplifiy_testfiles": null, "msoos@@xor_testfiles": "Shell", "msoos@@pbsugar": "Java", "TheQwertiest@@foobar2000-sdk": "C++", "TheQwertiest@@pfc": "C++", "3dyd@@acfu-sdk": "C++", "martinmoene@@span-lite": "C++", "jmorton06@@imgui": null, "CedricGuillemet@@ImGuizmo": "C++", "KCL-Planning@@VAL": "C++", "gerardcanal@@rddl_parser": "C++", "ssanner@@rddlsim": "Java", "gerardcanal@@PPDDL_determinization": "C++", "oscar-lima@@hddl_parser": "Yacc", "ArvernOS@@printf": null, "mpaland@@printf": "C", "greenplum-db@@gp-xerces": "C++", "greenplum-db@@gpos": "C++", "greenplum-db@@gporca": "C++", "jconway@@plr": null, "rapidsai@@thirdparty-cub": "<PERSON><PERSON>", "picotorrent@@crashpad": null, "picotorrent@@sentry-crashpad": null, "antlr@@antlr4": "Java", "boostorg@@array": "C++", "boostorg@@asio": "C++", "boostorg@@container": "C++", "boostorg@@container_hash": "C++", "boostorg@@crc": "C++", "boostorg@@date_time": "C++", "boostorg@@exception": "C++", "boostorg@@function": "C++", "boostorg@@fusion": "C++", "boostorg@@integer": "C++", "boostorg@@intrusive": "C++", "boostorg@@lexical_cast": "C++", "boostorg@@math": "C++", "boostorg@@move": "C++", "boostorg@@multiprecision": "C++", "boostorg@@numeric_conversion": "C++", "boostorg@@optional": "C++", "boostorg@@pool": "C++", "boostorg@@range": "C++", "boostorg@@rational": "C++", "boostorg@@scope_exit": "C++", "boostorg@@smart_ptr": "C++", "boostorg@@system": "C++", "boostorg@@tuple": "C++", "boostorg@@typeof": "C++", "boostorg@@variant": "C++", "boostorg@@winapi": "C++", "ginge@@btstack": "C", "cppformat@@cppformat": "C++", "DECENTfoundation@@fc": "C++", "DECENTfoundation@@cpp-ipfs-api": "C++", "DECENTfoundation@@homebrew-DCore": "<PERSON>", "DECENTfoundation@@json": "C++", "nitrocaster@@GameSpy": "C", "cginternals@@glbinding": "C++", "gyeongin@@pretrained-models.pytorch": "Python", "gyeongin@@pytorch-image-models": "Python", "Meulengracht@@acpica": "ASL", "Meulengracht@@mesa": "C", "Meulengracht@@glm": "C++", "dnbaker@@vec": "C++", "kimwalisch@@libpopcnt": "C", "dnbaker@@aesctr": "C++", "dnbaker@@circularqueue": "C++", "SuperHouse@@esp-lwip": "C", "thvdburgt@@KnR-The-C-Programming-Language-Solutions": "C", "garaemon@@SLIC-Superpixels": "C++", "jsk-ros-pkg@@jsk_travis": "Python", "dronecore@@DroneCore-Proto": "Python", "dronecore@@curl-android-ios": "C", "dronecore@@json11": "C++", "gcesarmza@@curl-android-ios": "C", "napocahv@@acpica": null, "napocahv@@VisualUefi": null, "solokeys@@dfuse-tool": "Python", "SoloKeysSec@@python-fido2": "Python", "conorpp@@python-fido2": "Python", "rampantpixels@@rpmalloc": "C", "lotem@@librime": "C++", "pothosware@@SoapyOsmo": "C++", "mitsuba-renderer@@zeromq": "C++", "intel@@IntelSEAPI": "Python", "Zubax@@zubax_chibios": "C++", "Zubax@@document_templates": "TeX", "Zubax@@drwatson": "Python", "Zubax@@ChibiOS": "C", "Qihoo360@@glog": "C++", "Qihoo360@@slash": "C++", "Qihoo360@@blackwidow": "C++", "Axlgrep@@blackwidow": "C++", "Qihoo360@@nemo": "C++", "PikaLabs@@pink": "C++", "Qihoo360@@nemo-rocksdb": "C++", "baotiao@@nemo": "C++", "CatKang@@glog": "C++", "baotiao@@pink": "C++", "desktop-app@@gyp_helpers": "Python", "telegramdesktop@@crl": "C++", "telegramdesktop@@qtlottie": "C++", "nkolban@@ESP32_BLE_Arduino": "C++", "radosroka@@usbmon": "C++", "sdottaka@@winimerge": "C++", "sdottaka@@freeimage": "C", "sld-columbia@@linux": "C", "sld-columbia@@esp-chisel-accelerators": "Scala", "sld-columbia@@esp-accelerator-templates": "C++", "sld-columbia@@ariane": "SystemVerilog", "sld-columbia@@riscv-pk": "C", "riscv@@riscv-tests": "C", "sld-columbia@@nvdla-hw": "Verilog", "sld-columbia@@nvdla-sw": "C++", "sld-columbia@@esp-caches": "SystemVerilog", "lowRISC@@ibex": "SystemVerilog", "sld-columbia@@zynq-template": "<PERSON><PERSON><PERSON>", "bespoke-silicon-group@@basejump_stl": "Verilog", "IBM@@esp-chisel-accelerators": "Scala", "pulp-platform@@ariane": "C++", "ethz-asl@@libnabo": "C++", "ethz-asl@@libpointmatcher": "C++", "google-coral@@edgetpu": "C++", "NPLPackages@@main": "<PERSON><PERSON>", "madeso@@embed": "CMake", "microsoft@@vscode-codicons": "Handlebars", "madeso@@build": "Python", "eerimoq@@punchboot": "C", "eerimoq@@detools": "Python", "eerimoq@@linux": null, "eerimoq@@monolinux": "<PERSON><PERSON><PERSON>", "eerimoq@@monolinux-c-library": "C", "jonasblixt@@bpak": "C", "eerimoq@@dbg-macro": "C", "eerimoq@@pbtools": "C", "eerimoq@@messi": "C", "polycube-network@@bcc": "Python", "cuberite@@jsoncpp": "C++", "cuberite@@zlib": "C", "cuberite@@transapi": "<PERSON><PERSON>", "cuberite@@ChunkWorx": "<PERSON><PERSON>", "cuberite@@Handy": "<PERSON><PERSON>", "cuberite@@MagicCarpet": "<PERSON><PERSON>", "mc-server@@Core": "<PERSON><PERSON>", "mc-server@@ProtectionAreas": "<PERSON><PERSON>", "bearbin@@transapi": "<PERSON><PERSON>", "mc-server@@ChunkWorx": "<PERSON><PERSON>", "mc-server@@ChatLog": "<PERSON><PERSON>", "mc-server@@Handy": "<PERSON><PERSON>", "mc-server@@MagicCarpet": "<PERSON><PERSON>", "mc-server@@polarssl": "C", "mc-server@@SQLiteCpp": "C", "mc-server@@libevent": "C", "mc-server@@TCLAP": "C++", "daos-stack@@scons_local": "Python", "mmp@@double-conversion": "C++", "mmp@@openexr": "C", "wjakob@@filesystem": "C++", "AcademySoftwareFoundation@@openvdb": "C++", "mmp@@libdeflate": null, "DiligentGraphics@@glslang": "C++", "DiligentGraphics@@SPIRV-Cross": "GLSL", "DiligentGraphics@@SPIRV-Tools": "C++", "DiligentGraphics@@SPIRV-Headers": "C++", "DiligentGraphics@@googletest": "C++", "DiligentGraphics@@Vulkan-Headers": "C++", "DiligentGraphics@@volk": "C", "HdrHistogram@@HdrHistogram_c": "C", "google@@styleguide": "HTML", "lemire@@fastrange": "C", "pothosware@@pothos-plotters": "C++", "pothosware@@pothos-widgets": "C++", "pothosware@@pothos-gui": "C++", "pothosware@@pothos-sdr": "C++", "pothosware@@pothos-blocks": "C++", "pothosware@@pothos-python": "C++", "pothosware@@pothos-audio": "C++", "pothosware@@pothos-serialization": "C++", "moritz-wundke@@Boost-for-Android": "Shell", "FFTW@@fftw3": "C", "sethtroisi@@libgmp": "C", "potato77@@RL_swarm_planning": null, "fwsGonzo@@tinyprintf": "C", "paparazzi@@luftboot": "C", "enacuavlab@@fatfs": "C", "ChibiOS@@ChibiOS": "C", "paparazzi@@libzbar": "C", "swift-nav@@libsbp": "C", "paparazzi@@mavlink": "Python", "paparazzi@@pprzlink": "Python", "tudelft@@opencv_bebop": "<PERSON><PERSON><PERSON>", "sfwa@@TRICAL": "C", "paparazzi@@hacl-c": "C", "paparazzi@@rustlink": "Rust", "paparazzi@@key_generator": "Rust", "tudelft@@gazebo_models": null, "PX4@@ecl": "C++", "PX4@@Matrix": "C++", "mabl@@ChibiOS": "C", "tudelft@@ardrone2_vision": "C", "ChibiOS@@ChibiOS-RT": "C", "taiwins@@twclient": "C", "xeechou@@tdbus": "C", "xeechou@@ctypes": "C", "xeechou@@love-nuklear": "C", "taiwins@@taiwins-protocols": null, "taiwins@@twobjects": "C", "ReneNyffenegger@@cpp-base64": "C++", "JCash@@voronoi": "C", "thinks@@poisson-disk-sampling": "C++", "vietjtnguyen@@argagg": "C++", "jbuckmccready@@CavalierContours": "C++", "sheredom@@subprocess.h": "C", "siu@@minunit": "C", "jaseg@@pogojig": "Python", "lvgl@@lv_font_conv": "JavaScript", "lvgl@@lv_utils": "PHP", "espressif@@esp-qcloud": "C", "omniavinco@@glew-cmake": "C", "cpeditor@@QCodeEditor": "C++", "Megaxela@@QCodeEditor": "C++", "robocomp@@robocomp-examples": "Python", "rogerclarkmelbourne@@STM32duino-bootloader": "C", "victorpv@@TFT_ILI9163C": "<PERSON><PERSON><PERSON><PERSON>", "njlr@@googletest": "C++", "Josh4428@@bigg": "C", "yuki-koyama@@rand-util": "C++", "JoshuaBrookover@@bigg": "C", "valhalla@@osmlr-tile-spec": null, "ctcyang@@moderngpu": "<PERSON><PERSON>", "KhronosGroup@@OpenCL-CLHPP": "C++", "nithinn@@ncc": "Python", "openvinotoolkit@@open_model_zoo": "Python", "termbox@@termbox": "C", "nsf@@termbox": "C", "adsr@@mlbuf": "C", "MaJerle@@Embedded_Libs": "C", "node-red@@node-red": "JavaScript", "jedisct1@@dnscrypt-proxy": "Go", "jorisvink@@kore": "C", "TrevorSundberg@@minih264": "C", "embeddedartistry@@format": "Shell", "embeddedartistry@@premake-buildsystem": "<PERSON><PERSON>", "Qihoo360@@floyd": "C++", "baotiao@@floyd": "C++", "trusch@@libbcrypt": "C", "lcdr@@utils": "Python", "Xiphoseer@@LUnpack": "Rust", "sandsmark@@genieutils": "C++", "aap@@geniedoc": null, "sandsmark@@pcrio": "C", "mosra@@magnum": "C++", "mosra@@corrade": "C++", "alex-petrenko@@v4r": null, "QuasarApp@@QuasarAppLib": "C++", "qt@@qttools": "C++", "QuasarApp@@pe-parse": "C++", "QuasarApp@@QuasarAppScripts": "Shell", "QuasarApp@@zip": "C", "QuasarApp@@DesktopInstaller": "QMake", "qt@@qtbase": "C++", "xiph@@opusfile": "C", "rpetrich@@LightMessaging": "Objective-C", "rpetrich@@rocketbootstrap": "C", "rpetrich@@CaptainHook": "Objective-C", "dmlc@@rabit": "C++", "dmlc@@nccl": "<PERSON><PERSON>", "TheCherno@@glfw": "C", "johnmcfarlane@@cnl": "C++", "johnmcfarlane@@fixed_point": "C++", "kobalicek@@asmjit": "C++", "docopt@@docopt.cpp": "C++", "exjam@@spdlog": "C++", "centaurean@@cputime": "C", "centaurean@@spookyhash": "C", "RWTH-OS@@LwIP": "C", "RWTH-OS@@gcc": "C", "RWTH-OS@@binutils": "C", "RWTH-OS@@pthread-embeded": "C", "RWTH-OS@@newlib": "C", "RWTH-OS@@linux": "C", "mosra@@magnum-integration": "C++", "Duet3D@@RRFLibraries": "C++", "Duet3D@@qoi": "C", "ot@@succinct": "C++", "ot@@FastPFor": "C++", "XiaoMi@@rdsn": "C++", "tgoyne@@fontconfig": "C", "sillsdev@@icu4c": "C++", "tgoyne@@ffms2": "Shell", "tgoyne@@libass": "C", "alexanderkjeldaas@@boost-mirror": "C++", "HansKristian-Work@@dxil-spirv": "C++", "diepes@@sorttable.js": "JavaScript", "cesanta@@mongoose": "C", "moneymanagerex@@cajun": "C++", "moneymanagerex@@csv-parser": "Shell", "moneymanagerex@@cppunit": "C++", "moneymanagerex@@ctpp": "C++", "moneymanagerex@@mongoose": "C", "moneymanagerex@@wxsqlite3": "C", "Regaddi@@Chart.js": "JavaScript", "RadeonOpenCompute@@llvm": "LLVM", "RadeonOpenCompute@@lld": "C++", "RadeonOpenCompute@@hcc-clang-upgrade": "C++", "RadeonOpenCompute@@compiler-rt": "C", "ARM-software@@HWCPipe": "C++", "KhronosGroup@@KTX-Software": "C++", "vit-vit@@CTPL": "C++", "KhronosGroup@@Vulkan-Samples-Assets": null, "pdesaulniers@@DPF": "C++", "spencer-project@@spencer_messages": "CMake", "srl-freiburg@@spencer_tracking_rviz_plugin": "C++", "srl-freiburg@@animated_markers": "C++", "electronicarts@@EAAssert": "C++", "electronicarts@@EAMain": "C++", "electronicarts@@EAStdC": "C++", "electronicarts@@EATest": "C++", "Derecho-Project@@mutils-serialization": "C++", "mpmilano@@mutils": "C++", "mpmilano@@mutils-containers": "C++", "ofiwg@@libfabric": "C", "mpmilano@@mutils-serialization": "C++", "client9@@libinjection": "C", "mbedmicro@@mbed": "C", "jgkamat@@DoCIF": "Shell", "RoboJackets@@robocup-common": "C++", "cloudendpoints@@service-control-client-cxx": "C++", "DragonJoker@@Vulkan": "C++", "SciDAVis@@qwt5-qt5": "C++", "SciDAVis@@qwtplot3d": "C++", "jpcre2@@jpcre2": "C++", "simon987@@argparse": "C", "simon987@@libscan": "C", "cofyc@@argparse": "C", "enthought@@bzip2-1.0.6": "C", "davidmoreno@@onion": "C", "OKaluza@@LavaVu": "Jupyter Notebook", "Ryan-rsm-McKenzie@@CommonLibSSE": "C++", "sepehrdaddev@@libressl": "C", "openpeer@@udns": "C", "openpeer@@op-services-cpp": "C++", "openpeer@@curl-build-scripts": "Batchfile", "openpeer@@boost": "Shell", "TheQwertiest@@fb2k_utils": "C++", "ArthurSonzogni@@nlohmann_json_cmake_fetchcontent": "C++", "MRPT@@libfyaml": "C", "electro-smith@@DaisySP": "C++", "Dafang-Hacks@@v4l2rtspserver-master": "C", "Dafang-Hacks@@drivers": "C", "Dafang-Hacks@@kernel": "C", "Dafang-Hacks@@Audio": "C", "Dafang-Hacks@@mips-gcc472-glibc216-64bit": "C", "Dafang-Hacks@@uboot": "C", "Dafang-Hacks@@opus": "C", "Dafang-Hacks@@live": "C++", "Dafang-Hacks@@lame": "C", "xushiwei@@ImageMagick": "C", "AppImage@@AppImageKit": "C", "alex-spataru@@QJoysticks": "C++", "romixlab@@qmsgpack": "C++", "nitroshare@@qmdnsengine": "C++", "infincia@@mavlink": "CMake", "OpenHD@@SortFilterProxyModel": "C++", "dreamsxin@@libwebsockets": "C", "dreamsxin@@libqrencode": null, "dreamsxin@@mongo-c-driver": "C", "dreamsxin@@wiredtiger": "C", "dreamsxin@@mongoose": "C", "bobthecow@@mustache.php": "PHP", "fabpot@@Twig": "PHP", "dreamsxin@@libuv": null, "fukuchi@@libqrencode": "C", "dreamsxin@@libdeep": "C", "GraphProcessor@@Graclus": "C", "CxAalto@@lcelib": "C++", "tree-sitter@@tree-sitter-python": "JavaScript", "tree-sitter@@tree-sitter-javascript": "JavaScript", "tree-sitter@@tree-sitter-rust": "JavaScript", "tree-sitter@@tree-sitter-typescript": "JavaScript", "tree-sitter@@tree-sitter-java": "JavaScript", "tree-sitter@@tree-sitter-html": "C++", "tree-sitter@@tree-sitter-go": "JavaScript", "tree-sitter@@tree-sitter-css": "JavaScript", "tree-sitter@@tree-sitter-c-sharp": "JavaScript", "opentracing@@opentracing-cpp": "C++", "ceph@@jaeger-client-cpp": null, "ceph@@civetweb": "C", "ceph@@lua": "C", "ceph@@cephadm-adoption-corpus": "Shell", "ceph@@Beast": "C++", "ceph@@dpdk": "C", "ceph@@gmock": "C++", "ceph@@libs3": "C", "ceph@@leveldb": "C++", "softins@@jamulus-php": "PHP", "softins@@jamulus-web": "JavaScript", "softins@@jamulus-wireshark": "<PERSON><PERSON>", "pljones@@jamulus-historytool": "JavaScript", "pljones@@jamulus-jamexporter": "Shell", "Rolisteam@@DiceParser": "C++", "Rolisteam@@RPlugins": "C++", "Rolisteam@@RCharacterSheet": "C++", "harvard-acc@@aladdin": "C++", "xyzsam@@xenon": "Python", "ysshao@@aladdin": "C++", "GlPortal@@glportal_data": "GLSL", "GlPortal@@specification": null, "GlPortal@@gwen": "C++", "katursis@@urmem": "C++", "katursis@@samp-ptl": "C++", "urShadow@@urmem": "C++", "Qv2ray@@QNodeEditor": "C++", "Qv2ray@@PureSource": "C++", "Qv2ray@@backward-cpp": "C++", "Qv2ray@@QvRPCBridge": "Go", "nu-book@@zxing-cpp": "C++", "xyz347@@x2struct": "C++", "nikhilm@@qhttpserver": "C", "adobe@@stylesheet": "C++", "WohlSoft@@SQLite3": "C", "WohlSoft@@PgeGameSave": "C++", "WohlSoft@@PGE-Editor-Help": "JavaScript", "WohlSoft@@QtPropertyBrowser": "C++", "Armada651@@BugTrap": "C++", "nitrocaster@@BugTrap": "C++", "corecode@@summon-arm-toolchain": "Shell", "pascalhahn@@pirate-swd": "Python", "esden@@summon-arm-toolchain": "Shell", "QuasarApp@@Qt-AES": "C++", "QuasarApp@@mini-gmp": "C", "xaqq@@spdlog": "C++", "xaqq@@libmcp23s17": "C", "queueRAM@@sm64tools": "C", "ethteck@@splat": "Python", "LLNL@@sundials": "C", "Cantera@@sundials-mirror": "C", "JakeWharton@@ActionBarSherlock": "Java", "bauerca@@drag-sort-listview": "Java", "JakeWharton@@Android-ViewPagerIndicator": "Java", "mike4192@@spot_micro_kinematics_cpp": "C++", "mike4192@@spot_micro_kinematics_python": "Python", "jakcron@@liblz4": "C", "jakcron@@libmbedtls": "C", "jakcron@@libtoolchain": "C++", "jakcron@@libfmt": "C++", "jakcron@@libnintendo-pki": "C++", "jakcron@@libnintendo-es": "C++", "jakcron@@libnintendo-hac": "C++", "jakcron@@libfnd": "C++", "jakcron@@libnintendo-hac-hb": "<PERSON><PERSON><PERSON>", "jakcron@@libpolarssl": "C", "eth-cscs@@Tiled-MM": "C++", "teonnik@@gtest_mpi": "C++", "kabicm@@semiprof": "C++", "kabicm@@Tiled-MM": "C++", "kabicm@@grid2grid": "C++", "kabicm@@options": "C++", "Microsoft@@SEAL": "C++", "everitoken@@chainbase": "C++", "everitoken@@appbase": "C++", "everitoken@@rapidjson": "C++", "FreeRTOS@@FreeRTOS": "C", "hathach@@lpcopen": "C", "daquexian@@onnx": "PureBasic", "OpenSpace@@Ghoul": "C++", "OpenSpace@@Kameleon": "C", "mkalten@@TUIO11_CPP": "C", "OpenSpace@@minvr": "C++", "sgct@@sgct": "C", "OpenSpace@@CCfits": "HTML", "OpenSpace@@cfitsio": "C", "opensgct@@glfw": "C", "opensgct@@sgct": "C", "OpenSpace@@libtorrent": "C++", "LansburyCH@@eos-expression-aware-proxy": "C++", "apple@@swift-corelibs-foundation": "Swift", "apple@@darwin-xnu": "C", "phoboslab@@JavaScriptCore-iOS": "C++", "insane-adding-machines@@busybox": "C", "insane-adding-machines@@picotcp": "C", "insane-adding-machines@@libopencm3": "C", "google@@marl": "C++", "google@@ukey2": "Java", "electron@@node": null, "electron@@chromium-breakpad": "C++", "electron@@crashpad": "C++", "boto@@boto": "Python", "electron@@pdf-viewer": "JavaScript", "electron@@gyp": "Python", "electron@@native-mate": "C++", "electron@@brightray": "C++", "zcbenz@@native-mate": "C++", "atom@@brightray": "C++", "atom@@node": "JavaScript", "atom@@chromium-breakpad": "C++", "atom@@apm": "CoffeeScript", "aroben@@brightray": "C++", "tschoonj@@GTK-for-Windows-Runtime-Environment-Installer": "Python", "jnastarot@@enma_pe": "C++", "jnastarot@@capstone": "C", "jnastarot@@shibari": "C++", "no1msd@@json.hpp": "C++", "thebracket@@rltk": "C++", "SFML@@SFML": "C++", "LuaDist@@lua": "C", "martinrotter@@sed": null, "martinrotter@@qt-binaries": null, "jacob-carlborg@@mambo": "D", "roleoroleo@@onvif_srvd": "C++", "KDE@@kldap": "C++", "oKcerG@@SortFilterProxyModel": "C++", "Skycoder42@@QConsole": "C++", "boostorg@@filesystem": "C++", "boostorg@@io": "C++", "boostorg@@functional": "HTML", "arun11299@@cpp-subprocess": "C++", "Turbine1991@@cpp-feather-ini-parser": "C++", "loki-project@@loki-mq": "C++", "ampl@@pathlib": "C", "ampl@@gsl": "C", "ampl@@cbc": "C++", "vitaut@@gsl": "C", "vitaut@@cbc": "C++", "xiongziliang@@ZLToolKit": "C++", "howerj@@libline": "C", "avast-tl@@libdwarf": "C", "avast-tl@@llvm": "LLVM", "NVIDIA@@TensorRT": "C++", "mattdawkins@@scallop-tk": "C++", "VIAME@@pytorch": null, "Erotemic@@netharn": "Python", "mattdawkins@@py-faster-rcnn": "Python", "Kitware@@vibrant": "C++", "Embroidermodder@@libembroidery": "C", "mnhrdt@@iio": "C", "gfacciol@@mgm": "C", "mnhrdt@@tvl1flow": "C", "Vector35@@asmx86": "C++", "Vector35@@BinaryTestCases": "Assembly", "AndrewBelt@@imgui": "C++", "marton78@@pffft": "C", "guillaumechereau@@noc": "C", "openconfig@@gnmi": "Go", "openconfig@@public": "Shell", "tvdzwan@@protobuf": "C++", "coin-or@@CppAD": "C++", "eric-heiden@@CppADCodeGen": "C++", "esa@@pagmo2": "C++", "oneapi-src@@oneTBB": "C++", "erwincoumans@@boost": "C++", "oxfordcontrol@@osqp": "C++", "coin-or@@qpOASES": "C++", "getsentry@@libunwindstack-ndk": "C++", "getsentry@@breakpad": "C++", "hglm@@detex": "C", "vadz@@libtiff": "C", "OSGeo@@proj.4": "C++", "fpw@@XSDK": "<PERSON>", "OSGeo@@libgeotiff": "HTML", "ldarren@@QuickJS": "C", "boostorg@@tti": "C++", "gammasoft71@@xtd_delegates": "C++", "gammasoft71@@xtd_properties": "C++", "gammasoft71@@xtd_tunit": "C++", "gammasoft71@@xtd.tunit": "C++", "noporpoise@@BitArray": "C", "noporpoise@@string_buffer": "C", "noporpoise@@madcrowlib": "C", "samtools@@bcftools": "C", "noporpoise@@seq_file": "C", "noporpoise@@sort_r": "C", "noporpoise@@bioinf-perl": "<PERSON><PERSON>", "noporpoise@@msg-pool": "C", "noporpoise@@readsim": "C", "noporpoise@@seq-align": "C", "noporpoise@@biogrok": "Shell", "noporpoise@@vcf-slim": "C", "noporpoise@@carrays": "C", "GPUOpen-LibrariesAndSDKs@@D3D12MemoryAllocator": "C++", "AndreRH@@glib": "C", "jrk@@llvm": "C++", "chronoxor@@CppBuildScripts": "Batchfile", "chronoxor@@CppCMakeScripts": "CMake", "chronoxor@@CppBenchmark": "C++", "martine@@ninja": "C++", "masagrator@@libtesla": "C", "emanuele-f@@nDPI": "C", "emanuele-f@@zdtun": "C", "emanuele-f@@PCAPdroid_res": "Shell", "nektra@@Deviare-InProc": "C++", "liancheng@@amy": "C++", "koemeet@@rtti-obfuscator": "C++", "sphair@@ClanLib": "C++", "pulp-platform@@riscv-torture": "Scala", "pulp-platform@@uvm-components": "SystemVerilog", "pulp-platform@@axi2per": "SystemVerilog", "apitrace@@gltrim-tests": null, "apitrace@@libpng": "C", "rigaya@@ffmpeg_dlls_for_hwenc": "C", "webosbrew@@native-dev-utils": "CMake", "LnxPrgr3@@message_queue": "C", "graphitemaster@@incbin": "C", "webosbrew@@tv-native-apis": "C", "mariotaku@@lvgl": "C", "videolabs@@libmicrodns": "C", "mjansson@@mdns": "C", "NVIDIA@@BROADCAST-AR-SDK": "C", "Azure@@sonic-quagga": "C", "Azure@@sonic-platform-modules-s6000": "C", "Azure@@sonic-platform-modules-dell": "C", "Ingrasys-sonic@@sonic-platform-modules-ingrasys": "Shell", "edge-core@@sonic-platform-modules-accton": "C", "celestica-Inc@@sonic-platform-modules-cel": "C", "FRRouting@@frr": "C", "Ingrasys-sonic@@sonic-platform-modules-ingrasys-nephos": "Shell", "QuantaSwitchONIE@@sonic-platform-modules-quanta": "C", "krambn@@switch": "C", "krambn@@behavioral-model": "C++", "opencomputeproject@@SAI": "Python", "Azure@@sonic-docker-base": "Python", "lguohan@@switch": "C", "lguohan@@behavioral-model": "C++", "pelya@@commandergenius": "C", "etcd-cpp-apiv3@@etcd-cpp-apiv3": "C++", "microsoft@@cpprestsdk": "C++", "boostorg@@leaf": "C++", "aliyun@@aliyun-oss-cpp-sdk": "C++", "mypaint@@libmypaint": "C", "opencor@@qscintilla": "C++", "smassung@@porter2_stemmer": "C++", "meta-toolkit@@porter2_stemmer": "C++", "whimsicalraps@@wrLib": "C", "whimsicalraps@@wrDsp": "C", "trentgill@@dfu-stm32f7": "C", "trentgill@@lua": "C", "trentgill@@STM32CubeF7_Drivers": "C++", "andrusha97@@rapidjson": "C++", "chriskohlhoff@@urdl": "C++", "luoyetx@@jsmnpp": "C++", "magiblot@@tvision": "C++", "eschkufz@@cl": "C++", "google@@iree-llvm-fork": null, "dvidelabs@@flatcc": "C", "axboe@@liburing": "C", "google@@iree-mhlo-fork": null, "yaml@@libyaml": "C", "webgpu-native@@webgpu-headers": "C", "tensorflow@@mlir-hlo": "C++", "iml130@@mlir-emitc": "C++", "google@@ruy": "C++", "google@@llvm-bazel": "Starlark", "google@@swiftshader": "C++", "SDL-mirror@@SDL": "C", "tensorflow@@mlir": null, "flame@@blis": "C", "poulson@@metis": "C", "aerospike@@aerospike-lua-core": "<PERSON><PERSON>", "GPUOpen-LibrariesAndSDKs@@RadeonImageFilter": "C++", "GPUOpen-LibrariesAndSDKs@@RadeonProRenderSDK": "C", "materialx@@MaterialX": "C++", "hugoam@@two": "C++", "hugoam@@mud": "C++", "openthread@@openthread": "C++", "benmcollins@@libjwt": "C", "redboltz@@mqtt_cpp": "C++", "Itokoyamato@@teamspeak-plugin-radiofx": "C++", "vinniefalco@@DSPFilters": "C++", "thorwe@@teamspeak-plugin-qt-common": "C++", "dariomanesku@@bx": "C++", "dariomanesku@@bgfx": "C++", "dariomanesku@@cmft": "C", "awslabs@@amazon-kinesis-video-streams-pic": "C++", "LancePutnam@@Gamma": "C++", "foonathan@@array": "C++", "foonathan@@type_safe": "C++", "topisani@@Choreograph": "C++", "topisani@@NanoCanvas": "C++", "alexmarsev@@libbs2b": "C", "mpc-hc@@LAVFilters": "C++", "MediaArea@@MediaInfoLib": "C++", "mpc-hc@@rarfilesource": "C++", "mpc-hc@@sanear": "C++", "alexmarsev@@soundtouch": "C++", "alexmarsev@@soxr": "C", "drtimcooper@@XmlRpc4Win": "C++", "MediaArea@@ZenLib": "C++", "alexmarsev@@sanear": "C++", "eggs-cpp@@variant": "C++", "amzn@@ion-tests": null, "iamantony@@qtcsv": "C++", "alex-spataru@@CuteLogger": "C++", "nmwsharp@@geometry-central": "C++", "nmwsharp@@polyscope": "C++", "sstephenson@@bats": "Shell", "fbxiang@@optifuser": "C++", "haosulab@@sapien-vulkan-2": "C++", "chwarr@@grpc": "C++", "riscv@@riscv-qemu": "C", "intel-isl@@open3d_sphinx_theme": "HTML", "intel-isl@@Open3D-PoissonRecon": "C++", "oneapi-src@@oneDPL": "C++", "google@@filament": "C++", "hydrabus@@tokenline": "C", "hydrabus@@ChibiOS": "C", "bvernoux@@tokenline": "C", "biot@@tokenline": "C", "rilian-la-te@@cmake-vala": "CMake", "caiorss@@winapi-snippets-cmake": "C++", "feelpp@@nlopt": "C", "feelpp@@hpddm": "C++", "fangq@@jsonlab": "MATLAB", "feelpp@@ipopt": "C++", "feelpp@@pybind11": "C++", "feelpp@@mongo-cxx-driver": "C++", "feelpp@@kwsys": "C++", "feelpp@@viewer": "JavaScript", "feelpp@@benchmark": "C++", "feelpp@@fmi-library": "C", "feelpp@@tabulate": "C++", "feelpp@@ParMmg": "C", "feelpp@@mmg": "C", "feelpp@@indicators": "C++", "feelpp@@range-v3": null, "feelpp@@matplotplusplus": "C++", "feelpp@@cpr": "C++", "feelpp@@hana": null, "feelpp@@fmt": "C++", "vincentchabannes@@napp": "C++", "feelpp@@FMI4cpp": "C++", "feelpp@@Ipopt": "C++", "feelpp@@nt2": "C++", "feelpp@@paralution": "C++", "feelpp@@minieigen": "C++", "cemosis@@seme-cerema": "C++", "feelpp@@quickstart": "CMake", "hpddm@@hpddm": "C++", "lxqt@@qtermwidget": "C++", "boostorg@@align": "C++", "boostorg@@algorithm": "C++", "learnedsystems@@RMI": "Rust", "Christoph-Maximilian@@FST": "C++", "gvinciguerra@@PGM-index": "C++", "learnedsystems@@RadixSpline": "C++", "RyanMarcus@@fast64": "Rust", "wuxb45@@wormhole": "C", "microsoft@@ALEX": "C++", "stoianmihail@@CHT": "C++", "stoianmihail@@TrieSpline": "C++", "alamaison@@winapi": "C++", "haxmeadroom@@fuzzgoat": "C", "CREDITSCOM@@berkeleydb": "C", "CREDITSCOM@@thrift-interface-definitions": "Thrift", "CREDITSCOM@@thrift": "C++", "CREDITSCOM@@cscrypto": "C++", "drycpp@@lmdbxx": "C++", "Neargye@@nameof": "C++", "efficient@@libcuckoo": "C++", "AlexanderSelzer@@radioparse": "JavaScript", "tolgahanakgun@@krack": "Python", "tadashi@@wifi-monitor": null, "wraith-wireless@@pyric": "Python", "adde88@@hostapd-mana-openwrt": "C", "adde88@@hostapd-mana": "C", "UtkMSNL@@IEEE802.11-complete": "Python", "FluxionNetwork@@fluxion": "HTML", "blaa@@WifiStalker": "Python", "Wh1t3Rh1n0@@air-hammer": "Python", "0x90@@piwat": "<PERSON>", "0x90@@pyrit": "Python", "danielhk@@android_external_wpa_supplicant_8_ath": "C", "thuehn@@Minstrel-Blues": "<PERSON><PERSON>", "smd-ros-devel@@smd_wifi_monitor": "C++", "shizhai@@wprobe": "C", "taoliu-fnet@@airdump": "C++", "prahladyeri@@hotspotd": "Python", "techlib@@wifinator": "Python", "eibbors@@reavetard": "CoffeeScript", "entropy1337@@infernal-twin": "Python", "vvalien@@ptf-modules": "Python", "rabbitmq@@rabbitmq-codegen": "Python", "supercollider@@portaudio": "C", "pqrs-org@@Karabiner-DriverKit-VirtualHIDDevice": "C++", "pipeacosta@@sumolib4matlab": "Java", "gunnarfloetteroed@@java": "Java", "taosdata@@driver-go": "Go", "taosdata@@hivemq-tdengine-extension": "Java", "taosdata@@TSZ": "C", "taosdata@@taos-tools": "C", "taosdata@@taosadapter": "Go", "songtianyi@@tdengine-rust-bindings": "Rust", "taosdata@@grafanaplugin": "Go", "psuriana@@Halide": "C++", "guangqianpeng@@jackson": "C++", "openigtlink@@OpenIGTLink": "C++", "smistad@@caffe": "C++", "smistad@@OpenCLUtilityLibrary": "C++", "jkevin@@PS3EYEDriver": "C++", "falltergeist@@libfalltergeist": "C++", "ebertolazzi@@Utils": "C++", "ebertolazzi@@quarticRootsFlocke": "C++", "nayutaco@@bech32": "C", "p4lang@@SAI": "C", "Blue-Rocket@@clucene": "C++", "GPUOpen-ProfessionalCompute-Libraries@@amdovx-core": "C++", "cls@@libutf": "C", "Musicoll@@kiwi-node-server": "JavaScript", "boostorg@@beast": "C++", "julianstorer@@JUCE": "C++", "openMVG-thirdparty@@OpenExif": "C++", "cc2qe@@lumpy-sv": "C", "cc2qe@@svtyper": "Python", "hall-lab@@sv-tools": "Python", "bear101@@tolk": "C", "BearWare@@toolchain": "<PERSON><PERSON><PERSON>", "CasualPokePlayer@@Emu83": "C", "TASVideos@@GLideN64": "C", "TASVideos@@mupen64plus-rsp-cxd4": "C", "TASVideos@@snes9x": "C++", "TASVideos@@mgba": "C", "TASVideos@@melonDS": "C", "TASVideos@@mednafen": "Objective-C", "TASVideos@@gambatte-speedrun": null, "openvehicles@@mongoose": "C", "openvehicles@@zlib": "C", "Staacks@@phyphox-experiments": null, "Staacks@@phyphox-webinterface": "HTML", "Kuhlen@@phyphox-experiments": null, "bluebird75@@luaunit": "<PERSON><PERSON>", "CocoaLumberjack@@CocoaLumberjack": "Objective-C", "ShadowsocksR-Live@@OpenSSL-for-iPhone": "Shell", "haxpor@@libmaxminddb": "C", "haxpor@@Eureka": "Swift", "encryptogroup@@ENCRYPTO_utils": "C++", "encryptogroup@@OTExtension": "C++", "CertiVox@@MIRACL": "C", "TheCrypt0@@mqttv4": "C", "ix-project@@dune": "C", "ix-project@@pcidma": "C", "Siguza@@iometa": "C", "jakeajames@@jelbrekLib": "C", "theos@@theos": "<PERSON><PERSON><PERSON>", "iGhibli@@iOS-DeviceSupport": "Python", "bazad@@ida_kernelcache": "Python", "stefanesser@@IDA-IOS-Toolkit": null, "trezor@@trezor-qrenc": "C", "parallel101@@hw01": "C", "parallel101@@hw02": "C++", "parallel101@@hw03": "C++", "parallel101@@hw04": "C++", "parallel101@@subtitles": null, "parallel101@@hw05": "C++", "parallel101@@hw06": "C++", "parallel101@@hw07": "C++", "jgarff@@rpi_ws281x": "C", "SapphireServer@@recastnavigation": "C++", "xenia-project@@libav": "C", "JoelLinn@@SDL": null, "benvanik@@gflags": "Shell", "premake@@premake-core": "C", "benvanik@@beaengine": "C", "xenia-project@@dockpanelsuite": "C#", "benvanik@@sparsehash": "C++", "benvanik@@asmjit": "C++", "deskvox@@deskvox": "C++", "kanaka@@noVNC": "JavaScript", "szellmann@@visionaray": "C++", "hlrs-vis@@nvtop": "C", "vistle@@univiz": "C++", "hlrs-vis@@OpenFOAM-Exporter": "<PERSON><PERSON>", "vitaut@@breathe": "Python", "vitaut@@format-benchmark": "C++", "sergiud@@bzip2": "C", "plenluno@@convertutf": "C++", "LuaDist@@libjpeg": "C", "AnastaZIuk@@glm": null, "svn2github@@pcre": "C", "AnastaZIuk@@glew-cmake": null, "devshgraphicsprogramming@@RadeonRays_SDK": "C++", "jacobly0@@fasmg-ez80": "Assembly", "drdnar@@convfont": "C", "mateoconlechuga@@convbin": "<PERSON>", "mateoconlechuga@@convhex": "<PERSON>", "mateoconlechuga@@convpng": "C", "MattWaltz@@convhex": "<PERSON>", "MattWaltz@@convpng": "C", "CE-Programming@@libload": "Assembly", "walmis@@esp32-espfs": null, "walmis@@libesphttpd": "C", "walmis@@blackmagic-esp8266": "C", "heiher@@ini-parser": "C", "ShadowsocksR-Live@@pthreads4w": "C", "ShadowsocksR-Live@@pcre": "C", "ShadowsocksR-Live@@zlib": "C", "RfidResearchGroup@@proxmark3": "C", "xianglin1998@@proxmark3": "C", "drebain@@OpenGP": "C++", "BlueBrain@@hpc-coding-conventions": "CMake", "neuronsimulator@@ringtest": "Python", "neuronsimulator@@pythontutorial": "Jupyter Notebook", "neuronsimulator@@rxd-tutorials": "Jupyter Notebook", "neuronsimulator@@progref-py": "Python", "neuronsimulator@@progref-hoc": "Python", "rhempel@@c-helper-macros": "C", "irods@@irods_schema_configuration": "Python", "cyoung@@linux-mpu9150": "C", "coolstar@@VoodooGPIO": "C++", "alexandred@@VoodooI2CHID": "C++", "kprinssu@@VoodooI2CELAN": "C++", "blankmac@@VoodooI2CUPDDEngine": "C++", "alexandred@@VoodooI2CSynaptics": "C++", "prizraksarvar@@VoodooI2CFTE": "C++", "algorithm-ninja@@cpp-btree": "C", "ArneMayer@@cqf": "C++", "hyrise@@pgasus": "C", "TKJElectronics@@BalanduinoAndroidApp": "Java", "TKJElectronics@@BalanduinoProcessingApp": "Erl<PERSON>", "TKJElectronics@@BalanduinoWindowsApp": "Visual Basic", "jeremybarnes@@jml": "C++", "jeremybarnes@@jml-build": "<PERSON><PERSON><PERSON>", "datacratic@@leveldb": "C++", "datacratic@@tinyxml2": "C++", "datacratic@@google-url": "C++", "meshula@@LabMidi": "C++", "scarsty@@common": "C++", "jatinchowdhury18@@foleys_gui_magic": null, "Chowdhury-DSP@@chowdsp_utils": "C++", "jatinchowdhury18@@RTNeural": "C++", "ffAudio@@foleys_gui_magic": "C++", "McMartin@@FRUT": "CMake", "embotech@@ecos": "C", "emoon@@gdb-remote": "Rust", "emoon@@scintilla": "C++", "v3n@@bgfx": "C++", "VoodooSMBus@@VoodooSMBus": "C++", "dthaler@@ebpf-verifier": "C++", "iovisor@@ubpf": "C", "dthaler@@bpftool-1": null, "vbpf@@ebpf-verifier": "C++", "dthaler@@bpftool": "C", "EKA2L1@@dynarmic": "C++", "MerryMage@@ext-boost": "C++", "eka2l1@@ffmpeg": "C", "EKA2L1@@compat-config": null, "citra-emu@@dynarmic-android": "C++", "luajit@@luajit": "C", "EKA2L1@@lunasvg": "C++", "EKA2L1@@miniBAE": null, "merrymage@@dynarmic": "C++", "EKA2L1@@oss.API_REF.Public_API": "C++", "gileoo@@Imgui-IGS-Snippets": "C++", "bentokun@@FRSML": "C++", "monocasual@@rtaudio": "C++", "monocasual@@geompp": "C++", "monocasual@@mcl-audio-buffer": "C++", "apache@@incubator-brpc": "C++", "island-org@@stb": "C", "island-org@@Remotery": "C", "cesanta@@v7": "C", "island-org@@tinyobjloader-c": "C++", "prideout@@par": "C", "xant@@libhl": "C", "island-org@@tinydir": "C", "AdrienHerubel@@imgui": "C", "bendiken@@lmdbxx": "C++", "andymatuschak@@Sparkle": "Objective-C", "vslavik@@winsparkle": "C++", "svn2github@@inih": "C", "yuzu-emu@@unicorn": "C", "yuzu-emu@@ext-boost": "C++", "ogniK5377@@opus": "C", "thpatch@@win32_utf8": "C", "thpatch@@thtypes": "C", "brliron@@135tk": "C", "thpatch@@thcrap_external_dependencies": "C", "nmlgc@@libmpg123": "C", "thpatch@@Act-Nut-lib": "C++", "Element-0@@vcpkg": "C++", "CodeHz@@mini_bus_cpp": "C++", "Element-0@@boost-cmake": null, "CodeHz@@ws-gw": "C++", "katahiromz@@EGA": "C++", "facebook@@mcrouter": "C++", "facebook@@proxygen": "C++", "facebook@@squangle": "C++", "facebook@@mysql-5.6": "C++", "ocaml@@ocaml": "OCaml", "ocaml@@ocamlbuild": "OCaml", "rsocket@@rsocket-cpp": "C++", "apache@@xerces-c": "C++", "apache@@xalan-c": "C++", "JGCRI@@hector": "C++", "JGCRI@@modelinterface": "Java", "vector-im@@riot-web": "TypeScript", "matrix-org@@matrix-js-sdk": "TypeScript", "AMReX-Codes@@amrex": "C++", "ethz-asl@@rosserial": "C++", "mousebird@@shapelib": "C", "mousebird@@proj-4": "C", "mousebird@@libjson": "C++", "mousebird@@glues": "C++", "mousebird@@KissXML": "Objective-C", "ccgus@@fmdb": "Objective-C", "nfarina@@calloutview": "Objective-C", "mousebird@@clipper": "C#", "mousebird@@protobuf": "C++", "mousebird@@LASzip": "C++", "mousebird@@boost": "C++", "AFNetworking@@AFKissXMLRequestOperation": "Objective-C", "lunixbochs@@glues": "C++", "trailbehind@@clipper": "C#", "mousebird@@eigen": "C++", "mousebird@@wg-resources": null, "torproject@@tor": "C", "StefanBruens@@ESP8266_new_pwm": "C", "espressif@@esp-gdbstub": "C", "cxong@@cbehave": "C++", "hannespetur@@seqan": "C++", "hannespetur@@paw": "C++", "hannespetur@@libStatGen": "C++", "hannespetur@@SeqAnHTS": "C++", "hannespetur@@stations": "C++", "ubisoft@@Sharpmake": "C#", "soufianekhiat@@glad": "C", "Geri-Borbas@@Clipper": "<PERSON>", "wichtounet@@cpp_utils": "C++", "wichtounet@@cpm": "C++", "rizinorg@@ghidra": "Java", "securesocketfunneling@@cmake-build-system": "CMake", "secmob@@cansecwest2016": "HTML", "fi01@@CVE-2015-3636": "C", "secmob@@PoCForCVE-2015-1528": "C++", "jduck@@cve-2015-1538-1": "Python", "UnataInc@@PyCon2017": "Jupyter Notebook", "TheAssemblyArmada@@BaseConfig": "C", "TheAssemblyArmada@@SetSail": "C++", "dusty-nv@@pytorch-imagenet": "Python", "openLuat@@LuatOS-qemu-vexpress-a9": "C", "openLuat@@LuatOS-W60x": "C", "lloyd@@yajl": "C", "tarantool@@tarantool-c": "C", "wiedi@@libmaia": "C++", "knolleary@@pubsubclient": "C++", "z3t0@@Arduino-IRremote": "C++", "sparkfun@@SparkFun_BME280_Arduino_Library": "C++", "LowPowerLab@@RFM69": "C++", "tklengyel@@drakvuf-doppelganging": "C", "tklengyel@@xen": "C", "w4123@@vcpkg": "CMake", "neotron@@PathFinder": "C++", "neotron@@EDJournalQT": "C++", "Mudlet@@edbee-lib": "C++", "martin-eden@@lua_code_formatter": "<PERSON><PERSON>", "Mudlet@@dblsqd-sdk-qt": "C++", "Mudlet@@mixing-cocoa-and-qt": "CMake", "Mudlet@@qtkeychain": "C++", "dicene@@edbee-lib": null, "edbee@@edbee-lib": "C++", "Mudlet@@lua_code_formatter": "<PERSON><PERSON>", "aergoio@@luajit": "C", "glotzerlab@@upp11": "C++", "glotzerlab@@nano-signal-slot": "C++", "glotzerlab@@cub": "<PERSON><PERSON>", "glotzerlab@@quickhull": "C++", "glotzerlab@@random123": "C++", "glotzerlab@@HIP": null, "glotzerlab@@hipCUB": null, "mphowardlab@@neighbor": "C++", "mphowardlab@@hipper": "C++", "glotzerlab@@libgetar": "C", "glotzerlab@@pybind11": "C++", "glotzerlab@@cereal": "C++", "glotzerlab@@eigen-git-mirror": "C++", "joaander@@upp11": "C++", "NoAvailableAlias@@nano-signal-slot": "C++", "TheCherno@@imgui": "C++", "premake@@premake-xcode": "<PERSON><PERSON>", "premake@@premake-monodevelop": "<PERSON><PERSON>", "premake@@premake-codelite": "<PERSON><PERSON>", "HeliumProject@@Foundation": null, "HeliumProject@@Inspect": null, "HeliumProject@@Math": null, "HeliumProject@@MathSimd": null, "HeliumProject@@Mongo": null, "HeliumProject@@Persist": null, "HeliumProject@@Platform": null, "HeliumProject@@ThreadBuildingBlocks": "C++", "HeliumProject@@Expat": "C", "HeliumProject@@Reflect": null, "ned14@@nedmalloc": "C", "Snaipe@@libcsptr": "CMake", "Snaipe@@dyncall": "C", "Others@@musl": "C", "adishavit@@argh": "C++", "Didstopia@@physfs": "C", "ArashPartow@@bitmap": "C++", "kmhofmann@@selene": "C++", "criptych@@physfs": null, "limine-bootloader@@limine": "C", "wanduow@@wandio": "C", "mangos@@mangosDeps": "C++", "mangos@@realmd": "C++", "mangos@@ScriptDev3": "C++", "mangos@@Extractor_projects": "C++", "mangostools@@EasyBuild": "Batchfile", "MangosServer@@Eluna": "C++", "elunaluaengine@@eluna": "C++", "auriamg@@macdylibbundler": "C++", "ned14@@kerneltest": "C++", "ned14@@quickcpplib": "C", "ned14@@afio": "C++", "ned14@@boost.afio": "C++", "ned14@@boost-lite": "C", "ned14@@boost.outcome": "C++", "martinmoene@@gsl-lite": "C++", "riebl@@artery_scenario_mt-its2017": "Python", "riebl@@inet": "C++", "riebl@@simulte": "C++", "riebl@@vanetza": "C", "sommer@@veins": "C++", "shogun-toolbox@@shogun-tutorial": null, "matlo@@gimx-adapter-protocol": "C", "matlo@@gimxpoll": "C", "matlo@@gimxprio": "C", "matlo@@gimxusb": "C", "matlo@@gimxhid": "C", "matlo@@gimxinput": "C", "matlo@@gimxserial": "C", "matlo@@gimxtimer": "C", "matlo@@gimxuhid": "C", "matlo@@gimxcommon": "C", "matlo@@gimx-network-protocol": "C", "matlo@@gimxlog": "C", "matlo@@gimxfile": "C++", "matlo@@gimxtime": "C++", "FDOS@@share": "C", "PX4@@libuavcan": "C++", "PX4@@PX4-Matrix": "C++", "PX4@@PX4-ECL": "C++", "PX4@@cmake_hexagon": "CMake", "ATLFlight@@dspal": "C", "PX4@@uavcan": "C++", "PX4@@micro-CDR": "C++", "PX4@@sitl_gazebo": "C++", "PX4@@genmsg": "Python", "PX4@@gencpp": "Python", "PX4@@DriverFramework": "C++", "ros@@genmsg": "Python", "ros@@gencpp": "Python", "ATLFlight@@cmake_hexagon": "CMake", "eProsima@@micro-CDR": "C++", "PX4-NuttX@@nuttx": "C", "PX4-NuttX@@apps": "C", "mavlink@@c_library_v1": "C", "PX4@@PX4NuttX": null, "sjwilks@@gtest": "C++", "pavel-kirienko@@uavcan": "C", "yuanming-hu@@taichi_assets": "C++", "taichi-dev@@glfw": "C", "taichi-dev@@taichi_glad_ready": "C", "taichi-dev@@taichi_assets": null, "KhronosGroup@@SPIRV-Reflect": "C", "jrmadsen@@cereal": "C++", "poke1024@@nanosvg": "C", "poke1024@@polypartition": "C++", "Curve@@fancypp": "C++", "Soundux@@soundux-ui": "<PERSON><PERSON>", "Soundux@@webviewpp": "C++", "Soundux@@traypp": "C++", "Neargye@@semver": "C++", "Soundux@@lockpp": "C++", "Soundux@@guardpp": "C++", "jdunmire@@kicad-ESP8266": "OpenSCAD", "littlevgl@@lv_lib_lodepng": "C", "switch-iot@@n2n": "C", "sirikata@@berkelium": "C++", "sirikata@@pbj": "C++", "sirikata@@cxxtest": "C++", "sirikata@@prox": "C++", "sirikata@@sirikata-protocol": null, "sirikata@@liboauthcpp": "C++", "ajaxorg@@ace": "JavaScript", "danielrh@@pbj": "C++", "danielrh@@prox": "C++", "danielrh@@cxxtest": "Python", "pulseaudio@@pulseaudio": "C", "ValveSoftware@@wine": "C", "ValveSoftware@@dxvk": "C++", "Kitware@@CMake": "C", "ValveSoftware@@vkd3d": "C", "doitsujin@@dxvk": "C++", "KhronosGroup@@MoltenVK": "Objective-C++", "AnimatedCircuits@@RackModules": "C++", "CardinalModules@@Fundamental": "C", "VCVRack@@Befaco": "C++", "AScustomWorks@@AS": "C++", "CardinalModules@@ValleyAudio": "C++", "Xenakios@@Atelier": "C++", "keystone-enclave@@riscv-pk": "C", "riscv@@riscv-linux": "C", "keystone-enclave@@busybear-linux": "Shell", "keystone-enclave@@riscv-linux": "C", "Kode@@koremake": "TypeScript", "CBICA@@dcmqi": "C++", "CBICA@@greedy": "C++", "tihmstar@@img4tool": "C++", "GPUOpen-Tools@@common-lib-ext-tinyxml-2.6.2": "C++", "GPUOpen-Tools@@common-lib-ext-OpenGL": "C", "GPUOpen-Tools@@common-lib-ext-OpenCV-2.49": "C++", "GPUOpen-Tools@@common-lib-ext-Boost-1.59": "C++", "GPUOpen-Tools@@common-lib-amd-APPSDK-3.0": "C", "GPUOpen-Tools@@common-lib-ext-glew-1.9": "C", "GPUOpen-Tools@@common-lib-ext-zlib-1.2.10": "C", "GPUOpen-Tools@@common-lib-ext-Vulkan-********": "C++", "GPUOpen-Tools@@common-lib-ext-zlib-1.2.8": "C", "GPUOpen-Tools@@common-lib-ext-Qt-5.5": "C++", "GPUOpen-Tools@@common-lib-ext-OpenEXR-1.4": "C++", "Sonicadvance1@@vixl": null, "monero-project@@unbound": "C", "luvit@@openssl": "Assembly", "kosua20@@GL_Template": "C++", "apache@@httpd": "C", "google@@closure-library": "JavaScript", "apache@@incubator-pagespeed-icu": "C++", "apache@@apr": "C", "apache@@incubator-pagespeed-optipng": "C", "apache@@serf": "C", "google@@sparsehash": "C++", "apache@@incubator-pagespeed-drp": "Python", "pagespeed@@mod_fcgid": "C", "pagespeed@@giflib": "C", "pagespeed@@icu": "C++", "pagespeed@@optipng": "C", "pagespeed@@zlib": "C", "pagespeed@@domain-registry-provider": "Python", "JonnyH@@glm": "C++", "JonnyH@@physfs-hg-import": "C", "JonnyH@@libsmacker": "C", "JonnyH@@miniz": "C", "tessel@@miniz": "C", "feragon@@nano-signal-slot": "C++", "sandyre@@libopencad": "C++", "satoren@@kaguya": "C++", "SteveKChiu@@lua-intf": "C++", "electronicarts@@EAThread": "C++", "elucideye@@drishti-upload": "CMake", "hunter-packages@@xgboost": "C++", "hunter-packages@@thread-pool-cpp": "C++", "hunter-packages@@ogles_gpgpu": "C++", "elucideye@@polly": "CMake", "hunter-packages@@imshow": "CMake", "hunter-packages@@boost-pba": "C++", "headupinclouds@@xgboost": "C++", "headupinclouds@@thread-pool-cpp": "C++", "headupinclouds@@ogles_gpgpu": "C++", "oscarlab@@gcc_test_files": "C", "oscarlab@@lmbench-2.5": "C", "nekopanda@@googletest": "C++", "desktop-app@@lib_ton": "C++", "desktop-app@@lib_wallet": "C++", "desktop-app@@lib_updater": "C++", "exelix11@@LibUsbDotNet": null, "exelix11@@SharpRTSP": null, "paulsapps@@TinyXML": "C++", "paulsapps@@luabind": "C++", "q-gears@@data": "<PERSON><PERSON>", "paulsapps@@SUDM": "C++", "SDLash3D@@halflife": "C++", "Tom94@@nanogui": "C++", "pybricks@@ev3rt-hrp2": null, "PetteriAimonen@@libfixmath": "C", "pybricks@@nxt-firmware-drivers": "C", "pybricks@@micropython": "C", "jahshaka@@breakpad": "C++", "jahshaka@@Effects": "C++", "jahshaka@@Miner": "C++", "actor-framework@@benchmarks": "C++", "actor-framework@@nexus": "C++", "actor-framework@@riac": "C++", "KDE@@syntax-highlighting": "HTML", "nekitu@@binpack": "C++", "reupen@@foobar2000-sdk": "C++", "reupen@@pfc": "C++", "msquared2@@foobar2000-common": "C++", "msquared2@@ui_helpers": "C++", "msquared2@@mmh": "C++", "msquared2@@pfc": "C++", "mumble-voip@@opus": "C", "mumble-voip@@sbcelt": "C", "mumble-voip@@celt-0.11.0": "C", "mumble-voip@@fx11": "C++", "justusc@@FindTBB": "CMake", "runrev@@livecode-ide": "<PERSON><PERSON><PERSON>", "runrev@@livecode-thirdparty": "C", "runrev@@livecode-prebuilt": "Groovy", "pedvide@@ADC": "C++", "adafruit@@Adafruit_TinyUSB_ArduinoCore": "C", "embree@@embree": "C++", "CGAL@@cgal": "C++", "libigl@@tetgen": "C++", "libigl@@CoMISo": "C++", "libigl@@cork": "C", "libigl@@nanogui": "C", "wjakob@@pybind11": "C++", "mosra@@magnum-plugins": "C++", "erikwijmans@@recastnavigation": "C++", "mosra@@magnum-bindings": "C++", "ScottCSteinhauser@@v-hacd": "C++", "alufers@@cpp-reflection": "Go", "cinder@@Cinder": "C++", "ewasm@@hera": "C++", "ethereum@@cpp-dependencies": "CMake", "ethereum@@alethzero": null, "ethereum@@mix": "JavaScript", "ethereum@@solidity": "C++", "ethereum@@webthree": null, "ethereum@@libweb3core": null, "ethereum@@libethereum": null, "ethereum@@webthree-helpers": null, "hfp@@libxsmm": "C", "veluca93@@IQA-optimization": "Python", "Netflix@@vmaf": "Python", "thorfdbg@@difftest_ng": "C++", "google@@brunsli": "C++", "DragonJoker@@Castor3DDeps": null, "DragonJoker@@zlib": "C", "DragonJoker@@Ashes": "C++", "DragonJoker@@ShaderWriter": "C++", "DragonJoker@@FreeType": "C", "DragonJoker@@RenderGraph": "C++", "DragonJoker@@Castor3DTestScenes": "Python", "samadejacobs@@moses": "Python", "snap-stanford@@snap": "C++", "KIwabuchi@@havoqgt": null, "mickem@@lua-protobuf": "Python", "mickem@@json-protobuf": "Python", "mickem@@md-protobuf": "Python", "mickem@@cryptopp": "C++", "mickem@@mongoose-cpp": "C", "mickem@@nscp-docs": "HTML", "paulharris@@miniz": "C", "mickem@@rst-protobuf": "Python", "zeek@@spicy-analyzers": null, "rapidsai@@jitify": "C++", "rapidsai@@rmm": "C++", "rapidsai@@dlpack": "C", "bitbank2@@BitBang_I2C": "C++", "libretro@@libretro-fceumm": "C", "libretro@@Genesis-Plus-GX": "C", "libretro@@snes9x": "C++", "libretro@@stella-libretro": "C++", "libretro@@gambatte-libretro": "C", "libretro@@beetle-pce-fast-libretro": "C", "libretro@@mgba": "C", "libresprite@@duktape": "C", "dacap@@observable": "C++", "aseprite@@gtest": "C++", "aseprite@@duktape": "C", "Bitconch@@bus-rust": null, "eProsima@@IDL-Parser": "Java", "cloudflare@@lua-aho-corasick": "C++", "Syphon@@Syphon-Framework": "Objective-C", "banterle@@piccante": "C++", "jorio@@Quesa": "C++", "freetype@@freetype": "C", "jherico@@OculusSDK": "C++", "OculusRiftInAction@@OculusRiftInActionResources": "GLSL", "OculusRiftInAction@@jocular-examples": "Java", "OculusRiftInAction@@pyovr-examples": "Python", "jherico@@gl": "C++", "jherico@@jocular-examples": "Java", "FreeRTOS@@coreSNTP": "C", "FreeRTOS@@FreeRTOS-Community-Supported-Demos": "C", "FreeRTOS@@FreeRTOS-Partner-Supported-Demos": "C", "FreeRTOS@@Lab-Project-FreeRTOS-Cellular-Library": "C", "PierrePharel@@utf8_decoder": "C", "ArkScript-lang@@std": "Common Lisp", "ArkScript-lang@@modules": "C++", "ArkScript-lang@@String": "C++", "plasma-umass@@coz": "C", "zerotier@@ZeroTierOne": "C++", "joseph-henry@@lwip": "C", "agentzh@@test-nginx": "<PERSON><PERSON>", "simpl-it@@ngx_devel_kit": null, "pierreguillot@@libpd": "Objective-C", "CICM@@CreamLibrary": "C++", "agcooke@@sofie_ros": "Python", "peterrudenko@@JUCE": "C++", "vectorclass@@version2": "C++", "ptrkrysik@@test_data": null, "serge-sans-paille@@frozen": "C++", "skyline-emu@@tz": "C", "KhronosGroup@@Vulkan-Hpp": "C++", "skyline-emu@@vkhpp": "C++", "nullworks@@ucccccp": "C++", "nullworks@@ChIRC": "C++", "nullworks@@co-library": "C++", "leethomason@@MicroPather": "C++", "ValveSoftware@@source-sdk-2013": "C++", "nullifiedcat@@ucccccp": "C++", "nmwsharp@@happly": "C++", "lheric@@libgitlevtbus": "C++", "jkuhlmann@@cgltf": "C", "jessey-git@@fx-gltf": "C++", "laas@@visp_tracker": "C++", "lagadic@@visp_hand2eye_calibration": "C++", "lagadic@@visp_camera_calibration": "C++", "lagadic@@visp_bridge": "C++", "MIRTK@@LBFGS": "C", "MIRTK@@Deformable": "C++", "MIRTK@@Mapping": "C++", "MIRTK@@DrawEM": "C++", "MIRTK@@Scripting": "Python", "ColdGrub1384@@InputAssistant": "Swift", "ColdGrub1384@@TextKit_LineNumbers": "Objective-C", "numpy@@numpy": "Python", "pandas-dev@@pandas": "Python", "matplotlib@@matplotlib": "Python", "lxml@@lxml": "Python", "ColdGrub1384@@libxslt": "HTML", "python-pillow@@Pillow": "Python", "pyca@@pynacl": "C", "nucleic@@kiwi": "C++", "biopython@@biopython": "Python", "PyWavelets@@pywt": "Python", "pyca@@bcrypt": "C", "statsmodels@@statsmodels": "Python", "zeromq@@pyzmq": "Python", "RaRe-Technologies@@gensim": "Python", "wmayner@@pyemd": "C++", "astropy@@astropy": "Python", "scikit-learn@@scikit-learn": "Python", "scikit-image@@scikit-image": "Python", "scipy@@scipy": "Python", "cdave1@@freetype2-ios": "C", "liberfa@@pyerfa": "Python", "python@@typed_ast": "C", "ColdGrub1384@@ios_system": "Objective-C", "chaoslawful@@drizzle-nginx-module": "C", "agentzh@@encrypted-session-nginx-module": "C", "agentzh@@echo-nginx-module": "C", "calio@@form-input-nginx-module": "<PERSON><PERSON>", "agentzh@@headers-more-nginx-module": "C", "ezmobius@@lua-nginx-module": "<PERSON><PERSON>", "agentzh@@memc-nginx-module": "C", "PiotrSikora@@ngx_http_auth_request_module": "C", "agentzh@@rds-json-nginx-module": "C", "agentzh@@redis2-nginx-module": "C", "agentzh@@set-misc-nginx-module": "C", "agentzh@@srcache-nginx-module": "C", "agentzh@@xss-nginx-module": "C", "agentzh@@lua-redis-parser": "C", "agentzh@@chunkin-nginx-module": "C", "agentzh@@array-var-nginx-module": "C", "agentzh@@nginx-eval-module": "C", "masterzen@@nginx-upload-progress-module": "C", "wisk@@ogdf": "C++", "uncrustify@@uncrustify": "C++", "algernon@@libmongo-client": "C", "srz-zumix@@iutest": "C++", "wang-bin@@capi": "C++", "wang-bin@@build_ffmpeg": "Shell", "wang-bin@@libchardet": "C++", "tars-node@@config": "JavaScript", "tars-node@@dyeing": "JavaScript", "tars-node@@logs": "JavaScript", "tars-node@@monitor": "JavaScript", "tars-node@@node-agent": "JavaScript", "tars-node@@notify": "JavaScript", "tars-node@@utils": "JavaScript", "tars-node@@winston-tars": "JavaScript", "tars-node@@deploy": "JavaScript", "tars-node@@registry": "JavaScript", "tars-node@@stream": "HTML", "fabianschuiki@@objectivelua": "JavaScript", "cocos2d-x@@bindings-auto-generated": "C++", "SiggiG@@ProceduralMeshes": null, "vim@@vim": "Vim script", "nestlabs@@nlassert": "C", "nestlabs@@nlfaultinjection": "<PERSON><PERSON><PERSON>", "nestlabs@@nlio": "C++", "nestlabs@@nlunit-test": "<PERSON><PERSON><PERSON>", "jeremyjh@@ESP32_TFT_library": "C", "google@@pigweed": "C++", "openthread@@ot-br-posix": "C++", "bluez@@bluez": "C", "openweave@@cirque": "Python", "Qorvo@@qpg-connectedhomeip": "C", "project-chip@@zap": "ZAP", "SiliconLabs@@sdk_support": "C", "NXP@@plug-and-trust": "C", "openthread@@ot-nxp": "C", "openthread@@ot-qorvo": "C", "openthread@@ot-efr32": "C", "Infineon@@ot-ifx-release": "C", "ARMmbed@@mbed-os": "C", "ARMmbed@@mbed-os-posix-socket": "C++", "ARMmbed@@mbed-os-cypress-capsense-button": "C", "Infineon@@abstraction-rtos": "C", "Infineon@@bluetooth-freertos": "C", "Infineon@@btstack": "C", "Infineon@@clib-support": "C", "Infineon@@connectivity-utilities": "C", "Infineon@@core-lib": "C", "Infineon@@core-make": "<PERSON><PERSON><PERSON>", "Infineon@@kv-store": "C", "Infineon@@mtb-hal-cat1": "C", "Infineon@@mtb-pdl-cat1": "C", "Infineon@@psoc6cm0p": "C", "Infineon@@whd-bsp-integration": "C", "Infineon@@wifi-connection-manager": "C", "Infineon@@wifi-host-driver": "C", "Infineon@@wifi-mw-core": "C", "Infineon@@TARGET_CY8CKIT-062S2-43012": "C", "Infineon@@freertos": "C", "Infineon@@retarget-io": "C", "Infineon@@secure-sockets": "C", "Infineon@@recipe-make-cat1a": "<PERSON><PERSON><PERSON>", "Infineon@@30739A0": "C", "Infineon@@btsdk-include": "C", "Infineon@@TARGET_CYW930739M2EVB-01": "C", "Infineon@@btsdk-tools": "Python", "ATmobica@@mcuboot": "C", "google@@perfetto": "C++", "openweave@@happy": "Python", "ATmobica@@wifi-ism43362": "C++", "openthread@@ot-commissioner": "C++", "cartodb@@mobile-carto-libs": "C++", "google@@cppdap": "C++", "powervr-graphics@@Native_SDK": "C++", "ChrisCummins@@paper-autotuning-opencl-wgsize": "TeX", "ChrisCummins@@paper-towards-collaborative-performance-tuning": "TeX", "ChrisCummins@@paper-synthesizing-benchmarks": "Jupyter Notebook", "ChrisCummins@@paper-end2end-dl": "TeX", "ChrisCummins@@cgo2018": "HTML", "ChrisCummins@@chriscummins.github.io": "JavaScript", "ChrisCummins@@humanorrobot.uk": "JavaScript", "ChrisCummins@@warhaniye.com": "HTML", "ChrisCummins@@interactive-coding-challenges": "Python", "ChrisCummins@@emu": "Python", "jcjohnson@@torch-rnn": "<PERSON><PERSON>", "codeplaysoftware@@visioncpp": "C++", "ChrisCummins@@eevee": "Python", "ChrisCummins@@euclid": "C", "ChrisCummins@@gh-archiver": "Python", "ChrisCummins@@srtime": "Python", "ChrisCummins@@llvm": "C++", "llvm-mirror@@libclc": "C", "ChrisCummins@@torch-rnn": "<PERSON><PERSON>", "ChrisCummins@@CLSmith": "C++", "IfcOpenShell@@files": null, "opensourceBIM@@python-mvdxml": "Python", "simsong@@be20_api": "C++", "simsong@@be13_api": "C++", "simsong@@dfxml": null, "nbeebe@@sceadan": "C++", "gwjjeff@@cryptojs": "JavaScript", "asutton@@origin": "C++", "traviscross@@libzrtp": "C", "davehorton@@sofia-sip": "C", "davehorton@@redisclient": "C++", "easylogging@@easyloggingpp": "C++", "chenxiaolong@@libarchive": "C", "chenxiaolong@@neuteredsaf": "Java", "chenxiaolong@@xz": "C", "chenxiaolong@@lz4": "C", "chenxiaolong@@libaxmlparser": "C++", "openpmix@@prrte": "C", "open-mpi@@hwloc": "C", "espressif@@espmqtt": "C", "256dpi@@esp-mqtt": "C", "kevinbackhouse@@DBusParse": "C++", "kevinbackhouse@@EPollLoop": "C++", "kevinbackhouse@@EPollLoopDBusHandler": "C++", "kerukuro@@digestpp": "C++", "Squalr@@cocos2d-x": "C++", "Squalr@@breakpad": "C++", "Squalr@@AFNetworking": "Objective-C", "Squalr@@Spriter2dX": "C++", "Squalr@@SpriterPlusPlus": "C++", "zcanann@@cocos2d-x": "C++", "teknoman117@@breakpad": "C++", "teknoman117@@AFNetworking": "Objective-C", "mattbucci@@vcpkg": "CMake", "zcanann@@Spriter2dX": "C++", "loki-47-6F-64@@Simple-Web-Server": "C++", "beagleboard@@BeagleBoard-DeviceTrees": "C", "google@@cityhash": "Shell", "eidheim@@Simple-Web-Server": "C++", "spektom@@cpp-subprocess": "C++", "clemahieu@@lmdb": "C", "vinniefalco@@Beast": "C++", "lethal-guitar@@atria": "C++", "tensor-tang@@protobuf": "C++", "unicode-org@@cldr": "Java", "rhvoice@@victoria-ru": null, "rhvoice@@evgeniy-rus": null, "rhvoice@@evgeniy-eng": null, "waywardgeek@@sonic": "C", "rhvoice@@aleksandr-hq-rus": null, "rhvoice@@yuriy-rus": null, "rhvoice@@volodymyr-ukr": null, "rhvoice@@tatiana-rus": null, "rhvoice@@mikhail-rus": null, "rhvoice@@marianna-ukr": null, "rhvoice@@lyubov-eng": null, "rhvoice@@vitaliy-rus": null, "RHVoice@@Polish": null, "RHVoice@@magda-pol": "HTML", "XyrisOS@@liballoc": "C", "panix-os@@liballoc": "C", "CraneStation@@wasi-libc": "C", "chadaustin@@sajson": "C++", "mikeando@@fastjson": "C++", "vivkin@@gason": "C++", "servalproject@@serval-dna": "C", "openmiko@@buildroot_output": null, "openmiko@@ingenic_videocap": "JavaScript", "openmiko@@v4l2rtspserver": "C++", "libsndfile@@libsndfile": "C", "paceholder@@nodeeditor": "C++", "pthom@@hello_imgui": "C", "Dobiasd@@FunctionalPlus": "C++", "juliettef@@imgui_markdown": "C++", "pthom@@ImGuiColorTextEdit": null, "trailofbits@@pe-parse": "C++", "neo-ai@@tvm": "Python", "neo-ai@@treelite": "C++", "llohse@@libnpy": "C++", "google@@grpc": "C++", "freifunk-gluon@@packages": "C", "nuft@@libopencm3": "C", "cvra@@CRC": "C", "cvra@@test-runner": "C++", "cvra@@cmp": "C", "cvra@@packager": "Python", "cvra@@serializer": "C++", "qmk@@uGFX": "C", "redislabs@@raft": "C", "yossigo@@raft": "C", "yossigo@@hiredis": "C", "embeddedartistry@@gdtoa": "C", "embeddedartistry@@meson-buildsystem": "<PERSON><PERSON>", "embeddedartistry@@printf": "C", "percona@@wsrep-API": "C", "Vita3K@@vita-toolchain": "C", "illusionman1212@@nativefiledialog-cmake": "C", "B-Con@@crypto-algorithms": "C", "Vita3K@@SPIRV-Cross": "C++", "Vita3K@@shaders-db": "GLSL", "vitasdk@@vita-headers": "C", "vita3k@@printf": "C++", "ndeadly@@libnx": "C", "github@@cmark": "C", "foonathan@@cppast": "C++", "jgm@@cmark": "C", "wwivbbs@@my_basic": "C", "wwivbbs@@PDCurses": "C", "wwivbbs@@cereal": "C++", "wwivbbs@@cctz": "C++", "wwivbbs@@abseil-cpp": "C++", "jgillis@@hpmpc": "C", "jgillis@@blasfeo": "Assembly", "snopt@@snopt-interface": "Shell", "jgillis@@osqp": "C", "Ultrawipf@@OpenFFBoard-configurator": "Python", "omf2097@@libShadowDive": "C", "cowboyd@@redjs": "<PERSON>", "Microsoft@@DirectXTex": "C++", "GrinPlusPlus@@cuckoo": "C++", "mimblewimble@@secp256k1-zkp": "C", "lordoffox@@ajson": "C++", "ketoo@@lua-intf": "C++", "BitShares@@vendor": "C", "BitShares@@fc": "C++", "BitShares@@web_wallet": "CoffeeScript", "BitShares@@qt_wallet": "C++", "InvictusInnovations@@fc": "C++", "microsoft@@FeaturizersLibrary": "C++", "JDAI-CV@@DNNLibrary": "C++", "horovod@@horovod": "Python", "TheAssemblyArmada@@gamemath": "C", "TheAssemblyArmada@@Baseconfig": "C", "TheAssemblyArmada@@CaptainsLog": "CMake", "zuhd-org@@easyloggingpp": "C++", "arduino@@ArduinoCore-sam": "HTML", "mike-kfed@@BOSSA": null, "adafruit@@Adafruit_ILI9341": "C++", "bazzinotti@@libgme_m": "C++", "bazzinotti@@wla-dx": "C", "cockpit-project@@node-cache": null, "ros@@ros_comm": "Python", "NikolausDemmel@@roscpp_core": null, "ros@@console_bridge": "C++", "ros@@roscpp_core": "C++", "pbek@@FakeVim": "C++", "TheQwertiest@@smp_2003": "JavaScript", "theqwertiest@@scintilla": "C++", "tvanslyke@@timsort-cpp": "C++", "mirror@@scintilla": "C++", "snej@@sqlite3-unicodesn": "C", "couchbaselabs@@BLIP-Cpp": "C++", "couchbasedeps@@civetweb": "C", "couchbaselabs@@couchbase-lite-libcrypto": "C", "couchbaselabs@@forestdb": "C++", "alexzielenski@@ZKSwizzle": "Objective-C", "NSError@@ArgumentParser": "Objective-C", "freewizard@@AppProxyCap": "Objective-C", "clowwindy@@iProxy": "C", "l4u@@NSData-Base64": "Objective-C", "Masonry@@Masonry": "Objective-C", "ekg@@bamtools": "C++", "ekg@@vcflib": "C++", "microsoft@@krabsetw": "C++", "Yara-Rules@@rules": "YARA", "Kogia-sima@@cppglob": "C++", "litehtml@@litehtml": "C", "maddinat0r@@samp-log-core": "C", "arobenko@@cmake_extra": null, "openpst@@gui-common": "C++", "verilator@@verilator": "C++", "pulp-platform@@cva6": null, "nomolk@@M5Stack_CrackScreen": "C++", "pcelli85@@M5Stack_FlappyBird_game": "<PERSON><PERSON><PERSON><PERSON>", "neoxharsh@@Pixel-Fun-M5Stack": "C++", "tobozo@@M5Stack-Pacman-JoyPSP": "C", "tobozo@@M5Stack-Rickroll": "C", "dsiberia9s@@SpaceDefense-m5stack": "C", "PartsandCircuits@@M5Stack-SpaceShooter": "C++", "robo8080@@M5Stack_Sokoban": "Objective-C", "tobozo@@M5Stack-NyanCat": "C", "botofancalin@@M5Stack-ESP32-Oscilloscope": "C++", "tobozo@@M5Stack-PacketMonitor": "C++", "dsiberia9s@@mp3-player-m5stack": "C", "PartsandCircuits@@M5Stack-Tetris": "C", "NibblesLab@@d_invader": "C", "xisai@@M5Stack_NyanCat": "C", "hkoffer@@M5Stack-Thermal-Camera-": "C++", "tobozo@@M5Tube": "C++", "m4k3r-net@@M5Stack-MegaChess": "C++", "tobozo@@M5Stack-Raytracer": "C", "anton-b@@M5Stack_ESP32_radio": "C++", "drayde@@nixietubeM5": "C", "robo8080@@M5Stack_WebRadio_Avator": "C++", "PartsandCircuits@@M5Stack-WiFiScanner": "C++", "TTtensan@@M5StackMiniOthello": "C++", "RJPlog@@M5Stack-CrazyAsteroids": "C++", "FromF@@M5Stack_DigitalClock_NTP": "C++", "vcraftjp@@M5Stack_Particle_demo": "C++", "micutil@@SetWiFi_Mic": "C++", "kumaashi@@M5StackSandbox": "C", "lovyan03@@M5Stack_LovyanToyBox": "C++", "pappani@@Frogger_M5Stack": "C++", "mongonta0716@@M5Stack_Test_tools": "C++", "Kongduino@@M5_LoRa_Frequency_Hopping": "C++", "ar90n@@msgpack11": "C++", "misson20000@@msgpack11": "C++", "giordi91@@spdlog": "C++", "giordi91@@imgui": "C++", "giordi91@@cxxopts": "C++", "giordi91@@tinyobjloader": "C++", "giordi91@@json": "C++", "giordi91@@Catch2": "C++", "giordi91@@DirectXTK12": "C++", "giordi91@@Compressonator": "C++", "giordi91@@DirectXTex": "C++", "giordi91@@farmhash": "C++", "giordi91@@display-library": "HTML", "giordi91@@PixEvent": "C++", "giordi91@@crunch": "C++", "giordi91@@lua": "C", "giordi91@@glslang": "C++", "giordi91@@SPIRV-Cross": null, "alfredh@@rew": "C", "alfredhz@@re": "C", "TiltedPhoques@@TiltedReverse": "C++", "TiltedPhoques@@TiltedUI": "C++", "TiltedPhoques@@TiltedConnect": "C++", "TiltedPhoques@@TiltedHooks": "C++", "TiltedPhoques@@TiltedScript": "C++", "TiltedPhoques@@TiltedCore": "C++", "TiltedPhoques@@entt": "C++", "Foxtacles@@iniparser": "C", "andlabs@@libui": "C", "ThePBone@@EELEditor": "C++", "ThePBone@@GraphicEQWidget": "C++", "ThePBone@@FlatTabWidget": "C++", "MonaSolutions@@MonaJS": "JavaScript", "libMesh@@libmesh": "C", "StarEngine@@Box2D": "C", "StarEngine@@SDL2": "C", "StarEngine@@protobuf": "C++", "david-m-rosen@@Optimization": "C++", "rezaali@@ofxUI": "C++", "satoruhiga@@ofxInteractivePrimitives": "C++", "YCAMInterlab@@ofxNodeArray": "C", "motoishmz@@ofxBt": "C++", "julapy@@ofxQuadWarp": "C++", "obviousjim@@ofxDelaunay": "C++", "kylemcdonald@@ofxDmx": "C++", "YoshitoONISHI@@ofxEvent": "C", "YoshitoONISHI@@ofxException": "C++", "satoruhiga@@ofxBt": "C++", "davidsiaw@@lua": "C", "aros-translation-team@@muimaster.library": "C", "aros-translation-team@@acpitool": "C", "aros-translation-team@@dos.library": null, "aros-translation-team@@sfsdefrag": "C", "aros-translation-team@@pcitool": "C", "aros-translation-team@@security.library": null, "aros-translation-team@@trident": "C", "aros-translation-team@@loadresource": "C", "aros-translation-team@@r": "C", "aros-translation-team@@aboutwindow": "C", "aros-translation-team@@prefswindow": "C", "aros-translation-team@@systemprefswindow": "C", "aros-translation-team@@diskimage.device": null, "aros-translation-team@@diskimage-zune": null, "aros-translation-team@@asl.library": "C", "aros-translation-team@@identify.library": "C", "aros-translation-team@@identify": "C", "aros-translation-team@@reqtools.library": "C", "aros-translation-team@@ahi-prefs": null, "aros-translation-team@@ahi.device": null, "aros-translation-team@@multiview": "C", "aros-translation-team@@more": "C", "aros-translation-team@@installer": "C", "aros-translation-team@@clock": "C", "aros-translation-team@@wanderer": "C", "aros-translation-team@@sysexplorer": "C", "aros-translation-team@@aboutaros": "C", "aros-translation-team@@installaros": "C", "aros-translation-team@@hdtoolbox": "C", "aros-translation-team@@boingiconbar": "C", "aros-translation-team@@sysmon": "C", "aros-translation-team@@commodities": "C", "aros-translation-team@@palette-prefs": "C", "agordon@@posix-libc-examples": "C", "aros-translation-team@@appearance-prefs": "C", "aros-translation-team@@boingiconbar-prefs": "C", "aros-translation-team@@font-prefs": "C", "aros-translation-team@@input-prefs": "C", "aros-translation-team@@locale-prefs": "C", "aros-translation-team@@reqtools-prefs": "C", "aros-translation-team@@boot-prefs": "C", "aros-translation-team@@icontrol-prefs": "C", "aros-translation-team@@network-prefs": "C", "aros-translation-team@@pointer-prefs": "C", "aros-translation-team@@printer-prefs": "C", "aros-translation-team@@editor-prefs": "C", "aros-translation-team@@psi-prefs": "C", "aros-translation-team@@screenmode-prefs": "C", "aros-translation-team@@serial-prefs": "C", "aros-translation-team@@time-prefs": "C", "aros-translation-team@@trackdisk-prefs": "C", "aros-translation-team@@wanderer-prefs": "C", "aros-translation-team@@zune-prefs": "C", "aros-translation-team@@miamipanel": "C", "aros-translation-team@@format": "C", "aros-translation-team@@find": "C", "aros-translation-team@@ftmanager": "C", "aros-translation-team@@snoopy": "C", "aros-translation-team@@diskinfo": "C", "aros-translation-team@@executecommand": "C", "aros-translation-team@@executestartup": "C", "aros-translation-team@@wbnewdrawer": "C", "aros-translation-team@@wbrename": "C", "aros-translation-team@@editor": "C", "aros-translation-team@@keyshow": "C", "aros-translation-team@@screengrabber": "C", "aros-translation-team@@wimp": "C", "aros-development-team@@datatype-test-binaries": null, "MegaGlest@@megaglest-data": "GLSL", "MegaGlest@@megaglest-masterserver": "PHP", "c3d@@recorder": "C", "c3d@@build": "<PERSON><PERSON><PERSON>", "Chia-Network@@bls-signatures": "C++", "Chia-Network@@chiapos": "HTML", "calccrypto@@uint256_t": "C++", "dcdpr@@libbech32": "C++", "madMAx43v3r@@vnx-addons": "C++", "automyinc@@basic-opencl": "C++", "madMAx43v3r@@vnx-base": "C++", "visionmedia@@expresso": "JavaScript", "visionmedia@@express": "JavaScript", "littlefs-project@@littlefs-fuse": "C", "boost-ext@@sml": "C++", "adamgreen@@CrashDebug": "C++", "mudita@@utz": "C", "rafagafe@@base64": "C", "mudita@@hash-library": null, "mudita@@libical": null, "mudita@@dr_libs": "C", "digint@@tinyfsm": "C++", "msftguy@@syringe": "C", "Matazure@@benchmark": "C++", "Matazure@@googletest": "C++", "Matazure@@stb": "C", "tuttleofx@@sconsProject": "Python", "hyperion-project@@rpi_ws281x": "C", "hyperion-project@@protobuf": "C++", "an-tao@@drogon": "C++", "aseprite@@stringencoders": "Objective-C", "diharaw@@dwSampleFramework": "C", "edgelesssys@@ertgo": "Go", "edgelesssys@@ttls": "C++", "erikd@@libsndfile": "C", "dechamps@@cpputil": "C++", "dechamps@@cpplog": "C++", "dechamps@@ASIOUtil": "C++", "dechamps@@ASIOTest": "C++", "vurtun@@nuklear": "C", "robert-strandh@@SICL": "Common Lisp", "clasp-developers@@asdf": "Common Lisp", "Ravenbrook@@mps": "C", "robert-strandh@@Acclimation": "Common Lisp", "Bike@@SICL": "Common Lisp", "clasp-developers@@SICL": "Common Lisp", "ivmai@@libatomic_ops": "C", "ivmai@@bdwgc": "C", "Ravenbrook@@mps-temporary": "C", "drmeister@@SICL": "Common Lisp", "arrayfire@@arrayfire_data": null, "arrayfire@@assets": null, "alltheflops@@threads": "C++", "arrayfire@@forge": "C++", "Levi-Armstrong@@bullet3": "C++", "flexible-collision-library@@fcl": "C++", "SaurikIT@@SDURLCache": "Objective-C", "crawl@@crawl-sqlite": "C", "crawl@@crawl-lua": "C", "crawl@@crawl-luajit": "C", "crawl@@crawl-pcre": "C", "crawl@@crawl-freetype": "C", "crawl@@crawl-libpng": "C", "crawl@@crawl-zlib": "C", "crawl@@crawl-fonts": null, "crawl@@crawl-sdl2": "C", "crawl@@crawl-sdl2-image": "C", "reddit@@obs-browser-plugin": "C++", "sieren@@arduino-esp32": "C", "sieren@@TFT_eFEX": "C++", "sieren@@JPEGDecoder": "C", "vstakhov@@rspamd-interface": "JavaScript", "vstakhov@@doxydown": "<PERSON><PERSON>", "vstakhov@@snowball": "C", "vstakhov@@librdns": "C", "vstakhov@@libucl": "C", "iodcpp@@symbol": "C++", "iodcpp@@metamap": "C++", "rafael-santiago@@cute": "C", "mapsme@@osmctools": "C", "mapsme@@kothic": "Python", "mapsme@@Alohalytics": "C++", "aurelien-rainone@@macdeployqtfix": "Python", "mapsme@@protobuf": "C++", "mapsme@@twine": "<PERSON>", "mapsme@@just_gtfs": "C++", "hayguen@@greenffts": "C", "hayguen@@kissfft": "C", "btx000@@lv_arduino": "C", "sifive@@example-hello": "<PERSON><PERSON><PERSON>", "sifive@@example-itim": "C", "sifive@@example-software-interrupt": "C", "sifive@@example-timer-interrupt": "C", "sifive@@example-local-interrupt": "C", "sifive@@example-return-fail": "<PERSON><PERSON><PERSON>", "sifive@@example-return-pass": "<PERSON><PERSON><PERSON>", "sifive@@example-pmp": "C", "sifive@@example-spi": "C", "sifive@@example-empty": "<PERSON><PERSON><PERSON>", "sifive@@sifive-welcome": "C", "sifive@@benchmark-dhrystone": "C", "sifive@@example-multicore-hello": "C", "sifive@@example-user-mode": "C", "sifive@@example-user-syscall": "C", "sifive@@benchmark-coremark": "C", "sifive@@test-coreip": "C", "sifive@@example-rtc": "C", "sifive@@example-watchdog": "C", "sifive@@example-cflush": "C", "sifive@@example-plic-interrupts": "C", "sifive@@example-clic-selective-vector-interrupts": "C", "sifive@@example-clic-hardware-vector-interrupts": "C", "sifive@@example-local-vector-interrupts": "C", "sifive@@example-csr-access": "C", "sifive@@example-minimal-boot": "C", "sifive@@example-uart-interrupt": "C", "sifive@@example-atomics": "C", "sifive@@example-i2c": "C", "sifive@@example-pwm": "C", "sifive@@example-clic-nested-interrupts": "C", "sifive@@benchmark-mem-latency": "C", "sifive@@example-hpm": "C", "sifive@@Segger_SystemView-metal": "C", "sifive@@example-freertos-blinky": "C", "sifive@@example-freertos-minimal": "C", "sifive@@example-freertos-blinky-systemview": "C", "sifive@@example-clic-nonvector-interrupts": "C", "sifive@@example-freertos-pmp-blinky": "C", "sifive@@example-buserror": "C", "sifive@@example-hca-metal": "C", "sifive@@openocd": "C", "vkhatri@@chef-alternatives": "<PERSON>", "chef-cookbooks@@ark": "<PERSON>", "chef-cookbooks@@build-essential": "<PERSON>", "chef-cookbooks@@git": "<PERSON>", "chef-cookbooks@@homebrew": "<PERSON>", "chef-cookbooks@@mingw": "C", "windowschefcookbooks@@seven_zip": "<PERSON>", "chef-cookbooks@@tar": "<PERSON>", "chef-cookbooks@@windows": "<PERSON>", "pixie-labs@@cpplint": "Python", "pixie-labs@@aes-min": null, "pixie-labs@@arrow": "C++", "pixie-labs@@bcc": "Python", "pixie-labs@@bpftrace": "C++", "pixie-labs@@dnsparser": "C++", "pixie-labs@@kuberesolver": "Go", "pixie-labs@@libpypa": "C++", "pixie-labs@@tdigest": "C++", "pixie-labs@@threadstacks": "C++", "pixie-labs@@ELFIO": "C++", "Qucs@@ADMS": "<PERSON><PERSON>", "CUBRID@@cubrid-manager-server": "C++", "ezEngine@@content": null, "ezEngine@@precompiled-tools": "CMake", "ezEngine@@thirdparty-fmod": "C", "m-labs@@lm32": "Verilog", "openrisc@@mor1kx": "Verilog", "cliffordwolf@@picorv32": "Verilog", "enjoy-digital@@tapcfg": "C", "enjoy-digital@@VexRiscv-verilog": "Verilog", "lambdaconcept@@minerva": "Python", "enjoy-digital@@rocket-litex-verilog": "Verilog", "mbrcic@@cpu_features": null, "retr0rangepi@@BashTool_ROPI_RCA": "Shell", "prusa3d@@TMCStepper": "C++", "prusa3d@@Marlin": null, "ComparativeGenomicsToolkit@@pinchesAndCacti": "C", "ComparativeGenomicsToolkit@@matchingAndOrdering": "C", "ComparativeGenomicsToolkit@@cPecan": "C", "ComparativeGenomicsToolkit@@sonLib": "C", "ComparativeGenomicsToolkit@@hal": "C++", "ComparativeGenomicsToolkit@@cactus2hal": "C++", "ComparativeGenomicsToolkit@@kyoto": "C++", "ComparativeGenomicsToolkit@@abPOA": "C", "diekhans@@pinchesAndCacti": "C", "diekhans@@matchingAndOrdering": "C", "diekhans@@cPecan": "C", "diekhans@@sonLib": "C", "diekhans@@hal": "C++", "benedictpaten@@cPecan": "C", "UCSCReconGroup@@hdf5": "C", "glennhickey@@hal": "C++", "adderan@@cactus2hal": "C++", "junrrein@@PDF-Writer": "C", "larsgottesbueren@@WHFC": "C++", "bingmann@@sqlplot-tools": "C++", "bengardner@@uncrustify": "C++", "GodotNativeTools@@godot-cpp": "C++", "AaltoML@@mobile-cv-suite": "Shell", "reswitched@@newlib": "C", "eriksl@@crosstool-NG": "Shell", "eriksl@@lwip-for-esp8266-nonos-sdk": "C", "someburner@@crosstool-ng": "Shell", "STMicroelectronics@@lsm6dsm": "C", "STMicroelectronics@@lsm6dso32": "C", "STMicroelectronics@@lsm6dsox": "C", "STMicroelectronics@@lsm6dso": "C", "STMicroelectronics@@lsm6dso32x": "C", "STMicroelectronics@@lsm6dsrx": "C", "STMicroelectronics@@lsm6dsr": "C", "STMicroelectronics@@lsm9ds1": "C", "STMicroelectronics@@stts22h": "CSS", "STMicroelectronics@@stts751": "CSS", "chronoxor@@CppCommon": "C++", "red-eclipse@@acerspyro": null, "red-eclipse@@actors": null, "red-eclipse@@appleflap": null, "red-eclipse@@blendbrush": null, "red-eclipse@@caustics": null, "red-eclipse@@crosshairs": null, "red-eclipse@@decals": null, "red-eclipse@@dziq": null, "red-eclipse@@elyvisions": null, "red-eclipse@@fonts": null, "red-eclipse@@freezurbern": null, "red-eclipse@@john": null, "red-eclipse@@jojo": null, "red-eclipse@@jwin": null, "red-eclipse@@luckystrike": null, "red-eclipse@@maps": null, "red-eclipse@@mayhem": null, "red-eclipse@@mikeplus64": null, "red-eclipse@@misc": null, "red-eclipse@@nieb": null, "red-eclipse@@nobiax": null, "red-eclipse@@particles": null, "red-eclipse@@philipk": null, "red-eclipse@@projectiles": null, "red-eclipse@@props": null, "red-eclipse@@skyboxes": null, "red-eclipse@@snipergoth": null, "red-eclipse@@sounds": null, "red-eclipse@@textures": null, "red-eclipse@@torley": null, "red-eclipse@@trak": null, "red-eclipse@@ulukai": null, "red-eclipse@@unnamed": null, "red-eclipse@@vanities": null, "red-eclipse@@vegetation": null, "red-eclipse@@weapons": null, "ClickHouse-Extras@@libc-headers": "C", "ClickHouse-Extras@@antlr4-runtime": "C++", "kthohr@@gcem": "C++", "kthohr@@stats": "C++", "uber@@h3": "C", "ClickHouse-Extras@@simdjson": "C++", "apache@@orc": "HTML", "cyrusimap@@cyrus-sasl": "C", "powturbo@@Turbo-Base64": "C", "ClickHouse-Extras@@cppkafka": "C++", "ClickHouse-Extras@@openssl": "C", "ClickHouse-Extras@@ryu": "C++", "openldap@@openldap": "C", "aklomp@@base64": "C", "ClickHouse-Extras@@boost-extra": "C++", "greenplum-db@@pgbouncer": "C", "greenplum-db@@postgis": "C", "greenplum-db@@gpfdist-ext": "<PERSON><PERSON><PERSON>", "greenplum-db@@gpclients": "C", "greenplum-db@@plr": "C", "bk138@@wxservdisc": "C", "GlPortal@@vhacd-lib": "C++", "GlPortal@@serine": "C++", "GlPortal@@cpp-linenoise": "C++", "GlPortal@@RadixEntity": "C++", "djowel@@nanovg": "C", "bkeevil@@esp32-camera": null, "ivanarh@@libunwind-ndk": "C", "ivanarh@@libunwindstack-ndk": "C++", "Seagate@@opensea-common": "C", "Seagate@@opensea-transport": "C", "Seagate@@opensea-operations": "C", "bitcoin-core@@leveldb-old": "C++", "ShroomKing@@libcross2d": "C++", "Cpasjuste@@mpv": "C", "scanlime@@libusbx": "C", "scanlime@@libwebsockets": "C", "columbia@@egalito-tests": "C", "columbia@@egalito-capstone": "PHP", "columbia@@egalito-docs": "C++", "Luthaf@@Chemharp-Tests": "Arc", "Luthaf@@chrp": "C++", "Luthaf@@Chemharp.jl": "<PERSON>", "Iyengar111@@NanoLog": "C++", "KjellKod@@g3log": "C++", "dkfans@@peresec": "C", "randy408@@libspng": "C", "guruz@@qtmacgoodies": "Objective-C++", "owncloud@@owncloud-client-binary": null, "owncloud@@zsync": "C", "owncloud@@documentation": "CSS", "shadone@@qtmacgoodies": null, "oceancx@@glm": "C++", "oceancx@@asio": "C++", "oceancx@@NESupport": "C++", "oceancx@@nlohmann": "C++", "oceancx@@imgui": "C++", "oceancx@@luacjson-cmake": "C", "oceancx@@miniaudio-cmake": "C", "oceancx@@nanovg": "C", "oceancx@@ejoy2d": "C", "oceancx@@luadbg": "C++", "madewokherd@@mono": "C#", "madewokherd@@mono-basic": "Visual Basic .NET", "madewokherd@@FNA": "C#", "madewokherd@@FNA.NetStub": "C#", "madewokherd@@winforms": "C#", "madewokherd@@winforms-datavisualization": "C#", "madewokherd@@wpf": "C#", "madewokherd@@monoDX": "C#", "FNA-XNA@@SDL_image_compact": "C", "lkl@@linux": "C", "Kangz@@SPIRV-Cross": "C++", "Armada651@@minhook": "C", "LibreVR@@ext-win-glew": "C", "tianocore@@edk2-libc": "Python", "polymonster@@premake-android-studio": "<PERSON><PERSON>", "polymonster@@pmfx-shader": "Python", "polymonster@@jsn": "Python", "francescmm@@QLogger": "C++", "cocos-creator@@cocos2d-console": "Python", "seanbaxter@@RayTracingInVulkan": "C++", "seanbaxter@@mgpu-shaders": "C++", "allisonvacanti@@thrust": "C++", "azawadzki@@base-n": "C++", "microsoft@@arcana.cpp": "C++", "BabylonJS@@bgfx.cmake": "CMake", "google-ar@@arcore-android-sdk": "C", "microsoft@@OpenXR-MixedReality": "HTML", "datacratic@@baseimage-docker": "Python", "mldbai@@s2-geometry-library": "C++", "horsicq@@XProcess": "C++", "sgorsten@@linalg": "C++", "pagespeed@@mod_pagespeed": "C++", "mikalhart@@TinyGPSPlus": "C++", "lewisxhe@@AXP202X_Library": "C++", "lewisxhe@@Button2": "C++", "macmade@@gmock-xcode": "C++", "SymbiFlow@@prjxray-db": "Shell", "joaoleal@@CppADCodeGen": "C++", "EmbersArc@@socp_interface": "C++", "paullouisageneau@@plog": "C++", "cfig@@android_image_res": "Python", "ethz-asl@@rotors": "C++", "ethz-asl@@volumetric_mapping": "C++", "ethz-asl@@minkindr_ros": "C++", "ethz-asl@@minkindr": "C++", "ethz-asl@@mav_comm": "C++", "ethz-asl@@glog_catkin": "CMake", "ethz-asl@@gflags_catkin": "CMake", "ethz-asl@@eigen_catkin": "CMake", "ethz-asl@@eigen_checks": "C++", "Vitallium@@qtbase": "C++", "Vitallium@@qtwebkit": "C++", "felix-lang@@fbuild": "Python", "felix-lang@@dypgen": "OCaml", "OSGeo@@gdal": "C++", "google@@jsonnet": "Jsonnet", "AlloSphere-Research-Group@@GLV": "C++", "ProgHQ@@m-stack": "C", "KhronosGroup@@Vulkan-ValidationLayers": "C++", "mateidavid@@zstr": "C++", "mikaelpatel@@Cosa-Shell": "C++", "mikaelpatel@@Cosa-SNMP": "C++", "mikaelpatel@@Cosa-UML": "C++", "mikaelpatel@@Cosa-MQTT": "C++", "mikaelpatel@@Cosa-ThingSpeak": "C++", "mikaelpatel@@Cosa-ProtocolBuffer": "C++", "mikaelpatel@@Cosa-AVR": "C++", "mikaelpatel@@Cosa-NEXA": "C++", "mikaelpatel@@Cosa-Telnet": "C++", "mikaelpatel@@Cosa-ICMP": "C++", "mikaelpatel@@Cosa-HTTP": "C++", "JoshuaBrookover@@bgfx.cmake": "CMake", "biojppm@@cmake": "CMake", "NSchertler@@nanogui": "C++", "NSchertler@@NSEssentials": "C++", "MIPT-ILab@@mips-traces": "Assembly", "bminor@@binutils-gdb": "C", "qnzhou@@carve": "C++", "qnzhou@@cork": "C", "qnzhou@@tetgen": "C++", "qnzhou@@triangle": "C", "qnzhou@@qhull": "C", "qnzhou@@Clipper": "C++", "qnzhou@@eigen": "C++", "qnzhou@@quartet": "C++", "qnzhou@@cgal": "C++", "qnzhou@@pybind11": "C++", "qnzhou@@mmg": "C", "qnzhou@@geogram": "C++", "qnzhou@@draco": "C++", "secdev@@scapy": "Python", "WerWolv@@libTesla": "C", "tallbl0nde@@Aether": "C++", "tallbl0nde@@splash": "C++", "avaneev@@avir": "C++", "alainmarcel@@UHDM": "C++", "gamerslab@@TMXParser": "C++", "gamerslab@@Simple-OpenGL-Image-Library": "C", "log4cplus@@ThreadPool": "C++", "bhuman@@CompiledNN": "C++", "DFHack@@stonesense": "C++", "DFHack@@isoworld": "C++", "DFHack@@df-structures": "<PERSON><PERSON>", "DFHack@@clsocket": "C++", "DFHack@@DF2MC": "C++", "DFHack@@lethosor-scripts": null, "DFHack@@roses-scripts": null, "DFHack@@maxthyme-scripts": null, "DFHack@@dscorbett-scripts": null, "DFHack@@kane-t-scripts": null, "peterix@@stonesense": "C++", "peterix@@isoworld": "C++", "peterix@@DF2MC": "C++", "peterix@@df-structures": "<PERSON><PERSON>", "yuki-koyama@@parallel-util": "C++", "yuki-koyama@@timer": "C++", "apiaryio@@dtl": "C++", "apiaryio@@sos": "C++", "apiaryio@@snowcrash": "C++", "navganti@@caffe-segnet-cudnn7": "C++", "ggerganov@@ggsock": "C++", "ggerganov@@imtui": "C++", "alex-shpak@@hugo-book": "HTML", "contrem@@arduino-timer": "C++", "ThingPulse@@esp8266-oled-ssd1306": "C", "adafruit@@Adafruit_BMP085_Unified": "C++", "adafruit@@Adafruit_BMP280_Library": "C++", "luciansabo@@GP2YDustSensor": "C++", "miguel5612@@MQSensorsLib": "C++", "FastLED@@FastLED": "C++", "bakercp@@CRC32": "<PERSON><PERSON><PERSON><PERSON>", "jgromes@@RadioLib": "C++", "Project-Owl@@Crypto": "C++", "Project-Owl@@RadioLib": "C++", "RedisLabsModules@@cpython": "Python", "wxWidgets@@Catch": "C++", "wxWidgets@@zlib": "C", "wxWidgets@@libpng": "C", "wxWidgets@@libexpat": "C", "wxWidgets@@libtiff": "C", "wxWidgets@@libjpeg-turbo": "C", "wxWidgets@@pcre": null, "alltheworld@@tp4056": null, "BenHanson@@lexertl": "C++", "c9s@@r3": "C", "PazerOP@@implot": "C++", "tenbaht@@stm8gal": "C", "travisdowns@@libpfc": "C", "ilmola@@gml": "C++", "AudioProcessingFramework@@apf": "C++", "grrrr@@flext": "C++", "jljusten@@LZMA-SDK": "C++", "alef78@@lzoma": "C", "Blosc@@c-blosc2": "C", "skal65535@@fsc": "C", "inikep@@lz5": "C", "CoreSecurity@@pysap": "Python", "andikleen@@snappy-c": "C", "nemequ@@tornado": "C++", "ivan-tkatchev@@yalz77": "C++", "Ed-von-Schleck@@shoco": "C", "powturbo@@TurboRLE": "C", "ebiggers@@xpack": "C", "jkbonfield@@rans_static": "C", "tyoma@@wpl": "C++", "tyoma@@easy-addin": "C++", "tyoma@@agge": "C++", "tyoma@@utee": "C++", "grey-narn@@hera": "C++", "microsoft@@react-native-windows": "C++", "Microsoft@@react-native-windows": "C++", "ReactWindows@@react-native-windows": "C++", "NTAP@@warpcore": "C", "private-octopus@@picoquic": "C", "larseggert@@timeout": "C", "CANopenNode@@CANopenNode": "C", "GodotNativeTools@@godot_headers": "C", "OpenHMD@@hidapi": "C", "xxsds@@sdsl-lite": "C++", "gkdr@@axc": "C", "fltk@@test-only": "C++", "smasherprog@@Openssl_VS": "C", "NEstelami@@ZAP2": "C++", "xvortex@@ps4-payload-sdk": "C", "nanoporetech@@read_until_api": "Python", "remy@@polyfills": "JavaScript", "marijnh@@CodeMirror": "JavaScript", "machinezone@@ixwebsocket": "C++", "lv2-porting-project@@JUCE": "C++", "Chowdhury-DSP@@DISTRHO-JUCE": null, "3gguan@@stub": "C", "lief-project@@LIEF": "C++", "gdabah@@distormx": "C", "kubo@@plthook": "C", "avast@@pelib": "C++", "dyninst@@dyninst": "C", "RedisDesktop@@CrashReporter": "C++", "sijk@@qt-unix-signals": "C++", "benlau@@asyncfuture": "C++", "ajaxorg@@ace-builds": "JavaScript", "uglide@@QtConsole": "C++", "vbpf@@ebpf-samples": "C", "ytakano@@radix_tree": "C++", "seahorn@@crab": "C++", "ucb-bar@@rocket-chip": "Scala", "u-boot@@u-boot": "C", "alexforencich@@verilog-ethernet": "Verilog", "sifive@@block-inclusivecache-sifive": "Scala", "ucb-bar@@testchipip": "Scala", "riscv-boom@@riscv-boom": "Scala", "ucb-bar@@gemmini": "Scala", "riscv@@riscv-pk": "C", "libimobiledevice@@libimobiledevice": "C", "libimobiledevice@@libplist": "C", "libimobiledevice@@libusbmuxd": "C", "libimobiledevice@@ideviceinstaller": "C", "NyaMisty@@minimalupnpc": "C", "BLAKE3-team@@BLAKE3": "Assembly", "madMAx43v3r@@bls-signatures": "C++", "stinb@@libssh2": "C", "Zilliqa@@g3log": "C++", "Zilliqa@@schnorr": "C++", "imneme@@pcg-cpp": "C++", "svaarala@@duktape": "JavaScript", "neonious@@open62541": "C", "ned14@@hugo-theme-docdock": "JavaScript", "ned14@@boostdoc": "CSS", "vjeantet@@hugo-theme-docdock": "JavaScript", "ned14@@status-code": "C++", "ned14@@Boost.Expected": "C++", "inet-framework@@inet-tutorials": "JavaScript", "rinigus@@geocoder-nlp": "C++", "LuminosoInsight@@langcodes": "Python", "rinigus@@mapnik-styles-sqlite": "CartoCSS", "alvarotrigo@@fullPage.js": "JavaScript", "rinigus@@osmscout-server-route": "C++", "rapidsai@@cuml": "<PERSON><PERSON>", "aminnj@@cpptqdm": "C++", "Funatiq@@gossip": "C++", "leo-yuriev@@libmdbx": "C", "Softmotions@@ejdb": "C", "lmdb@@lmdb": "C", "BohuTANG@@nessDB": "C", "ReOpen@@libmdbx": "C", "particle-iot@@mbedtls": "C", "fgenesis@@minihttp": "C++", "XadillaX@@efsw": "C++", "spark@@node-mbed-dtls-client": "C++", "jdupuy@@dj_opengl": "C", "xhawk18@@promise-cpp": "C++", "recp@@json": "C", "recp@@xml": "C", "recp@@sample-models": null, "recp@@libds": "C", "datoviz@@imgui": "C++", "viskydev@@imgui": "C++", "KhronosGroup@@SPIRV-LLVM-Translator": "LLVM", "kpet@@SPIRV-LLVM-Translator": "C++", "KhronosGroup@@OpenCL-CTS": "C++", "particle-iot@@firmware-protobuf": "JavaScript", "particle-iot@@nrf5_sdk": "C", "particle-iot@@lwip": "C", "particle-iot@@lwip-contrib": "C", "particle-iot@@freertos": "C", "particle-iot@@nanopb": "C", "particle-iot@@littlefs": "C", "particle-iot@@ioLibrary_Driver": "C", "particle-iot@@miniz": "C", "particle-iot@@gsm0710muxer": "C++", "particle-iot@@Catch2": "C++", "particle-iot@@openthread": "C++", "vasi@@squashfuse": "C", "NativeScript@@android-metadata-generator": null, "TehPsychedelic@@SimpleIniParser": "<PERSON><PERSON><PERSON>", "tallbl0nde@@NX-Activity-Log-Translations": null, "v0lt@@BaseClasses": "C++", "Chowdhury-DSP@@foleys_gui_magic": null, "jatinchowdhury18@@JUCE": "C++", "highperformancecoder@@ecolab": "C++", "djarek@@certify": "C++", "highperformancecoder@@RavelCAPI": "C++", "msorvig@@iot-sensortag": "C++", "msorvig@@slate": "C++", "desktop-app@@mallocng": "C", "desktop-app@@materialdecoration": "C++", "desktop-app@@qt5ct": "C++", "desktop-app@@variant": null, "jrl-umi3218@@jrl-travis": "Shell", "lvgl@@lv_drivers": "C", "littlevgl@@hw": "C", "VSChina@@ESP32_AzureIoT_Arduino": "C", "esp8266@@Arduino": "C++", "ekalinin@@github-markdown-toc": "Shell", "brightdigit@@homebrew-speculid": "<PERSON>", "wolfSSL@@wolfssljni": "Java", "wolfssl@@wolfssl": "C", "wolfssl@@wolfcrypt-jni": "Java", "jtilly@@inih": "C++", "Spritetm@@elfload": "C", "paladin-t@@my_basic": "C", "PocketSprite@@UGUI": "C", "potswa@@cxx_function": "C++", "kmilo17pet@@quarkts-usermanual": "TeX", "TECREA@@QuarkTS": "C", "wbenny@@pdbex": "C++", "tgRobotics@@apriltag": "C", "microsoft@@libHttpClient": "C++", "jasonsandlin@@cpprestsdk": "C++", "Microsoft@@cppwinrt": "C++", "martinmoene@@lest": "C++", "fwsGonzo@@sqlite3_amalgamation": "C", "AnnikaH@@uzlib": "C", "fwsGonzo@@libsl3": "C", "fwsGonzo@@rapidjson": "C++", "DaemonEngine@@breakpad": "C++", "DaemonEngine@@crunch": "C++", "Unvanquished@@breakpad": "C++", "AlexAltea@@orbital-qemu": "C", "AlexAltea@@orbital-grub": "C", "Manu343726@@boost-cmake": "C++", "flexferrum@@value-ptr-lite": "C++", "clostra@@libutp": "C++", "mackyle@@blocksruntime": "C", "clostra@@dht": "C", "clostra@@bugsnag-cocoa": "Objective-C", "clostra@@libevent": "C", "clostra@@Libevent": "C", "wmcbrine@@PDCurses": "C", "miguelmartin75@@Wink-Signals": "C++", "seizu@@base128": "C++", "momo5502@@Wink-Signals": "C++", "libvmi@@libvmi": "C", "tklengyel@@libxdc": "C", "macmade@@SeriousCode": "C", "organicmaps@@osmctools": "C", "organicmaps@@kothic": "Python", "organicmaps@@protobuf": null, "organicmaps@@twine": "<PERSON>", "organicmaps@@just_gtfs": "C++", "omapsapp@@osmctools": "C", "omapsapp@@kothic": "Python", "omapsapp@@Alohalytics": "C++", "omapsapp@@protobuf": null, "omapsapp@@twine": "<PERSON>", "omapsapp@@just_gtfs": "C++", "exploitagency@@ESPloitV2": "C++", "AndrewBelt@@oui-blendish": "C", "AndrewBelt@@rtaudio": "C++", "AndrewBelt@@glfw": "C", "sisong@@zlib": "C", "younghyunjo@@wolfssl": "C", "AlexAltea@@ps3autotests": "C++", "avaneev@@r8brain-free-src": "C++", "clo-yunhee@@windeployqt": "C++", "clo-yunhee@@macdeployqt": "C++", "baohaojun@@md5": "C", "baohaojun@@qt-solutions": "C++", "azadkuh@@qhttp": "C++", "GPUOpen-LibrariesAndSDKs@@Anvil": "C++", "JoeyDeVries@@LearnOpenGL": "C++", "unrealircd@@ircfly": "<PERSON>", "limbo018@@Limbo": "C++", "saebyn@@munkres-cpp": "C++", "qword-os@@echfs": "C", "NikolausDemmel@@rootba_test_data": null, "NikolausDemmel@@Pangolin": "C++", "NikolausDemmel@@visit_struct": "C++", "quicknir@@wise_enum": "C", "grisumbras@@enum-flags": "C++", "jewelzhu@@azure-c-shared-utility": "C", "Dewb@@whitewhale": "C", "Dewb@@teletype": "C", "Dewb@@meadowphysics": "C", "phusion@@union_station_hooks_core": "<PERSON>", "phusion@@union_station_hooks_rails": "<PERSON>", "chfast@@ethash": "C++", "0xdead4ead@@BeastHttp": "C++", "schwabe@@ics-openvpn": "C", "vitor251093@@darkspore_server": "C++", "pando-project@@jerryscript": "C", "pando-project@@http-parser": "C", "pando-project@@libtuv": "C", "Samsung@@jerryscript": "C", "TarsCloud@@TarsProtocol": "CMake", "shadowsocks@@shadowsocks-libev": "C", "shadowsocks@@badvpn": "C", "shadowsocks@@libevent": "C", "shadowsocks@@redsocks": "C", "windowsair@@ESP8266_RTOS_SDK": null, "xlvector@@ps-lite": "C++", "fido2020@@LemonUtils": "C++", "Kode@@khamake": "TypeScript", "Kode@@khacpp": "C++", "Kode@@haxe_bin": "Haxe", "Kode@@oggenc_bin": null, "Kode@@kravur_bin": null, "Kode@@lame_bin": null, "Kode@@Kore": "C", "Jamol@@libkev": "C++", "cruzdb@@googletest": "C++", "lemire@@CRoaringUnityBuild": "C", "DanielChappuis@@reactphysics3d": "C++", "coqui-ai@@STT-examples": "Python", "coqui-ai@@tensorflow": "C++", "Return-To-The-Roots@@libendian": "C++", "Return-To-The-Roots@@liblobby": "C++", "Return-To-The-Roots@@libsiedler2": "C++", "Return-To-The-Roots@@libutil": "CMake", "Return-To-The-Roots@@mygettext": "C++", "Return-To-The-Roots@@s25update": "C++", "Return-To-The-Roots@@languages": null, "Return-To-The-Roots@@s25edit": "C++", "mat007@@turtle": "C++", "Return-To-The-Roots@@dev-tools": "C", "Return-To-The-Roots@@s-c": "C", "Return-To-The-Roots@@version": "C++", "rennerm@@avr-can-lib": "C", "erengy@@anime-relations": null, "erengy@@anisthesia": "C++", "erengy@@anitomy": "C++", "erengy@@hypp": "C++", "erengy@@hypr": "C++", "erengy@@monolog": "C++", "erengy@@nstd": "C++", "erengy@@semaver": "C++", "erengy@@windows": "C++", "erengy@@anime-seasons": null, "erengy@@utf8proc": "C", "hiroyuki-komatsu@@japanese-usage-dictionary": "Python", "taku910@@zinnia": "Shell", "mozilla-l10n@@mozilla-vpn-client-l10n": "Python", "WireGuard@@wireguard-tools": "C", "WireGuard@@wireguard-apple": "Swift", "WireGuard@@wireguard-go": "Go", "adjust@@ios_sdk": "Objective-C", "diharaw@@AssetCore": "C++", "chaoticbob@@SPIRV-Reflect": "C", "Atmosphere-NX@@libstratosphere": "C++", "yamashi@@RED4ext.SDK": "C++", "Try@@ZenLib": "C", "Try@@Tempest": "C++", "schellingb@@TinySoundFont": "C", "Try@@MoltenTempest": "C++", "leon-liangwu@@box_utils": "Python", "dvorka@@discount": "C", "kbeckmann@@retro-go-stm32": "C", "wjakob@@tinyformat": "C++", "wjakob@@hypothesis": "C++", "JaGoLi@@ytdl-gui-aur": "Shell", "vtil-project@@VTIL-Core": "C++", "xforce@@lavender": "Python", "xforce@@ksignals": "C++", "xforce@@meow-hook": "C++", "uliwitness@@WideningBezierPathTest": "Objective-C", "uliwitness@@Forge": "C++", "uliwitness@@ForgeDebugger": "Objective-C", "uliwitness@@KVOWithBlocks": "Objective-C", "uliwitness@@Stacks": "CSS", "uliwitness@@stackimport": "C++", "uliwitness@@UKPaintView": "Objective-C", "uliwitness@@UKDistributedView": "Objective-C", "uliwitness@@UKSyntaxColoredTextDocument": "Objective-C", "uliwitness@@tinyxml2": "C++", "uliwitness@@ULIMelodyQueue": "Objective-C", "uliwitness@@UliKit": "Objective-C", "uliwitness@@ULINetSocket": "Objective-C", "uliwitness@@Sparkle": "Objective-C", "uliwitness@@Leonie": "C", "tardate@@X113647Stepper": "C++", "tardate@@TextFinder": "C++", "wimleers@@flexitimer2": "C++", "maniacbug@@RF24": "Processing", "tardate@@RadioHead": "C++", "wayoda@@LedControl": "C++", "brianlow@@Rotary": "C++", "olikraus@@U8glib_Arduino": "C", "JChristensen@@DS3232RTC": "C++", "aaronjasso@@Cordwood": "C++", "LuckyResistor@@LRAS1130": "C++", "GreyGnome@@EnableInterrupt": "C++", "mathertel@@Radio": "C++", "adafruit@@TFTLCD-Library": "C", "sumotoy@@TFT_ILI9163C": "C++", "adafruit@@Adafruit-PCD8544-Nokia-5110-LCD-library": "C++", "milesburton@@Arduino-Temperature-Control-Library": "C++", "adafruit@@Adafruit_MPR121": "C++", "damellis@@PCM": "C", "LuckyResistor@@LRThreeDigits": "C++", "miguelbalboa@@rfid": "C++", "adafruit@@Adafruit_MQTT_Library": "C++", "SV-Zanshin@@INA": "C++", "jarzebski@@Arduino-INA226": "C++", "adafruit@@Adafruit-Thermal-Printer-Library": "C++", "adafruit@@TinyWireM": "C++", "datacute@@Tiny4kOLED": "C", "tardate@@arduino-new-ping": "C++", "TMRh20@@TMRpcm": "C++", "marecl@@HPDL1414": "C++", "fri000@@Servo8Bit": "C++", "kmhofmann@@eos": "Python", "topjohnwu@@MagiskSU": "C", "topjohnwu@@magiskpolicy": "C", "topjohnwu@@MagiskManager": "Java", "topjohnwu@@ndk-compression": "C", "topjohnwu@@sepolicy-inject": "C", "stephenrkell@@libmallochooks": "C", "stephenrkell@@cil": "OCaml", "stephenrkell@@libdlbind": "C", "stephenrkell@@liballocstool": "C++", "stephenrkell@@libsystrap": "C", "stephenrkell@@toolsub": "C++", "stephenrkell@@donald": "C", "stephenrkell@@dwarfidl": "C++", "stephenrkell@@libdwarfpp": "C++", "stephenrkell@@m4ntlr": "M4", "stephenrkell@@trap-syscalls": "C", "stephenrkell@@libantlr3cxx": "C++", "stephenrkell@@libcxxgen": "C++", "stephenrkell@@llvm": "C++", "stephenrkell@@libcxxfileno": "C++", "stephenrkell@@libsrk31cxx": "C++", "Loki-Astari@@ThorsIOUtil": "C++", "skui-org@@3rdparty": "C++", "skui-org@@ci": "Shell", "louisdx@@cxx-prettyprint": "C++", "thrust@@thrust": "C++", "giaf@@hpmpc": "C", "giaf@@blasfeo": "Assembly", "acados@@qpDUNES-dev": "C++", "giaf@@hpipm": "C", "acados@@qpOASES": "C", "acados@@tera_renderer": "Rust", "jaeandersson@@swig": "C++", "acados@@casadi": "C++", "fixstars@@libSGM": "<PERSON><PERSON>", "stotko@@stdgpu": "C++", "neka-nat@@flann": "C++", "benkuper@@juce_organicui": "C++", "benkuper@@juce_timeline": "C++", "benkuper@@juce_simpleweb": "C", "SaurikIT@@libffi": "C", "puppeh@@binutils-vc4": "C", "puppeh@@gcc-vc4": "C", "puppeh@@newlib-vc4": "C", "puppeh@@resim": "C++", "westerndigitalcorporation@@zenfs": "C++", "percona@@rocksdb": "C++", "mapbox@@mapbox-gl-js": "JavaScript", "mapbox@@vector-tile": "C++", "okdshin@@unique_resource": "C++", "mapbox@@mapbox-gl-native-boost": "C++", "mapbox@@eternal": "C++", "mapbox@@mapbox-base": "C++", "ZipArchive@@ZipArchive": "C", "mapbox@@mapbox-events-ios": "Objective-C", "mapbox@@geojson-vt-cpp": "C++", "mapbox@@shelf-pack-cpp": "C++", "mapbox@@cheap-ruler-cpp": "C++", "mapbox@@mapbox-gestures-android": "Java", "mapbox@@mapbox-java": "Java", "mapbox@@mapbox-events-android": "Java", "mapbox@@jazzy-theme": "JavaScript", "mapbox@@mvt-fixtures": "JavaScript", "mapbox@@mapbox-gl-styles": "JavaScript", "mapbox@@geojson.hpp": "C++", "mapbox@@supercluster.hpp": "C++", "mourner@@kdbush.hpp": "C++", "mapbox@@Optional": "C++", "mapbox@@pixelmatch-cpp": "C++", "mapbox@@jni.hpp": "C++", "kif-framework@@KIF": "Objective-C", "AliSoftware@@OHHTTPStubs": "Objective-C", "mapbox@@mapbox-gl-test-suite": null, "mapbox@@mapbox-gl-cocoa": null, "puyoai@@test-resource": null, "libretro@@glslang": "C++", "Cxbx-Reloaded@@subhook": "C", "Kitware@@sprokit": "C++", "ajweeks@@imgui": "C++", "Ivshti@@libmpv": "C", "Ivshti@@qBreakpad": "C++", "h2oai@@mxnet": "C++", "h2oai@@javacpp-presets": "Java", "h2oai@@javacpp": "Java", "alibaba@@GCanvas": "C", "codecat@@scratch2": "C++", "swganh@@documentation": null, "khronosgroup@@glslang": "C++", "cityflow-project@@rapidjson": "C++", "jasp-stats@@jaspDescriptives": "R", "jasp-stats@@jaspTTests": "R", "jasp-stats@@jaspAnova": "R", "jasp-stats@@jaspRegression": "R", "jasp-stats@@jaspFrequencies": "R", "jasp-stats@@jaspFactor": "R", "jasp-stats@@jaspAudit": "R", "jasp-stats@@jaspBain": "R", "jasp-stats@@jaspNetwork": "R", "jasp-stats@@jaspMachineLearning": "R", "jasp-stats@@jaspMetaAnalysis": "R", "jasp-stats@@jaspSem": "R", "jasp-stats@@jaspSummaryStatistics": "R", "jasp-stats@@jaspMixedModels": "R", "jasp-stats@@jaspDistributions": "R", "jasp-stats@@jaspEquivalenceTTests": "R", "jasp-stats@@jaspJags": "R", "jasp-stats@@jaspReliability": "R", "jasp-stats@@jaspVisualModeling": "QML", "jasp-stats@@jaspGraphs": "R", "jasp-stats@@jaspBase": "<PERSON><PERSON>", "jasp-stats@@jaspLearnBayes": "R", "jasp-stats@@jaspTools": "R", "jasp-stats@@jaspProphet": "R", "jasp-stats@@jaspProcessControl": "R", "jasp-stats@@jaspCochrane": "R", "jasp-stats@@jaspCircular": "R", "jasp-stats@@jaspColumnEncoder": "C++", "sepfy@@live555": "C++", "pistacheio@@pistache": "C++", "sony@@nnabla-c-runtime": "C", "Ai-Thinker-Open@@GPRS-C-SDK-LIB": null, "kosma@@minmea": "C", "aliyun@@iotkit-embedded": "C", "echfs@@echfs": "C", "ElonaFoobar@@lua53-android": "C", "ElonaFoobar@@SDL2": "C", "ElonaFoobar@@SDL2_mixer": "C", "ElonaFoobar@@SDL2_image": "C", "ElonaFoobar@@SDL2_ttf": "C", "ElonaFoobar@@libboost-1.69-android": "C++", "Ruin0x11@@lua53-android": "C", "Ruin0x11@@SDL2": "C", "Ruin0x11@@SDL2_mixer": "C", "Ruin0x11@@SDL2_image": "C", "Ruin0x11@@SDL2_ttf": "C", "c-util@@c-sundry": "C", "jeremyong@@Selene": "C++", "ariya@@esprima": "TypeScript", "auto-complete@@auto-complete": "Emacs <PERSON>", "Constellation@@escope": "JavaScript", "ariya@@esrefactor": "JavaScript", "Constellation@@estraverse": "JavaScript", "Andersbakken@@rparser": "C++", "pulp-platform@@Himax_Dataset": null, "pulp-platform@@Udacity_Dataset": null, "pulp-platform@@Zurich_Bicycle_Dataset": null, "leecbaker@@lb_common": "C++", "leomccormack@@Spatial_Audio_Framework": "C", "dtzxporter@@cppkore": "C", "ermig1979@@darknet": "C", "ermig1979@@blis": "C", "openvinotoolkit@@openvino": "C++", "microsoft@@onnxruntime": "C++", "opencv@@dldt": "C++", "FWGS@@nanogl": "C++", "FWGS@@gl-wes-v2": "C", "eliben@@pycparser": "Python", "hugoam@@bimg": "C++", "hugoam@@bgfx": "C++", "Celtoys@@pycgen": "Python", "edlund@@amalgamate": "Python", "jdryg@@vg-renderer": "C", "boostorg@@random": "C++", "atom@@crashpad": "C++", "hoytech@@hoytech-cpp": "C++", "hoytech@@lmdbxx": "C++", "aiekick@@sfntly": "C++", "freetype@@freetype2": "C", "aiekick@@cTools": "C++", "aiekick@@imgui": "C++", "trillek-team@@tec-assets": "GLSL", "trillek-team@@trillek-vcomputer-module": "C++", "adam4813@@Selene": "C++", "stephenmathieson@@str-starts-with.c": "C", "antirez@@sds": "C", "ltoscano@@ponvif": "PHP", "bluecherrydvr@@ponvif_ext": "Python", "bluecherrydvr@@ffmpeg": "C", "hkalodner@@range-v3": "C++", "zeek@@broccoli": "C", "zeek@@broctl": "Python", "JaCzekanski@@libchdr": "C", "JaCzekanski@@premake-androidmk": "<PERSON><PERSON>", "gelldur@@EventBus": "C++", "espressif@@esp32-alink": "C", "mozilla@@pymake": "Python", "isovic@@argparser": "C++", "godotengine@@godot-cpp": "C++", "dextero@@powercmd": "Python", "AVSystem@@avs_commons": "C", "filecoin-project@@filecoin-ffi": "Rust", "filecoin-project@@serialization-vectors": null, "Alexey-N-Chernyshov@@libsecp256k1": "C", "filecoin-project@@test-vectors": "Go", "filecoin-project@@bls-signatures": "Rust", "badaix@@popl": "C++", "badaix@@jsonrpcpp": "C++", "rui314@@msvc-demangler": "C++", "tjingrant@@google-benchmark": "C++", "NiLuJe@@font8x8": "C", "NiLuJe@@stb": "C", "etodd@@utf8.h": "C", "etodd@@dialogger": "JavaScript", "et1337@@glew": "C", "et1337@@assimp": "C++", "et1337@@glfw": "C", "et1337@@lodepng": "C++", "AirGuanZ@@Utils": "C++", "WebAssembly@@wabt": "C++", "LLNL@@conduit": "C++", "LLNL@@simpool": "C++", "ROCm-Developer-Tools@@HIP": "C++", "leutloff@@diff-match-patch-cpp-stl": "C++", "cryptonomex@@secp256k1-zkp": "C", "steemit@@fc": "C++", "DaemonEngine@@CBSE-Toolchain": "C++", "Unvanquished@@libRocket": "C++", "DaemonEngine@@recastnavigation": "C++", "DaemonEngine@@Daemon": "C++", "DaemonDevelopers@@CBSE-Toolchain": "C++", "ish-app@@libapps": "JavaScript", "azure-rtos@@threadx": "C", "azure-rtos@@netxduo": "C", "martty@@VulkanMemoryAllocator": "C++", "martty@@ImGuiColorTextEdit": "C++", "gallickgunner@@ImGui-Addons": "C++", "charles-lunarg@@vk-bootstrap": "C++", "rpetrich@@theos": "<PERSON><PERSON>", "bazelbuild@@rules_apple": "Starlark", "bazelbuild@@rules_swift": "Starlark", "bazelbuild@@apple_support": "Starlark", "bazelbuild@@tulsi": "Swift", "ali-fareed@@rules_swift": "Starlark", "ali-fareed@@tulsi": "Swift", "troglobit@@libite": "C", "martinpaljak@@MuscleApplet": "Shell", "frankmorgner@@IsoApplet": "Java", "openenclave@@openenclave-musl-libc-tests": "C", "camilasan@@qtmacgoodies": "Objective-C++", "wuxx@@STM32F103C8T6_CMSIS-DAP_SWO": "C", "wren-lang@@wren": "C", "Chia-Network@@relic": null, "bpowers@@atsy": "Python", "bpowers@@Heap-Layers": "C++", "anttikantee@@buildrump.sh": "C", "CNTK-components@@CNTK1bitSGD": "C++", "OSSIA@@API": "Max", "KDE@@qqc2-desktop-style": "QML", "jcelerier@@QRecentFilesMenu": "C++", "jcelerier@@quazip": "C++", "OSSIA@@cmake-modules": "CMake", "jcelerier@@nano-signal-slot": "C++", "jcelerier@@nodeeditor": "C++", "jcelerier@@libaudiotool": "C++", "leo-yuriev@@libfptu": "C++", "BrianGladman@@sha": "C", "espressif@@esp32-wifi-lib": "Shell", "espressif@@esp32-bt-lib": null, "obgm@@libcoap": "C", "espressif@@mbedtls": "C", "espressif@@asio": "C++", "espressif@@esp-lwip": "C", "espressif@@esp-mqtt": "C", "themadinventor@@esptool": "Python", "swift-nav@@libswiftnav": "C++", "swift-nav@@ChibiOS": "C", "PipeWire@@pipewire-alsa": "C", "PipeWire@@pipewire-jack": "C", "Librevault@@librevault-common": "C++", "Librevault@@dir_monitor": "C++", "Librevault@@spdlog": "C++", "Librevault@@rabin": "C", "Librevault@@websocketpp": "C++", "GamePad64@@spdlog": "C++", "GamePad64@@lvcrypto": "C++", "GamePad64@@lvsqlite3": "C++", "GamePad64@@cryptodiff": "C++", "DBraun@@Sampler": "C++", "grame-cncm@@faust": "C++", "breakfastquay@@rubberband": "C++", "lvgl@@lvgl_esp32_drivers": "C", "huggle@@extension-review": "C++", "2ndQuadrant@@pglogical": "C", "olegus8@@dasGenForEach": "Python", "olegus8@@dasBinder": "Python", "olegus8@@dasVulkan": "C++", "nasa@@cFE": "C", "nasa@@psp": "C", "nasa@@elf2cfetbl": "C", "nasa@@gen_sch_tbl": "C", "cncf@@udpa": "Starlark", "lyft@@protoc-gen-validate": "Go", "JuDePom@@LibSL-small": "C++", "dnbaker@@bonsai": "C++", "dnbaker@@sketch": "C++", "dnbaker@@distmat": "C++", "dnbaker@@khset": "C", "group-gu@@mumps": "Fortran", "UltimateHackingKeyboard@@KSDK_2.0_FRDM-K22F": "C", "plenluno@@libj": "C++", "plenluno@@cpplint": "Python", "plenluno@@openssl": "C", "plenluno@@b64": "C++", "plenluno@@libuv": "C", "plenluno@@http-parser": "C", "toblum@@McLightingUI": "<PERSON><PERSON>", "pocoproject@@openssl": "C", "berkeley-abc@@abc": "C", "barefootnetworks@@infra": "C", "a-n-t-h-o-n-y@@ncurses-snapshots": null, "a-n-t-h-o-n-y@@Signals": "C++", "a-n-t-h-o-n-y@@Chess-curses": "C++", "a-n-t-h-o-n-y@@Optional": "C++", "COLLADA2GLTF@@OpenCOLLADA": "C++", "lasalvavida@@ahoy": "C++", "KhronosGroup@@OpenCOLLADA": "C++", "joaquimorg@@lvgl": "C", "MoarVM@@dyncall": "C", "MoarVM@@dynasm": "C", "MoarVM@@libtommath": "C", "MoarVM@@cmp": "C", "MoarVM@@libatomic_ops": "C", "MoarVM@@ryu": null, "MoarVM@@linenoise": "C", "antonte@@sdlpp": "C++", "libusbhost@@libusbhost": "C", "cycfi@@portmidi": "C", "nickbruun@@hayai": "C++", "hpcc-systems@@crossfilter": null, "hpcc-systems@@dojo": "JavaScript", "hpcc-systems@@dijit": "HTML", "hpcc-systems@@dojox": "JavaScript", "hpcc-systems@@util": "JavaScript", "hpcc-systems@@Visualization": "TypeScript", "hpcc-systems@@topojson": null, "hpcc-systems@@d3": null, "shpakovski@@MASPreferences": "Objective-C", "neolee@@SHKit": "Objective-C", "Kentzo@@ShortcutRecorder": "Objective-C", "neolee@@YACYAML": "C", "mgiannikouris@@openxc-message-format": "C++", "openxc@@emhashmap": "C", "peplin@@arduino.mk": "Java", "chipsalliance@@rocket-chip": "Scala", "ucb-bar@@barstools": "Scala", "ucb-bar@@riscv-torture": "Scala", "sifive@@sifive-blocks": "Scala", "ucb-bar@@hwacha": "Scala", "firesim@@firesim": "Scala", "firesim@@icenet": "Scala", "ucb-bar@@chipyard-toolchain-prebuilt": "<PERSON><PERSON><PERSON>", "riscv@@riscv-openocd": "C", "ucb-bar@@esp-gnu-toolchain": "C", "ucb-bar@@esp-isa-sim": "C", "ucb-bar@@esp-tests": "C", "ucb-bar@@libgloss-htif": "C", "ucb-bar@@hammer": "Python", "ucb-bar@@dsptools": "Scala", "freechipsproject@@chisel-testers": "Scala", "ucb-bar@@sha3": "Verilog", "CTSRD-CHERI@@axe": "C++", "ucb-bar@@spec2017-workload": "Python", "ucb-bar@@coremark-workload": "Shell", "firesim@@FireMarshal": "Python", "ucb-bar@@cva6-wrapper": "Scala", "firesim@@DRAMSim2": "C++", "ucb-bar@@nvdla-wrapper": "Verilog", "ucb-bar@@nvdla-workload": "Shell", "riscv-boom@@dromajo": "C++", "ucb-bar@@riscv-sodor": "Scala", "sifive@@fpga-shells": "Scala", "chipsalliance@@api-config-chipsalliance": "Scala", "ucb-bar@@rocket-dsp-utils": "Scala", "ucb-bar@@ibex-wrapper": "Scala", "freechipsproject@@chisel3": "Scala", "freechipsproject@@firrtl": "Scala", "freechipsproject@@treadle": "Scala", "freechipsproject@@firrtl-interpreter": "Scala", "ucb-bar@@ariane-wrapper": "Scala", "riscv@@riscv-tools": "Shell", "ucb-bar@@esp-tools": "Shell", "dyne@@writedown": "Shell", "dyne@@webnomad": "JavaScript", "mozilla-services@@lua_sandbox": "C", "philanc@@luazen": "C", "scylladb@@scylla-seastar": "C++", "scylladb@@scylla-swagger-ui": "JavaScript", "scylladb@@xxHash": "C", "scylladb@@seastar": "C++", "advancedfx@@imgui": "C++", "ripieces@@advancedfx-prop": "C++", "CaseyCarter@@cmcstl2": "C++", "adah1972@@nvwa": "C++", "Ryzee119@@ArduinoCore-avr": "C", "RussTedrake@@underactuated": "HTML", "lcm-proj@@lcm": "Java", "openhumanoids@@bot_core_lcmtypes": "CMake", "RobotLocomotion@@libbot2": null, "RobotLocomotion@@director": "Python", "RobotLocomotion@@ipopt-mirror": "Fortran", "RobotLocomotion@@lcmtypes": "CMake", "commontk@@PythonQt": null, "patmarion@@ctkPythonConsole": "C++", "patmarion@@QtPropertyBrowser": "C++", "RobotLocomotion@@pybind11": "C++", "mwoehlke-kitware@@bot_core_lcmtypes": "CMake", "RobotLocomotion@@libbot": "C", "RobotLocomotion@@spotless-pod": "CMake", "mitdrc@@signal-scope": "C++", "OctoMap@@octomap": "C++", "RobotLocomotion@@gurobi": "CMake", "RobotLocomotion@@mosek": "CMake", "rdeits@@iris-distro": "Matlab", "RobotLocomotion@@yalmip": "CMake", "RobotLocomotion@@sedumi": "CMake", "RobotLocomotion@@avl": "FORTRAN", "RobotLocomotion@@xfoil": "FORTRAN", "RobotLocomotion@@meshConverters": "C++", "rdeits@@swigmake": "C", "RobotLocomotion@@LittleDog": "MATLAB", "dreal@@dreal3": "SMT", "RobotLocomotion@@bullet3": "C++", "RobotLocomotion@@eigen-mirror": "C++", "danfis@@libccd": "C", "RobotLocomotion@@cmake": "CMake", "RobotLocomotion@@eigen-pod": "CMake", "RobotLocomotion@@gtk-pod": "CMake", "RobotLocomotion@@lcm": "Java", "RobotLocomotion@@bullet-pod": "CMake", "RobotLocomotion@@octomap-pod": "Shell", "rdeits@@mosek-pod": null, "rdeits@@swig-matlab-pod": "<PERSON><PERSON><PERSON>", "CxxTest@@cxxtest": "Python", "randombit@@botan": "C++", "BoschSensortec@@BME68x-Sensor-API": "C", "pimoroni@@BMP280_driver": null, "lotem@@google-glog": "C++", "lotem@@kyotocabinet-treedb": "C", "lotem@@yaml-cpp": "C++", "tomgr@@googletest-mirror": "C++", "nesbox@@blip-buf": "C++", "nesbox@@giflib": "C", "nesbox@@lpeg": null, "grimfang4@@sdl-gpu": "C", "nesbox@@moonscript": null, "nesbox@@curl": null, "gorthauer@@artwork": null, "euroelessar@@q-xdg": "C++", "XITRIX@@borealis": "C++", "cypresssemiconductorco@@emeeprom": "C", "byteduck@@duckos-doom": "C", "byteduck@@duckos-DOOM": "C", "HLTAS@@hltas": "C++", "LuaDist@@luajit": "C", "renard314@@ViewPager3D": "Java", "namazso@@langcomp": "PowerShell", "rafagafe@@tiny-json": "C", "adegtyarev@@streebog": "Objective-C", "urbit@@berkeley-softfloat-3": "C", "urbit@@ed25519": "C", "urbit@@libscrypt": "C", "urbit@@murmur3": "C", "urbit@@libuv": "C", "urbit@@h2o": "C", "urbit@@argon2": "C", "urbit@@secp256k1": "C", "catkin@@catkin_simple": "EmberScript", "ethz-asl@@rotors_simulator": "C++", "ethz-asl@@forest_gen": "Python", "GorgonMeducer@@PLOOC": "C", "keithjjones@@7z": "C++", "espressif@@esp-aliyun": "C", "danielsanfr@@qt-qrcode": "C++", "Shadowsocks-NET@@Qv2rayBase": "C++", "xiaokangwang@@v2ray-core-1": null, "Shadowsocks-NET@@QNodeEditor": "C++", "Shadowsocks-NET@@QvPlugin-Interface": "C++", "cnlohr@@cntools": "C", "cnlohr@@rawdraw": "C", "coin-or-tools@@BuildTools": "M4", "coin-or-tools@@ThirdParty-ASL": "Shell", "coin-or-tools@@ThirdParty-Blas": "Shell", "coin-or-tools@@ThirdParty-HSL": "Shell", "coin-or-tools@@ThirdParty-Lapack": "Shell", "coin-or-tools@@ThirdParty-Metis": "Shell", "LibrePCB@@sexpresso": "C++", "LibrePCB@@demo-workspace": "Shell", "azonenberg@@scopehal": "C++", "azonenberg@@scopehal-apps": "C++", "TDesktop-x64@@lib_base": "C++", "TDesktop-x64@@lib_ui": "C++", "TDesktop-x64@@cmake_helpers": "CMake", "TDesktop-x64@@tgcalls": "C++", "sdlpal@@SDL": "C", "sdlpal@@mingw-std-threads": "C++", "faasm@@faabric": "C++", "faasm@@python": "Python", "Shillaker@@tensorflow": "C++", "Shillaker@@eigen-git-mirror": "C++", "Shillaker@@gem3-mapper": "C", "Shillaker@@WAVM": "C++", "Shillaker@@pyfaasm": "Python", "Shillaker@@faasm-clapack": null, "Shillaker@@Kernels": "C", "Shillaker@@wasi-libc": "C", "imageworks@@pystring": "C++", "wc-duck@@getopt": "C", "aengelke@@fadec": "C", "bitdefender@@bddisasm": "C", "canonical@@fmt": null, "albaguirre@@grpc": "C++", "albaguirre@@libssh": "C", "electron@@libchromiumcontent": "Python", "atom@@libchromiumcontent": "Python", "svn2github@@gyp": "Python", "brightray@@libchromiumcontent": "Python", "m-labs@@mor1kx": "Verilog", "llvm-mirror@@libunwind": "C++", "commaai@@panda": "C", "mapbox@@tilelive.js": "JavaScript", "stxxl@@stxxl": "C++", "horsicq@@QYara": "C", "horsicq@@QOpenSSL": "C", "RegrowthStudios@@SoADeps": "C++", "3Hren@@metrics": "C++", "3Hren@@blackhole": "C++", "swaywm@@wlroots-rs": "Rust", "ArweaveTeam@@prometheus.erl": "Erl<PERSON>", "deadtrickster@@accept": "Erl<PERSON>", "deadtrickster@@prometheus_process_collector": "C++", "ninenines@@cowboy": "Erl<PERSON>", "ArweaveTeam@@prometheus-cowboy": "Erl<PERSON>", "ninenines@@cowlib": "Erl<PERSON>", "ninenines@@ranch": "Erl<PERSON>", "deadtrickster@@prometheus.erl": "Erl<PERSON>", "actor-framework@@actor-framework": "C++", "meitar@@git-archive-all.sh": "Shell", "boutproject@@boututils": "Python", "maplibre@@maplibre-gl-native-boost": "C++", "mapbox@@mapbox-gl-native-curl": "C", "maplibre@@maplibre-java": "Java", "mapbox@@mapbox-gl-native": "C++", "osrg@@earthquake": "Go", "P-p-H-d@@mlib": "C", "Flipper-Zero@@STM32CubeWB": "C", "flipperdevices@@flipperzero-protobuf": null, "flipperdevices@@libusb_stm32": "C", "Flipper-Zero@@floopper-bloopper": "C", "STMicroelectronics@@STM32CubeL4": "C", "STMicroelectronics@@STM32CubeWB": "C", "osquery@@third-party-berkeley-db": "C", "awslabs@@aws-lc": "C++", "vrpn@@vrpn": "C", "rpavlik@@jenkins-ctest-plugin": "XSLT", "OSVR@@OSVR-JSON-Schemas": null, "OSVR@@discount": "C", "OSVR@@UIforETWbins": "Python", "catid@@libcat": "C++", "chjj@@marked": "JavaScript", "irdvo@@gpxlib": "C++", "michel2323@@AdjointMPI": "C", "SciCompKL@@CoDiPack": "C++", "simdsoft@@ftp_server": "C++", "biometrics@@janus": "C++", "biometrics@@openbr-models": null, "coronalabs@@submodule-mojoAL": "C", "coronalabs@@submodule-platform-emscripten": "C", "coronalabs@@submodule-platform-linux": "C", "coronalabs@@submodule-ideviceinstaller": "C", "coronalabs@@submodule-libimobiledevice": "C", "coronalabs@@submodule-libplist": "C", "coronalabs@@submodule-libusbmuxd": "C", "cloudcompare@@normals_Hough": "C++", "amnezia-vpn@@QtSsh": "C++", "ss-abramchuk@@OpenVPNAdapter": "C++", "lvgl@@lv_demos": "C", "keepkey@@device-protocol": null, "keepkey@@trezor-firmware": "C", "keepkey@@code-signing-keys": null, "keepkey@@python-keepkey": "Python", "keepkey@@QR-Code-generator": "Java", "keepkey@@trezor-crypto": "C", "EOSIO@@fc": "C++", "EOSIO@@binaryen": "Assembly", "TalonBraveInfo@@platform": "C", "peterall@@eurorack": "C++", "wallix@@program_options": "C++", "lukeredpath@@LRMocky": "<PERSON><PERSON>", "johnezang@@JSONKit": "Objective-C", "boost-experimental@@di": "C++", "boost-experimental@@msm-lite": null, "nasa@@astrobee_android": "Java", "bl4ck5un@@mbedtls-SGX": "C", "WohlSoft@@luabind-deboostified": "C++", "grumpycoders@@uC-sdk": "C", "exoticlibraries@@libcester": "C", "grumpycoders@@LuaJIT": "C", "Distrotech@@ucl": "Assembly", "grumpycoders@@zep": "C++", "grumpycoders@@ImGuiColorTextEdit": null, "russdill@@lwip-udhcpd": "C", "russdill@@lwip-libevent": "C", "russdill@@lwip-nat": "C", "babelouest@@orcania": "C", "ArashPartow@@bloom": "C++", "RipcordSoftware@@libhttpserver": "C++", "apache@@couchdb": "Erl<PERSON>", "RipcordSoftware@@libscriptobject": "C++", "RipcordSoftware@@libjsapi": "C++", "ikalnitsky@@termcolor": "C++", "RipcordSoftware@@lazyflatset": "C++", "RipcordSoftware@@thread-pool-cpp": "C++", "RangeNetworks@@CommonLibs": "C++", "RangeNetworks@@sqlite3": "C", "MrKepzie@@OpenColorIO-Configs": "Python", "gabriel-tenma-white@@mculib": "C++", "Open-Smartwatch@@lib-open-smartwatch": "C++", "kikuchan@@pngle": "C", "Open-Smartwatch@@NeoGPS": "C++", "moononournation@@Arduino_GFX": "C++", "T-vK@@ESP32-BLE-Keyboard": "C++", "bitbank2@@AnimatedGIF": "C", "BoschSensortec@@BMA400-API": "C", "azure@@azure-iot-sdks": null, "CeetronSolutions@@qwt": "C++", "WojciechMula@@Xscr": "C", "spring@@HughAI": "Java", "spring@@Python": "Python", "spring@@Shard": "<PERSON><PERSON>", "spring@@KAIK": "C++", "spring@@CircuitAI": "C++", "spring@@pyunitsync": "Python", "spring@@pr-downloader": "C++", "spring@@SpringMapConvNG": "C++", "spring@@E323AI": "C++", "Tarendai@@Shard": "C", "Error323@@E323AI": "C++", "hughperkins@@HughAI": "Java", "YosysHQ@@yosys": "C++", "kd7tck@@jar": "C", "NVIDIAGameWorks@@RTXMU": "C++", "TUM-I5@@XdmfWriter": "C++", "TUM-I5@@utils": "C++", "TUM-I5@@ASYNC": "C++", "TUM-I5@@scons-tools": "Python", "TUM-I5@@PUML2": "C++", "SeisSol@@easi": "C++", "uphoffc@@ImpalaJIT": "C++", "SeisSol@@yateto": "Python", "FlagBrew@@memecrypto": "C", "dsoldier@@memecrypto": "C", "FlagBrew@@EventsGalleryPacker": "C++", "zaksabeast@@PKSMBrowserInject": "HTML", "schani@@rwimg": "C", "SuperTux@@translations": null, "SuperTux@@data": "Scheme", "SuperTux@@squirrel": "C++", "gopro@@cineform-sdk": "C", "CESNET@@GPUJPEG": "C", "averne@@libstratosphere": "C++", "averne@@glfw": "C", "infinitdotio@@json-spirit": "C++", "shanteacontrols@@midi-lib": "C++", "paradajz@@LESS-DB": "C++", "shanteacontrols@@sysex-conf": "C++", "paradajz@@core": "C++", "paradajz@@EmuEEPROM": "C++", "shanteacontrols@@DMXUSB": "C++", "paradajz@@midi-lib": "C++", "paradajz@@sysex-conf": "C++", "paradajz@@avr-core": "C++", "paradajz@@AVR-DB": "C++", "TeamWisp@@DirectXTex": "C++", "TeamWisp@@DXR-Fallback-Layer": "C++", "TeamWisp@@Wisp-LFS": null, "TeamWisp@@Crashpad": "C++", "ahupowerdns@@powerblog": "C++", "nadrino@@borealis": "C++", "nadrino@@libtesla": "C", "nadrino@@cpp-generic-toolbox": "C++", "retronx-team@@switch-nanovg": "C", "hexhacking@@xDL": "C", "hexhacking@@lzma": "C++", "glotzerlab@@fsph": "C++", "chr1shr@@voro": "C++", "bloomtime@@CinderGestures": "C++", "bloomtime@@CinderIPod": "Objective-C", "bloomtime@@CinderFlurry": "Objective-C", "bloomtime@@CinderOrientation": "C++", "zdenop@@hunspell-mingw": "C++", "jherico@@jocular": "C", "viktorgino@@libheadunit": "C++", "viktorgino@@headunit-gui": "QML", "viktorgino@@welle.io": "C", "viktorgino@@navit": "C", "KDE@@plasma-pa": "C++", "AlbrechtL@@welle.io": "C", "gartnera@@headunit": "C++", "clMathLibraries@@clRNG": "C", "rigaya@@build_pkg": "Python", "Intel-Media-SDK@@MediaSDK": "C++", "dualface@@cocos2d-x": "C++", "kinnou02@@SimpleAmqpClient": "C++", "nasa-itc@@deployment": "HTML", "nasa-itc@@CFS_CI": null, "nasa-itc@@CFS_TO": null, "nasa-itc@@CFS_IO_LIB": null, "nasa-itc@@cFE": null, "nasa-itc@@osal": null, "nasa-itc@@PSP": null, "nasa-itc@@cFS-GroundSystem": null, "nasa-itc@@elf2cfetbl": null, "nasa-itc@@SCH": null, "nasa-itc@@SC": null, "nasa-itc@@CF": null, "nasa-itc@@HK": null, "nasa-itc@@cfs_lib": null, "nasa-itc@@LC": null, "nasa-itc@@hwlib": "C", "nasa-itc@@HS": null, "nasa-itc@@arducam": "C", "nasa-itc@@clyde_eps": "C", "nasa-itc@@novatel_oem615": "C", "nasa-itc@@clyde_battery_sim": "C++", "nasa-itc@@arducam_sim": "C++", "nasa-itc@@clyde_eps_sim": "C++", "nasa-itc@@nos_time_driver": "C++", "nasa-itc@@novatel_oem615_sim": "C++", "nasa-itc@@sim_common": "C++", "nasa-itc@@sim_server": "CMake", "nasa-itc@@sim_terminal": "C++", "nasa-itc@@gsw-ait": "HTML", "nasa-itc@@OrbitInviewPowerPrediction": "HTML", "nasa-itc@@gsw-cosmos": "<PERSON>", "nasa-itc@@generic_reaction_wheel": "C", "nasa-itc@@generic_reaction_wheel_sim": "C++", "nasa-itc@@sample_app": "C", "nasa-itc@@sample_sim": "C++", "aseprite@@mujs": "C", "bondhugula@@isl-for-pluto": "C", "bondhugula@@cloog": "C", "periscop@@piplib": "C", "vincentloechner@@polylib": "C", "periscop@@candl": "C", "periscop@@clan": "C", "periscop@@openscop": "C", "periscop@@cloog": "C", "LINBIT@@drbd-kernel-compat": "C", "shadowsocks@@libudns": "C", "shadowsocks@@libsodium": "C", "lotlab@@tmk_keyboard": "C", "tmk@@tmk_keyboard": "C", "mit-acl@@separator": "C++", "vgteam@@sglib": "C++", "simongog@@sdsl-lite": "C++", "edawson@@fermi-lite": "C", "vgteam@@vowpal_wabbit": "C++", "ekg@@gssw": "C", "JohnLangford@@vowpal_wabbit": "C++", "vgteam@@boost-subset": "C++", "vgteam@@vcflib": "C++", "vgteam@@htslib": "C", "edawson@@gfakluge": "C++", "ekg@@cpp_progress_bar": "C++", "vgteam@@Superbubbles": "C++", "ekg@@rocksdb": "C++", "ekg@@sparsehash": "C++", "ekg@@xg": "C++", "edawson@@libvcfh": "C++", "shafreeck@@pb2json": "C++", "GreenWaves-Technologies@@mbed-os": "C", "GreenWaves-Technologies@@tf2gap8": "C++", "GreenWaves-Technologies@@gap_applications": "C", "GreenWaves-Technologies@@freeRTOS": "C", "GreenWaves-Technologies@@pulp_tools": "CMake", "GreenWaves-Technologies@@gap8_docs": "C", "GreenWaves-Technologies@@autotiler": "Python", "pulp-platform@@dpi-models": "C++", "pulp-platform@@runner": "Python", "pulp-platform@@gvsoc": "C++", "GreenWaves-Technologies@@benchmarks": "C", "pulp-platform@@pulp-debug-bridge": "C++", "HSAnet@@qt-google-analytics": "C++", "sqlite@@sqlite": "C", "ProtonMail@@go-srp": "Go", "azadkuh@@nlohmann_json_release": "C++", "GerhardR@@objload": "C++", "cisco@@openh264": "C++", "kduske@@vecmath": "C++", "ComputationalPhysics@@SimVis": "QML", "idaholab@@TEAL": "Python", "idaholab@@HERON": "Python", "idaholab@@SR2ML": "Python", "idaholab@@LOGOS": "Python", "idaholab@@moose": "C++", "ornl-qci@@cpr": "C++", "ornl-qci@@boost-cmake": "CMake", "ornl-qci@@cppmicroservices": "C++", "amccaskey@@qiskit-terra": "C++", "dwavesystems@@minorminer": "C++", "ornl-qci@@legacy-sapi-clients": "C++", "ORNL-QCI@@TriQ": "C++", "CompSciOrBust@@Arriba": "C++", "berdal84@@Observe": null, "berdal84@@imgui": "C++", "berdal84@@IconFontCppHeaders": "C", "berdal84@@mirror": "C++", "berdal84@@imgui-filebrowser": "C++", "berdal84@@ImGuiColorTextEdit": "C++", "wieslawsoltes@@MfcToolkit": "C++", "devshgraphicsprogramming@@glslang": "C++", "devshgraphicsprogramming@@SPIRV-Cross": "GLSL", "AcademySoftwareFoundation@@openexr": "C", "NVIDIA@@jitify": "C++", "RPCS3@@ffmpeg-core": "C", "RPCS3@@llvm-mirror": "C++", "RPCS3@@cereal": "C++", "RPCS3@@hidapi": "C", "RPCS3@@yaml-cpp": "C++", "tcbrindle@@span": "C++", "RipleyTom@@curl": "C", "RPCS3@@llvm": "LLVM", "RPCS3@@libusb": "C", "RPCS3@@libpng": "C", "RPCS3@@rsx_program_decompiler": "C++", "RPCS3@@pugixml": "C++", "RPCS3@@rsx-debugger": "QML", "RPCS3@@wxWidgets": "C++", "kobalicekp@@asmjit": "C++", "ccoffing@@airbag_fd": "C", "tangrams@@isect2d": "C++", "tangrams@@css-color-parser-cpp": "C++", "tangrams@@variant": "C++", "tangrams@@earcut.hpp": "C", "tangrams@@geojson-vt-cpp": "C++", "tangrams@@duktape": "C", "tangrams@@yaml-cpp": "C++", "hjanetzek@@alfons": "C++", "tangrams@@fontstash-es": "C", "dbry@@adpcm-xq": "C", "krkrsdl2@@krkrz": null, "krkrsdl2@@SamplePlugin": null, "krkrsdl2@@KAGParser": null, "krkrsdl2@@wuvorbis": null, "krkrsdl2@@meson_toolchains": null, "krkrsdl2@@ncbind": null, "krkrsdl2@@varfile": null, "krkrsdl2@@json": null, "krkrsdl2@@fstat": null, "krkrsdl2@@krglhwebp": null, "x64dbg@@capstone_wrapper": "C", "yue@@base": "C++", "yue@@yoga": "C++", "yue@@abseil-cpp": "C++", "google@@compact_enc_det": "C++", "facebook@@css-layout": "C++", "modm-io@@scons-build-tools": "Python", "modm-io@@cmsis-5-partial": "C", "nanoant@@CMakePCHCompiler": "CMake", "wheybags@@StormLib": "C", "rsjudka@@aasdk": "C++", "ultraembedded@@librtos": "C", "ultraembedded@@libhelix-mp3": "C", "ultraembedded@@fat_io_lib": "C", "uiuc-hpc@@LC": "C", "surge-synthesizer@@vst3sdk": "CMake", "surge-synthesizer@@vstgui": "C++", "surge-synthesizer@@nanosvg": null, "drobilla@@lv2": "C", "MishkaRogachev@@industrial-controls": "QML", "bitcoin@@libbase58": "C", "jwalabroad@@fermi-lite": "C", "jwalabroad@@bwa": "C", "walaj@@htslib": "C", "lriki@@Lumino.Core": "C", "lriki@@Lumino.Math": "C++", "google@@woff2": "C++", "NVlabs@@nvbio": "C++", "thennequin@@imgui": "C++", "01org@@mkl-dnn": "C++", "flyinghead@@flycast": "C++", "cheif@@RxRealm": null, "ReactiveX@@RxSwift": "Swift", "ashleymills@@Reachability.swift": "Swift", "RxSwiftCommunity@@RxDataSources": "Swift", "stephencelis@@SQLite.swift": "Swift", "realm@@realm-cocoa": "Objective-C", "bcylin@@QuickTableViewController": "Swift", "JoeMatt@@reicast-emulator": "C", "radex@@SwiftyUserDefaults": "Swift", "RxSwiftCommunity@@RxRealm": "Swift", "RxSwiftCommunity@@RxGesture": "Swift", "nsoperations@@HockeySDK-iOS": "Objective-C", "bitstadium@@HockeySDK-tvOS": "Objective-C", "fpillet@@NSLogger": "Objective-C", "Puasonych@@XLActionController": "Swift", "google@@promises": "Objective-C", "weichsel@@ZIPFoundation": "Swift", "xmartlabs@@XLActionController": "Swift", "tsolomko@@BitByteData": "Swift", "bitstadium@@HockeySDK-iOS": "Objective-C", "Provenance-Emu@@realm-cocoa": "Objective-C", "mxcl@@PromiseKit": "Swift", "tsolomko@@SWCompression": "Swift", "SeetaFace6Open@@OpenRoleZoo": "C++", "SeetaFace6Open@@SeetaEyeStateDetector": "C++", "SeetaFace6Open@@SeetaAgePredictor": "C++", "SeetaFace6Open@@FaceAntiSpoofingX6": "C++", "SeetaFace6Open@@SeetaGenderPredictor": "C++", "SeetaFace6Open@@SeetaMaskDetector": "C++", "SeetaFace6Open@@FaceTracker6": "C++", "SeetaFace6Open@@FaceBoxes": "C++", "SeetaFace6Open@@Landmarker": "C++", "SeetaFace6Open@@FaceRecognizer6": "C++", "SeetaFace6Open@@PoseEstimator6": "C++", "SeetaFace6Open@@QualityAssessor3": "C++", "TenniS-Open@@TenniS": "C++", "steveicarus@@ivtest": "Verilog", "VUnit@@vunit": "VHDL", "ghdl@@ghdl": "VHDL", "Ubpa@@UCMake": "CMake", "Ubpa@@UHEMesh": "C++", "Ubpa@@UGM": "C++", "Ubpa@@UTemplate": "C++", "rstudio@@libuv": "C", "quictls@@openssl": "C", "akamai@@openssl": "C", "nibanks@@everest-dist": "C", "Manu343726@@ctti": "C++", "Manu343726@@cppascii": "C++", "wtoorop@@DNS-LDNS": "C++", "erikoest@@Net-LDNS": "C", "espressif@@esp-face": "C++", "blair1618@@yaml-cpp": "C++", "AxioDL@@hecl": "C++", "AxioDL@@specter": "C++", "AxioDL@@nod": "C", "AxioDL@@amuse": "C++", "AxioDL@@kabufuda": "C++", "AxioDL@@NODLib": "C", "mono@@aspnetwebstack": "C#", "mono@@Newtonsoft.Json": "C#", "mono@@cecil": "C#", "mono@@rx": "C#", "mono@@ikvm-fork": "C#", "mono@@ikdasm": "C#", "mono@@reference-assemblies": "C#", "mono@@NUnitLite": "C#", "mono@@NuGet.BuildTasks": "C#", "mono@@boringssl": "C", "mono@@corefx": "C#", "mono@@bockbuild": "Python", "mono@@linker": "C#", "mono@@roslyn-binaries": "C#", "mono@@corert": "C#", "mono@@xunit-binaries": "C#", "mono@@api-doc-tools": "C#", "DavidAntliff@@esp32-smbus": "C", "Antidote@@Athena": "C++", "RetroView@@RetroCommon": "C++", "PromiseKit@@UIKit": "Objective-C", "PromiseKit@@Accounts": "Swift", "PromiseKit@@AddressBook": "Swift", "PromiseKit@@AssetsLibrary": "Swift", "PromiseKit@@CoreLocation": "Swift", "PromiseKit@@QuartzCore": "Objective-C", "PromiseKit@@Social": "Objective-C", "PromiseKit@@StoreKit": "Swift", "PromiseKit@@Bolts": "Swift", "PromiseKit@@CoreBluetooth": "Swift", "PromiseKit@@EventKit": "Swift", "PromiseKit@@SystemConfiguration": "Objective-C", "PromiseKit@@Alamofire": "Swift", "PromiseKit@@OMGHTTPURLRQ": "Objective-C", "PromiseKit@@AVFoundation": "Objective-C", "PromiseKit@@WatchConnectivity": "Swift", "PromiseKit@@MapKit": "Swift"}