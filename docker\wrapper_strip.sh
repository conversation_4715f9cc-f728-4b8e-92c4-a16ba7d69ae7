#!/bin/bash

# Strip Wrapper for IDA Pro Analysis
# 专门为IDA Pro反汇编分析优化的strip包装器
# 防止符号信息被剥离，确保IDA Pro能够进行高质量的控制流图分析

# 检查是否有足够的参数
if [ "$#" -lt 1 ]; then
  echo "Usage: $0 [options] file..."
  echo "This is a wrapper for strip optimized for IDA Pro analysis."
  echo "It preserves symbol information for better disassembly analysis."
  exit 1
fi

real_strip="strip-real"

# 解析参数
output_file=""
input_files=()
strip_options=()

while [[ $# -gt 0 ]]; do
  case $1 in
    -o)
      output_file="$2"
      shift 2
      ;;
    -*)
      strip_options+=("$1")
      shift
      ;;
    *)
      input_files+=("$1")
      shift
      ;;
  esac
done

# 如果设置了环境变量 PRESERVE_SYMBOLS_FOR_IDA=True，则跳过strip操作
if [ "$PRESERVE_SYMBOLS_FOR_IDA" = "True" ]; then
  echo "[-] Preserving symbols for IDA Pro analysis, skipping strip operation"
  
  # 如果指定了输出文件，则复制而不是strip
  if [ -n "$output_file" ] && [ ${#input_files[@]} -eq 1 ]; then
    cp "${input_files[0]}" "$output_file"
    echo "[-] Copied ${input_files[0]} to $output_file (symbols preserved)"
  else
    echo "[-] Strip operation skipped to preserve symbols for IDA Pro analysis"
  fi
  
  exit 0
fi

# 如果没有设置保护符号的环境变量，执行正常的strip操作
if [ -n "$output_file" ]; then
  # 有输出文件的情况
  "$real_strip" "${strip_options[@]}" -o "$output_file" "${input_files[@]}"
else
  # 没有输出文件的情况
  "$real_strip" "${strip_options[@]}" "${input_files[@]}"
fi
