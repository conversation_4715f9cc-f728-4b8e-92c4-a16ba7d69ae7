# subsystems
OBJS-$(CONFIG_AUDIODSP)                += ppc/audiodsp.o
OBJS-$(CONFIG_BLOCKDSP)                += ppc/blockdsp.o
OBJS-$(CONFIG_FDCTDSP)                 += ppc/fdctdsp.o
OBJS-$(CONFIG_FMTCONVERT)              += ppc/fmtconvert_altivec.o
OBJS-$(CONFIG_H264CHROMA)              += ppc/h264chroma_init.o
OBJS-$(CONFIG_H264DSP)                 += ppc/h264dsp.o ppc/hpeldsp_altivec.o
OBJS-$(CONFIG_H264QPEL)                += ppc/h264qpel.o
OBJS-$(CONFIG_HPELDSP)                 += ppc/hpeldsp_altivec.o
OBJS-$(CONFIG_IDCTDSP)                 += ppc/idctdsp.o
OBJS-$(CONFIG_LLVIDDSP)                += ppc/lossless_videodsp_altivec.o
OBJS-$(CONFIG_ME_CMP)                  += ppc/me_cmp.o
OBJS-$(CONFIG_MPEGAUDIODSP)            += ppc/mpegaudiodsp_altivec.o
OBJS-$(CONFIG_MPEGVIDEO)               += ppc/mpegvideo_altivec.o
OBJS-$(CONFIG_MPEGVIDEOENCDSP)         += ppc/mpegvideoencdsp.o
OBJS-$(CONFIG_PIXBLOCKDSP)             += ppc/pixblockdsp.o
OBJS-$(CONFIG_VC1DSP)                  += ppc/vc1dsp_altivec.o
OBJS-$(CONFIG_VIDEODSP)                += ppc/videodsp.o
OBJS-$(CONFIG_VP3DSP)                  += ppc/vp3dsp_altivec.o
OBJS-$(CONFIG_VP8DSP)                  += ppc/vp8dsp_altivec.o

# decoders/encoders
OBJS-$(CONFIG_HEVC_DECODER)            += ppc/hevcdsp.o
OBJS-$(CONFIG_LLAUDDSP)                += ppc/lossless_audiodsp_altivec.o
OBJS-$(CONFIG_MPEG4_DECODER)           += ppc/mpeg4videodsp.o
OBJS-$(CONFIG_SVQ1_ENCODER)            += ppc/svq1enc_altivec.o
OBJS-$(CONFIG_VORBIS_DECODER)          += ppc/vorbisdsp_altivec.o
OBJS-$(CONFIG_VP7_DECODER)             += ppc/vp8dsp_altivec.o
