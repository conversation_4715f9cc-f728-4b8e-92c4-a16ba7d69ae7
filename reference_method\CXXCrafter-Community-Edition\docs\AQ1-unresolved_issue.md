Among the 100 projects in the empirical study at that time, 14 projects failed due to unresolved source code errors or exceeding the four-hour build time limit.

By searching for keywords in the error messages within the corresponding GitHub issues, we identified potential build errors in 9 projects. Seven of these remain unresolved (as of 2024-12-05). Two projects, Codon and MuseScore, have been resolved, with MuseScore being fixed after our study was completed.

The details are provided in the table below.

| **Project Name** | **Failure Category** | **Similar Issues** | **Eventually Resolved or Not** |
| --- | --- | --- | --- |
| Codon | Unresolved Issue | Exist | Resolved |
| MuseScore | Unresolved Issue | Exist | Resolved |
| RT-Thread | Unresolved Issue | Exist | Not Resolved |
| Doom | Unresolved Issue | Exist | Not Resolved |
| Cocos2d-x | Unresolved Issue | Exist | Not Resolved |
| LocalAI | Unresolved Issue | Exist | Not Resolved |
| OpenFramework | Unresolved Issue | Exist | Not Resolved |
| Qv2ray | Unresolved Issue | Exist | Not Resolved |
| Cquery | Unresolved Issue | Exist | Not Resolved |
| Paddle | Timeout | None |  |
| FoundationDB | Timeout | None |  |
| ArangoDB | Timeout | None |  |
| Scylladb | Timeout | None |  |
| Wav2letter | Timeout | None |  |


### 1	Codon
[https://github.com/exaloop/codon](https://github.com/exaloop/codon)

| **Category** | **Content** |
| --- | --- |
| Failure | Unresolved Issue |
| Similar Github Issue | Codon does not support the default LLVM shipped with os.   [https://github.com/exaloop/codon/issues/537](https://github.com/exaloop/codon/issues/537) |
| Resolved or Not | Resolved |


### 2	MuseScore
[https://github.com/musescore/MuseScore](https://github.com/musescore/MuseScore/issues/24235)

| **Category** | **Content** |
| --- | --- |
| Failure | Unresolved Issue |
| Similar Github Issue | Broken Cmakefile.<br/>[https://github.com/musescore/MuseScore/issues/24235](https://github.com/musescore/MuseScore/issues/24235) |
| Resolved or Not | Resolved |


### 3	RT-Thread
[https://github.com/RT-Thread/rt-thread](https://github.com/RT-Thread/rt-thread/issues/8785)

| **Categorry** | **Content** |
| --- | --- |
| Failure | Unresolved Issue |
| Similar Github Issue | Undeclared identifier.<br/>[https://github.com/RT-Thread/rt-thread/issues/8785](https://github.com/RT-Thread/rt-thread/issues/8785) |
| Resolved or Not | Not Resolved |


### 4	Doom
[https://github.com/id-Software/DOOM.git](https://github.com/id-Software/DOOM.git)

| **Category** | **Content** |
| --- | --- |
| Failure | Unresolved Issue |
| Similar Github Issue | The original project dosen't support OOTB compilation.<br/>[https://github.com/id-Software/DOOM/pull/21](https://github.com/id-Software/DOOM/pull/21) |
| Resolved or Not | Not Resolved |


### 5	Cocos2d-x
[https://github.com/cocos2d/cocos2d-x](https://github.com/cocos2d/cocos2d-x/issues/20783)

| **Category** | **Content** |
| --- | --- |
| Failure | Unresolved Issue |
| Similar Github Issue | Linking error when integrating with libchipmunk. <br/>[https://github.com/cocos2d/cocos2d-x/issues/20783](https://github.com/cocos2d/cocos2d-x/issues/20783)  |
| Resolved or Not | Not Resolved |


### 6	LocalAI
[https://github.com/mudler/LocalAI](https://github.com/mudler/LocalAI/issues/2397)

| **Category** | **Content** |
| --- | --- |
| Failure | Unresolved Issue |
| Similar Github Issue | Inappropriate protobuf version.<br/>[https://github.com/mudler/LocalAI/issues/2397](https://github.com/mudler/LocalAI/issues/2397) |
| Resolved or Not | Not Resolved |


### 7 	OpenFramework
[https://github.com/openframeworks/openFrameworks](https://github.com/openframeworks/openFrameworks/issues/7019)

| **Category** | **Content** |
| --- | --- |
| Failure | Unresolved Issue |
| Similar Github Issue | File system error.<br/>[https://github.com/openframeworks/openFrameworks/issues/7019](https://github.com/openframeworks/openFrameworks/issues/7019) |
| Resolved or Not | Not Resolved |


### 8	Qv2ray
[https://github.com/Qv2ray/Qv2ray](https://github.com/Qv2ray/Qv2ray/issues/1508)

| **Category** | **Content** |
| --- | --- |
| Failure | Unresolved Issue |
| Similar Github Issue | Grpc linking error.<br/>[https://github.com/Qv2ray/Qv2ray/issues/1508](https://github.com/Qv2ray/Qv2ray/issues/1508) |
| Resolved or Not | Not Resolved |


### 9	Cquery
[https://github.com/jacobdufault/cquery](https://github.com/jacobdufault/cquery/issues/881)

| **Category** | **Content** |
| --- | --- |
| Failure | Unresolved Issue |
| Similar Github Issue | C++ std namespace error.<br/>[https://github.com/jacobdufault/cquery/issues/881](https://github.com/jacobdufault/cquery/issues/881)<br/>[https://github.com/jacobdufault/cquery/issues/](https://github.com/jacobdufault/cquery/issues/881)733 |
| Resolved or Not | Not Resolved |


### 10 	Paddle
[https://github.com/PaddlePaddle/Paddle.git](https://github.com/PaddlePaddle/Paddle.git)

| **Category** | **Content** |
| --- | --- |
| Failure | Timeout |
| Similar Github Issue | None |


### 11 	FoundationDB
[https://github.com/apple/foundationdb.git](https://github.com/apple/foundationdb.git)

| **Category** | **Content** |
| --- | --- |
| Failure | Timeout |
| Similar Github Issue | None |


### 12 	ArangoDB
[https://github.com/arangodb/arangodb.git](https://github.com/arangodb/arangodb.git)

| **Category** | **Content** |
| --- | --- |
| Failure | Timeout |
| Similar Github Issue | None |


### 13 	Scylladb
[https://github.com/scylladb/scylladb.git](https://github.com/scylladb/scylladb.git)

| **Category** | **Content** |
| --- | --- |
| Failure | Timeout |
| Similar Github Issue | None |


### 14	Wav2letter
[https://github.com/flashlight/wav2letter.git](https://github.com/flashlight/wav2letter.git)

| **Category** | **Content** |
| --- | --- |
| Failure | Timeout |
| Similar Github Issue | None |


