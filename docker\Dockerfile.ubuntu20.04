# Dockerfile for Ubuntu 20.04 - 默认首选版本
FROM ubuntu:20.04

ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1

# 安装完整C/C++开发环境
RUN apt-get update && apt-get install -y \
    # 编译工具链 (GCC 9.x-11.x)
    build-essential gcc-9 g++-9 gcc-10 g++-10 gcc-11 g++-11 clang \
    make cmake autoconf automake libtool \
    pkg-config ninja-build meson \
    # SSH和系统工具
    openssh-server tree vim git \
    # 构建依赖库
    zlib1g-dev libssl-dev libcurl4-openssl-dev \
    libncurses5-dev libreadline-dev libffi-dev \
    libbz2-dev libgdbm-dev libexpat1-dev \
    # 脚本语言
    python3 python3-pip python3-dev perl ruby \
    # 网络和下载工具
    wget curl unzip tar proxychains4 \
    # 汇编和调试工具
    yasm nasm gdb valgrind \
    # 文档和帮助工具
    texinfo help2man m4 bison flex patch \
    # 额外的构建工具
    libtool-bin gettext intltool \
    && rm -rf /var/lib/apt/lists/*

# 设置默认GCC版本为10
RUN update-alternatives --install /usr/bin/gcc gcc /usr/bin/gcc-10 100 \
                        --slave /usr/bin/g++ g++ /usr/bin/g++-10 && \
    update-alternatives --install /usr/bin/gcc gcc /usr/bin/gcc-9 90 \
                        --slave /usr/bin/g++ g++ /usr/bin/g++-9 && \
    update-alternatives --install /usr/bin/gcc gcc /usr/bin/gcc-11 110 \
                        --slave /usr/bin/g++ g++ /usr/bin/g++-11

# SSH配置
RUN mkdir -p /var/run/sshd && \
    echo 'root:root' | chpasswd && \
    sed -i 's/#PermitRootLogin prohibit-password/PermitRootLogin yes/' /etc/ssh/sshd_config && \
    sed -i 's/#PasswordAuthentication yes/PasswordAuthentication yes/' /etc/ssh/sshd_config && \
    ssh-keygen -A

# Git全局配置
RUN git config --global user.email "<EMAIL>" && \
    git config --global user.name "AutoCompile" && \
    git config --global init.defaultBranch main && \
    git config --global safe.directory '*'

# Python基础包和ccscanner
RUN python3 -m pip install --no-cache-dir setuptools wheel ccscanner

# 代理配置
RUN echo "strict_chain\nproxy_dns\nremote_dns_subnet 224\ntcp_read_time_out 15000\ntcp_connect_time_out 8000\n[ProxyList]\nsocks5 127.0.0.1 1080" > /etc/proxychains4.conf

# 复制编译器wrapper脚本（用于IDA Pro分析的符号保护）
COPY wrapper_compiler.sh /
COPY wrapper_strip.sh /
RUN chmod +x /wrapper_compiler.sh /wrapper_strip.sh

# 设置编译器wrapper（保护符号信息用于IDA Pro分析）
RUN mv /usr/bin/gcc /usr/bin/gcc-real && ln -s /wrapper_compiler.sh /usr/bin/gcc
RUN mv /usr/bin/g++ /usr/bin/g++-real && ln -s /wrapper_compiler.sh /usr/bin/g++
RUN mv /usr/bin/cc /usr/bin/cc-real && ln -s /wrapper_compiler.sh /usr/bin/cc
RUN mv /usr/bin/c++ /usr/bin/c++-real && ln -s /wrapper_compiler.sh /usr/bin/c++
RUN mv /usr/bin/strip /usr/bin/strip-real && ln -s /wrapper_strip.sh /usr/bin/strip

# 工作目录
WORKDIR /work
RUN echo "cd /work" >> ~/.bashrc

# 编译环境优化（为IDA Pro分析优化）
ENV MAKEFLAGS="-j$(nproc)"
ENV CC=gcc
ENV CXX=g++
# 默认启用wrapper，保留符号信息用于IDA Pro分析
ENV ENABLE_COMPILER_WRAPPER=True
ENV WRAPPER_OPTI="-g -gdwarf-4"
ENV PRESERVE_SYMBOLS_FOR_IDA=True

EXPOSE 22
CMD ["/bin/bash"]
